stages:
  - build
  - deploy-dev

variables:  
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""

api-membership:
  stage: build
  tags:
    - runner-velodiva-production-idc
  image: docker:26.0.1-dind
  services:
    - name: docker:26.0.1-dind
      alias: docker
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - docker pull $CI_REGISTRY_IMAGE/api-membership:latest || true
    - docker build
      --cache-from $CI_REGISTRY_IMAGE/api-membership:latest
      -f apps/api-membership/Dockerfile --pull
      -t $CI_REGISTRY_IMAGE/api-membership:latest .
    - docker push $CI_REGISTRY_IMAGE/api-membership:latest
  only:
    refs:
      - develop
    changes:
      - "apps/api-membership/**/*"
      - "**/*.json"
      - "libs/**/*"

deploy-dev:
  stage: deploy-dev
  tags:
    - runner-velodiva-production-idc
  image: alpine:3.19.1
  environment:
    name: develop
  before_script:
    - "which ssh-agent || ( apk update && apk add --no-cache openssh )"
    - mkdir -p ~/.ssh
    - echo "$SSH_KEY_DEV" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 700 ~/.ssh/id_rsa
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/id_rsa
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - ssh -o StrictHostKeyChecking=no ${SSH_USER_DEV}@${SERVER_DEV} -p ${SSH_PORT_SERVER_DEV} "export APP_IMAGE_STG=$CI_REGISTRY_IMAGE && export APP_IMAGE_STG_TAG=$TAG_IMAGE && docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY && cd ~/backend-crm-membership-velodiva && git pull && docker compose -f compose.dev.yml down && docker compose -f compose.dev.yml pull && docker compose -f compose.dev.yml up -d"
  only:
    - develop

