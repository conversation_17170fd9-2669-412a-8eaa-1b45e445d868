// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  // previewFeatures = ["omitApi"]
}

datasource db {
  provider = "cockroachdb"
  url      = env("DATABASE_URL")
}

enum UserType {
  internal
  commercial
  creator
  reseller
  lmkn
  lmk
  other
}

// active when user acativate by email
enum UserStatus {
  active
  inActive
}

enum PropertyStatus {
  active
  inActive
  suspend
  draft
}

enum OrderStatus {
  pending
  completed
  cancelled
}

enum GenderType {
  male
  female
  unknown
}

enum TimeType {
  hourly
  daily
  monthly
  weekly
  yearly
}

enum AddOnType {
  enterprise
  custom
}

enum PaymentStatus {
  unpaid
  paid
  waiting
  cancel
}

enum RoleType {
  internal
  lmkn
  lmk
}

// enum BusinessType {
//   Single
//   Multiple
// }

model User {
  id                         String         @id @default(cuid())
  username                   String?        @db.String(50)
  email                      String         @db.String(50)
  provider                   String         @default("local") @db.String(30)
  mobileNumber               String?        @db.String(20)
  password                   String?        @db.String(200)
  businessType               String?
  activationCode             String?        @db.String(50)
  resetPasswordCode          String?        @db.String(50)
  type                       UserType?
  verificationToken          String?        @db.String(256)
  resetPasswordCodeExpiresAt DateTime?
  status                     String         @default("inActive") @db.String(30)
  activationAt               DateTime?
  isActive                   Boolean        @default(false)
  createdAt                  DateTime?      @default(now())
  updatedAt                  DateTime       @updatedAt()
  refferalCode               String?        @db.String(10)
  parent                     User?          @relation("ParentChild", fields: [parentId], references: [id])
  parentId                   String?
  children                   User[]         @relation("ParentChild")
  refferedBy                 String?
  profile                    Profile?
  account                    AccountOnUser?
  reseller                   Reseller?
  role                       Role?          @relation(fields: [roleId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  roleId                     String?
  isAdmin                    Boolean        @default(false)
  zone                       String?
  session                    UserSession[]
  to                         Notification[] @relation("to")
  from                       Notification[] @relation("from")
  activities                 UserActivity[]
  hashRt                     String?        @db.String(150)
  failedLoginAttempts        Int            @default(0)
  lastFailedLoginAt          DateTime?
  properties                 UserProperty[]
  property                   Property?
  order                      Order[]
  licenseTemp                licenseTemp[]

  @@unique([email, mobileNumber])
  @@index([status, mobileNumber, hashRt])
  @@index([verificationToken])
}

model UserProperty {
  id         String    @id @default(cuid())
  user       User      @relation(fields: [userId], references: [id])
  userId     String
  property   Property  @relation(fields: [propertyId], references: [id])
  propertyId String
  isDefault  Boolean   @default(false)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt()
  deletedAt  DateTime?

  @@unique([userId, propertyId])
}

model Profile {
  id           String     @id @default(cuid())
  firstName    String?    @db.String(50)
  lastName     String?    @db.String(50)
  placeOfBirth String?
  dateOfBirth  DateTime?  @db.Date
  gender       GenderType @default(unknown)
  address      String?    @db.String(50)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt()
  user         User?      @relation(fields: [userId], references: [id])
  userId       String?    @unique
  media        Media?     @relation(fields: [mediaId], references: [id])
  mediaId      String?    @unique
}

model PropertyGroup {
  id          String    @id @default(cuid())
  name        String    @db.String(50)
  description String    @db.String(50)
  property    Property?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt()
}

model licenseTemp {
  id             String   @id @default(cuid())
  user           User     @relation(fields: [userId], references: [id])
  userId         String
  propertyTypeId String?
  licenseKey     String?
  requestId      String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt()
}

model Property {
  id                 String               @id @default(cuid())
  cid                String?              @db.String(100)
  companyName        String?              @db.String(100)
  brandName          String?              @db.String(100)
  flag               String?              @db.String(50)
  companyEmail       String?              @db.String(50)
  companyPhoneNumber String?              @db.String(20)
  npwp               String?              @db.String(20)
  address            String?
  billingAddress     String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt()
  configuration      Configuration?
  reseller           PropertyOnReseller[]
  status             PropertyStatus?
  propertyTypeId     String?
  postalId           String?
  requestId          String?
  propertyGroup      PropertyGroup?       @relation(fields: [propertyGroupId], references: [id])
  propertyGroupId    String?              @unique
  groupOwner         Boolean?             @default(false)
  order              Order[]
  role               Role[]
  account            AccountOnProperty?
  historyFrom        OrderHistory[]       @relation("historyFromProperty")
  historyTo          OrderHistory[]       @relation("historyToProperty")
  media              Media?               @relation(fields: [mediaId], references: [id])
  mediaId            String?              @unique
  users              UserProperty[]
  user               User?                @relation(fields: [userId], references: [id])
  userId             String?              @unique
  paymentHistory     PaymentHistory[]
  licenseKey         String?
  licenseType        String?              @db.String(50)
  contactPerson      ContactPerson[]
  // orderInvoice       OrderInvoice[]
  // packages           Package[]
  // activation         Activation[]

  @@index([cid, companyName, brandName])
}

model Order {
  id             String          @id @default(cuid())
  name           String?
  invoice        String?         @db.String(100)
  description    String?         @db.String(250)
  status         OrderStatus     @default(pending)
  details        OrderDetail[]
  totalPrice     Decimal         @default(0) @db.Decimal(15, 2)
  discount       Decimal?        @default(0) @db.Decimal(10, 2)
  cancelAt       DateTime?
  cancelReason   String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt()
  property       Property?       @relation(fields: [propertyId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  propertyId     String?
  tag            String? // subsciption or one-purchase
  payment        OrderPayment?
  custom         OrderCustom[]
  history        OrderHistory[]
  voucherCode    String?
  referralCode   String?
  paymentHistory PaymentHistory?
  expiredAt      BigInt?
  // orderInvoice  OrderInvoice[]
  // devices       OrderDevice[]
  // VoucherDetail VoucherDetail?
  User           User?           @relation(fields: [userId], references: [id])
  userId         String?
}

model OrderDetail {
  id                 String               @id @default(cuid())
  name               String?
  serial             String?              @db.String(100)
  order              Order?               @relation(fields: [orderId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  orderId            String?
  duration           String?
  price              Decimal              @default(0) @db.Decimal(15, 2)
  itemType           String?              @db.String(50)
  itemId             String?
  sku                String?
  qty                Int                  @default(1)
  totalPrice         Decimal              @default(0) @db.Decimal(15, 2)
  tax                Decimal?             @default(0) @db.Decimal(10, 2)
  taxItems           Json?                @db.JsonB
  discount           Decimal?             @default(0) @db.Decimal(10, 2)
  voucherId          String?
  referralId         String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt()
  orderPackageDetail OrderPackageDetail[]
  // orderLicenseDetail    OrderLicenseDetail[]
}

model OrderPackageDetail {
  id            String       @id @default(cuid())
  orderDetail   OrderDetail? @relation(fields: [orderDetailId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  orderDetailId String?
  qty           Int?         @default(1)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt()
  featureId     String?
}

model OrderPayment {
  id               String   @id @default(cuid())
  by               String?
  url              String?
  expiredPayment   BigInt?
  isPaid           Boolean  @default(false)
  paidAt           String?
  paymentId        String?
  status           String?
  paymentRequestId String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt()
  order            Order?   @relation(fields: [orderId], references: [id])
  orderId          String   @unique
}

model PaymentHistory {
  id          String    @id @default(cuid())
  description String?
  orderId     String    @unique
  order       Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  propertyId  String?
  property    Property? @relation(fields: [propertyId], references: [id], onDelete: SetNull)
  amount      Decimal   @default(0) @db.Decimal(10, 2)
  createdAt   DateTime  @default(now())
}

model Notification {
  id        String   @id @default(cuid())
  to        User?    @relation("to", fields: [toId], references: [id])
  toId      String?
  title     String?
  url       String?
  message   String?
  related   String?
  relatedId BigInt?
  type      String?
  tags      String[]
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
  from      User?    @relation("from", fields: [fromId], references: [id])
  fromId    String?
}

model Media {
  id        String    @id @default(cuid())
  path      String    @db.String
  fileName  String    @unique
  name      String?   @db.String
  caption   String?   @db.String
  encoding  String?   @db.String(50)
  mimeType  String?   @db.String(50)
  size      Int       @default(0)
  dimension String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt()
  profile   Profile?
  property  Property?
}

model Role {
  id         String             @id @default(cuid())
  name       String             @db.String(50)
  type       RoleType           @default(internal)
  property   Property           @relation(fields: [propertyId], references: [id])
  propertyId String
  createdAt  DateTime           @default(now())
  updatedAt  DateTime           @updatedAt()
  permission PermissionOnRole[]
  user       User[]
}

model PermissionOnRole {
  role         Role?       @relation(fields: [roleId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  roleId       String
  permission   Permission? @relation(fields: [permissionId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  permissionId String
  manage       Boolean     @default(true)
  read         Boolean     @default(true)
  create       Boolean     @default(true)
  update       Boolean     @default(true)
  delete       Boolean     @default(true)

  @@unique([roleId, permissionId])
}

model Permission {
  id        String             @id @default(cuid())
  module    String             @db.String(50)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt()
  role      PermissionOnRole[]
  type      RoleType           @default(internal)
}

model UserActivity {
  id          String   @id @default(cuid())
  modul       String   @db.String(50)
  event       String   @db.String(30)
  reference   String   @db.String(50)
  description String   @db.String()
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt()
  user        User?    @relation(fields: [userId], references: [id])
  userId      String?
}

model UserSession {
  id        String   @id @default(cuid())
  ip        String?
  media     String?
  hashAt    String?  @unique @db.String(150)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
  user      User?    @relation(fields: [userId], references: [id])
  userId    String?
}

model Setting {
  type   String @id
  option Json   @db.JsonB
}

model Reseller {
  id        String               @id @default(cuid())
  name      String?              @db.String(50)
  createdAt DateTime             @default(now())
  updatedAt DateTime             @updatedAt()
  property  PropertyOnReseller[]
  user      User?                @relation(fields: [userId], references: [id])
  userId    String               @unique

  @@index([name])
}

model PropertyOnReseller {
  id         String   @id @default(cuid())
  reseller   Reseller @relation(fields: [resellerId], references: [id])
  resellerId String
  property   Property @relation(fields: [propertyId], references: [id])
  propertyId String
}

model Configuration {
  id         String   @id @default(cuid())
  options    Json     @db.JsonB
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt()
  property   Property @relation(fields: [propertyId], references: [id])
  propertyId String   @unique
}

model Account {
  id            String             @id @default(cuid())
  username      String?
  password      String?
  clientId      String?
  clientSecret  String?
  refreshToken  String?
  description   String?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt()
  integrationId BigInt?
  property      AccountOnProperty?
  user          AccountOnUser?
}

model AccountOnProperty {
  id         String   @id @default(cuid())
  property   Property @relation(fields: [propertyId], references: [id])
  propertyId String   @unique
  account    Account? @relation(fields: [accountId], references: [id])
  accountId  String   @unique
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt()
}

model AccountOnUser {
  user      User?    @relation(fields: [userId], references: [id])
  userId    String   @unique
  account   Account? @relation(fields: [accountId], references: [id])
  accountId String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([userId, accountId])
}

model OrderCustom {
  id        String   @id @default(cuid())
  price     Decimal  @default(0) @db.Decimal(10, 2)
  qty       Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
  order     Order?   @relation(fields: [orderId], references: [id])
  orderId   String?
  featureId String?
}

//model OrderInvoice {
//  id              String          @id @default(cuid())
//  orderId         String?
//  order           Order?          @relation(fields: [orderId], references: [id])
//  property        Property?       @relation(fields: [propertyId], references: [id])
//  propertyId      String?
//  invoiceDate     DateTime?
//  dueDate         DateTime?
//  subTotal        Decimal?        @default(0) @db.Decimal(10, 2)
//  tax             Decimal?        @default(0) @db.Decimal(10, 2)
//  discount        Decimal?        @default(0) @db.Decimal(10, 2)
//  total           Decimal?        @default(0) @db.Decimal(10, 2)
//  paymentStatus   String?
//  createdAt       DateTime        @default(now())
//  updatedAt       DateTime        @updatedAt()
//  invoiceItem     InvoiceItem[]
//}

//model InvoiceItem {
//  id                     String           @id @default(cuid())
//  invoiceId              String?
//  orderInvoice           OrderInvoice?    @relation(fields: [invoiceId], references: [id])
//  productId              String?
//  description            String?
//  qty                    Int              @default(1)
//  unitPrice              Decimal?         @default(0) @db.Decimal(10, 2)
//  lineTotal              Decimal?         @default(0) @db.Decimal(10, 2)
//  createdAt              DateTime         @default(now())
//  updatedAt              DateTime         @updatedAt()
//}

model OrderHistory {
  id             String      @id @default(cuid())
  orderId        String // Reference to the original Order
  order          Order?      @relation(fields: [orderId], references: [id], onDelete: Cascade)
  fromPropertyId String? // Reference to the source Property
  fromProperty   Property?   @relation("historyFromProperty", fields: [fromPropertyId], references: [id], onDelete: SetNull)
  toPropertyId   String? // Reference to the destination Property
  toProperty     Property?   @relation("historyToProperty", fields: [toPropertyId], references: [id], onDelete: SetNull)
  status         OrderStatus // Order payment status at the time
  totalPrice     Decimal     @default(0) @db.Decimal(10, 2)
  discount       Decimal?    @default(0) @db.Decimal(10, 2)
  tag            String? // subscription or one-purchase
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt()
  snapshot       Json? // Capture the state of the order details or other important data
  event          String? // Event type: e.g., "OrderCreated", "StatusUpdated", etc.

  @@index([orderId, status, createdAt])
}

model ContactPerson {
  id            String   @id @default(cuid())
  firstName     String   @db.String(50)
  lastName      String   @db.String(50)
  email         String   @db.String(100)
  phone         String   @db.String(20)
  jobPositionId String
  property      Property @relation(fields: [propertyId], references: [id])
  propertyId    String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt()

  @@index([email, phone])
}
