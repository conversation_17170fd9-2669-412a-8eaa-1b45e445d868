version: '3.9'
services:
  membership:
    container_name: api-membership
    image: gitlab.vnt.co.id:5050/velodiva-1/backend-crm-membership-velodiva/api-membership:latest
    restart: on-failure
    environment:
      - APP_MEMBERSHIP_SERVER=https://api-membership-stg.velodiva.com
      - APP_MEMBERSHIP_PORT=8082
      - DATABASE_URL=*******************************************************/velodiva_membership_dev
      - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - MAIL_HOST=smtp.gmail.com
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=hhinxwjsrcjmcqlv
      - MAIL_FROM=<EMAIL>
      - MAIL_PORT=465
      - MAIL_SECURE=true
      - REDIS=redis://default:Jaguar123@mainredis:6379
      - GRPC_SERVICE_URL=api-commercial-stg.velodiva.com:50051
      - DEFAULT_IMAGE_AVATAR_URL=https://bucket.velodiva.com/public/default-thumbnail/64x64.svg
      - MINIO_ENDPOINT=bucket.velodiva.com
      - MINIO_PORT=443
      - MINIO_USE_SSL=true
      - MINIO_ACCESS_KEY=sw0aTB827hiw36xFkY7E
      - MINIO_SECRET_KEY=xssOVt9jupA35Ygluja82KkKmO3bZdC2yPEjysh3
      - MINIO_BUCKET_NAME=membership
      - STORAGE_PROVIDER=minio
      - FOLDER_NAME=upload
      - GOOGLE_CLIENT_ID=366185989904-nmu7bct6lkub057hgoalspggm10i7670.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - CALLBACK_URL=https://api-membership-stg.velodiva.com/v1/auth/callback
      - REDIS_HOST=redis-membership
      - REDIS_PORT=6379
      - REDIS_PASS=Jaguar123
      - GRPC_URL=0.0.0.0:50052
      - GRPC_PORT=50052
      - RMS_HOST=http://*************:8010/v1
      - RMS_CLIENT_ID=d9ZXPBdiMXweztOo9ffN
      - RMS_CLIENT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - DLP_HOST=http://***************:9001/v1
      - DLP_CLIENT_ID=CLIENT-26f27029b6af016e484189d4297d4119-1742104340212
      - DLP_CLIENT_SECRET=b37c143cf431460168ee5236fb470d1c0d391d63a28384d167d0ee1629729f90
      - CLIENT_REDIRECT_URL=https://dashboard-stg.velodiva.com
      - INTERNAL_GRPC_HOST=*************:50051
      - DLP_API_KEY=2T5YBkgd1dmqa46MSPBXd6aM7N0PU8jPr6Qy7j6LrUu77dX9rCuUHNQryEDBzmtB
    ports:
      - '8082:8082'
      - '50052:50052'
    depends_on:
      - mainredis
    volumes:
      - api-membership:/app/storage

  mainredis:
    container_name: redis-membership
    image: redis:7.2.1-alpine
    restart: unless-stopped
    command: >
          --requirepass Jaguar123
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"

volumes:
  api-membership:
    driver: local
    driver_opts:
      type: 'none'
      o: 'bind'
      device: '/srv/storage/api-membership'
