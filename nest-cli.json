{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api-membership/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/api-membership/tsconfig.app.json"}, "generateOptions": {"spec": false}, "monorepo": true, "root": "apps/api-membership", "projects": {"api-membership": {"type": "application", "root": "apps/api-membership", "entryFile": "main", "sourceRoot": "apps/api-membership/src", "compilerOptions": {"tsConfigPath": "apps/api-membership/tsconfig.app.json", "assets": ["mail/templates/**/*", "mail/images"], "watchAssets": true}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "core": {"type": "library", "root": "libs/core", "entryFile": "index", "sourceRoot": "libs/core/src", "compilerOptions": {"tsConfigPath": "libs/core/tsconfig.lib.json"}}, "proto-schema": {"type": "library", "root": "libs/proto-schema", "entryFile": "index", "sourceRoot": "libs/proto-schema/src", "compilerOptions": {"tsConfigPath": "libs/proto-schema/tsconfig.lib.json"}}}}