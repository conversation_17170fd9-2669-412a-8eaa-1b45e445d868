FROM node:20-slim AS development
RUN apt-get update && apt-get install -y ca-certificates wget openssl chromium fonts-liberation libappindicator3-1 libasound2 libatk-bridge2.0-0 libatk1.0-0 libcups2 libdbus-glib-1-2 libgdk-pixbuf2.0-0 libgtk-3-0 libnspr4-dev libnss3 libx11-xcb1 libxcomposite1 libxdamage1 libxi6 libxtst6
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN npm install -g pnpm@9.7.0
WORKDIR /app
COPY --chown=node:node . .

FROM development AS build
RUN wget https://github.com/tj/node-prune/releases/download/v1.0.1/node-prune_1.0.1_linux_amd64.tar.gz \
    && tar xvf node-prune_1.0.1_linux_amd64.tar.gz \
    && mv node-prune /usr/local/bin/ \
    && rm node-prune_1.0.1_linux_amd64.tar.gz
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN pnpm prisma generate
RUN pnpm run build api-membership
RUN node-prune /app/node_modules

FROM node:lts-slim AS production
RUN apt-get update && apt-get install -y ca-certificates wget openssl chromium fonts-liberation libappindicator3-1 libasound2 libatk-bridge2.0-0 libatk1.0-0 libcups2 libdbus-glib-1-2 libgdk-pixbuf2.0-0 libgtk-3-0 libnspr4-dev libnss3 libx11-xcb1 libxcomposite1 libxdamage1 libxi6 libxtst6
WORKDIR /app
COPY --chown=node:node --from=build /app/node_modules ./node_modules
COPY --chown=node:node --from=build /app/dist ./dist
COPY --chown=node:node --from=build /app/_proto _proto
COPY --chown=node:node --from=build /app/_proto ./_proto

EXPOSE 8080
EXPOSE 50052

CMD [ "node","dist/apps/api-membership/main.js" ]
