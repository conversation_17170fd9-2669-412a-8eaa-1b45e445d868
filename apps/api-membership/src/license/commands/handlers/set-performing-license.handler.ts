import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>CommandHandler } from '@nestjs/cqrs';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { SetPerformingLicenseCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(SetPerformingLicenseCommand)
export class SetPerformingLicenseHandler
  implements ICommandHandler<SetPerformingLicenseCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: SetPerformingLicenseCommand) {
    const { args } = command;
    try {
      await this.prisma.property.update({
        where: {
          id: args.id
        },
        data: {
          licenseKey: args.key,
          licenseType: args.type,
          requestId: args?.requestId
        }
      });
      return {
        code: status.OK,
        message: 'success set performing license'
      };
    } catch (err) {
      console.log(err);
      throw new RpcException({
        code : status.INTERNAL,
        message: 'fail set performing license'
      })
    }
  }
}
