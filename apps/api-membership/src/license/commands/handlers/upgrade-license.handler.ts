import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpgradeLicenseCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { LicenseGrpcService } from '../../../internal-client/license.service';
import { IntegrationDlpService } from '../../../integration/services';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { OrderType } from '../../../order/dto/create-order.dto';
import { Metadata, status } from '@grpc/grpc-js';
import { lastValueFrom } from 'rxjs';
import { invoiceGenerator } from '@app/common';
import { Prisma } from '@prisma/client';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';

@CommandHandler(UpgradeLicenseCommand)
export class UpgradeLicenseHandler
  implements ICommandHandler<UpgradeLicenseCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly licenseGrpcService: LicenseGrpcService,
    private readonly integrationService: IntegrationDlpService,
    private readonly postalGrpc: PostalGrpcService,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
  ) {}

  async execute(command: UpgradeLicenseCommand) {
    const { user, args } = command;

    let totalPrice: number = 0;
    let payload = {};
    let payloadProp = {};
    let withLicense = false;
    const orderItems: any[] = [];
    const now = new Date();

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        AND: [
          {
            user: {
              id: user.id,
            },
          },
          {
            property: {
              id: args.propertyId,
            },
          },
        ],
      },
      include: {
        property: {
          include: {
            configuration: true,
          },
        },
      },
    });

    const newReq = Array(32)
      .fill(0)
      .map(() => String.fromCharCode(65 + Math.floor(Math.random() * 26)))
      .join('');

    const meta = new Metadata();

    const postal = await lastValueFrom(
      this.postalGrpc.getPostal({ id: userProperty.property.postalId }, meta),
    );

    const categoryCode = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType({
        id: userProperty.property.configuration.options['industryPlan'],
      }),
    );

    const validateLic = await this.integrationService.validateLicense(
      args.licenseNumber,
    );

    console.log('validateLic', validateLic);

    if (!validateLic) {
      throw new BadRequestException({ key: ['key not found'] });
    }

    if (validateLic.status === 'expired') {
      throw new BadRequestException({ key: ['License is expired'] });
    }

    if (validateLic.periode !== now.getFullYear()) {
      throw new BadRequestException({
        key: ['Only accept current year license'],
      });
    }

    if (!userProperty && userProperty?.property) {
      throw new BadRequestException('order cannot be processed');
    }

    if (!userProperty.property.id) {
      throw new BadRequestException('property not registered');
    }

    const existingConfiguration = userProperty.property.configuration;

    if (
      !existingConfiguration ||
      typeof existingConfiguration.options !== 'object' ||
      existingConfiguration.options === null ||
      Array.isArray(existingConfiguration.options)
    ) {
      throw new NotFoundException(
        'Configuration not found or options is not a valid object structure for this property.',
      );
    }

    const existingOptions = existingConfiguration.options as Prisma.JsonObject;

    const existingCustomFields =
      typeof existingOptions.customfields === 'object' &&
      existingOptions.customfields !== null &&
      !Array.isArray(existingOptions.customfields)
        ? (existingOptions.customfields as Prisma.JsonObject)
        : {};

    let valueToStore: Prisma.JsonValue | null = null;
    const rawValueFromArgs = args.customFields?.value;

    if (typeof rawValueFromArgs === 'number') {
      if (
        existingCustomFields &&
        typeof existingCustomFields['value'] === 'number' &&
        rawValueFromArgs < existingCustomFields['value']
      ) {
        throw new BadRequestException(
          `value cannot be smaller than the existing value: ${existingCustomFields['value']}`,
        );
      }
      valueToStore = rawValueFromArgs;
    } else if (rawValueFromArgs !== undefined) {
      throw new BadRequestException(
        `Unsupported type for customFields.value: ${typeof rawValueFromArgs}. Expected number or Percentages-compatible object.`,
      );
    }

    const configurations = await this.prisma.configuration.findMany({
      select: {
        id: true,
        options: true,
      },
    });

    const configWithLicense = configurations.find((config) => {
      if (
        config.options &&
        typeof config.options === 'object' &&
        !Array.isArray(config.options)
      ) {
        const options = config.options as Prisma.JsonObject;
        return options.licenseNumber === args.licenseNumber;
      }
      return false;
    });

    console.log('config License', configWithLicense);

    if (configWithLicense) {
      throw new BadRequestException({
        licenseNumber: ['license number already used'],
      });
    }

    if (!args.licenseNumber) {
      const updatedOptions: Prisma.JsonObject = {
        ...existingOptions,
        licenseType: args.licenseType,
        customfields: {
          ...existingCustomFields,
          ...(valueToStore !== null && { value: valueToStore }),
        },
      };

      await this.prisma.configuration.update({
        where: {
          id: existingConfiguration.id,
        },
        data: {
          options: updatedOptions,
        },
      });
    } else {
      const updatedOptions: Prisma.JsonObject = {
        ...existingOptions,
        licenseType: args.licenseType,
        licenseNumber: args.licenseNumber,
      };

      await this.prisma.configuration.update({
        where: {
          id: existingConfiguration.id,
        },
        data: {
          options: updatedOptions,
        },
      });
    }

    const customFieldId = existingCustomFields?.id as string | undefined;
    const customFieldType = existingCustomFields?.type as string | undefined;

    if (args.licenseType === 'DLM') {
      if (customFieldId) {
        try {
          await this.integrationService.DetailCategoryById(customFieldId);

          withLicense = true;
        } catch (err) {
          console.log('[DLP] Error:', err);
          throw new BadRequestException('Failed to get customField Id');
        }
      }
    }

    let licensePrice: number = 0;
    let licenseTaxPrice: number = 0;
    let licenseBasePrice: number = 0;
    let managementFee: number = 0;

    const licenseTaxItems = [];

    if (withLicense && customFieldId) {
      try {
        if (
          customFieldType === 'ranges' ||
          customFieldType === 'lupsum' ||
          customFieldType === 'split' ||
          customFieldType === 'progressives'
        ) {
          if (typeof valueToStore !== 'number') {
            throw new BadRequestException('custom field value not number');
          }
          payload = {
            id: customFieldId,
            amount: valueToStore,
          };
        } else {
          throw new BadRequestException('unrecognized custom fields');
        }

        const resp = await this.integrationService.calculateLicense(payload);
        const id = userProperty?.property.configuration.options['categoryId'];

        const license = await lastValueFrom(
          this.licenseGrpcService.getLicense({ id }),
        );

        licensePrice = resp.nominal + license.price;
        licenseBasePrice = resp.nominal + license.price;
        managementFee = licensePrice * 0.2;

        if (license.taxLicense.length > 0) {
          const initialLicenseTaxPrice = licenseTaxPrice;
          for (const tax of license.taxLicense) {
            const start = new Date(tax.startDate);
            const end = new Date(tax.endDate);

            if (now >= start && now < end) {
              if (tax.type === 'increase') {
                licenseTaxPrice +=
                  (Number(tax.nominal.toFixed(0)) / 100) * licensePrice;
                licenseTaxItems.push({
                  name: tax.name,
                  nominal: tax.nominal,
                  type: tax.type,
                  value: Number(
                    (Number(tax.nominal.toFixed(0)) / 100) * licensePrice,
                  ).toFixed(0),
                });
              } else if (tax.type === 'reduction') {
                const reductionAmount =
                  (Number(tax.nominal.toFixed(0)) / 100) * managementFee;

                if (tax.name === 'PPh23') {
                  const difference =
                    initialLicenseTaxPrice -
                    (initialLicenseTaxPrice - reductionAmount);

                  licenseTaxItems.push({
                    name: tax.name,
                    nominal: tax.nominal,
                    type: tax.type,
                    value: difference,
                  });

                  licenseTaxPrice -= reductionAmount;
                } else {
                  licenseTaxItems.push({
                    name: tax.name,
                    nominal: tax.nominal,
                    type: tax.type,
                    value: Number(reductionAmount).toFixed(0),
                  });
                  licenseTaxPrice -= reductionAmount;
                }
              }
            }
          }
        }

        licensePrice += licenseTaxPrice;
        totalPrice += licensePrice;
        Object.assign(license, {
          itemType: OrderType.LICENSE,
          basePrice: licenseBasePrice,
          totalPrice: licensePrice,
          taxPrice: licenseTaxPrice,
          duration: 'yearly',
          taxItems: licenseTaxItems,
        });
        orderItems.push(license);
      } catch (err) {
        if (err.code === status.NOT_FOUND) {
          throw new NotFoundException('license not found');
        } else {
          throw new InternalServerErrorException('failed order');
        }
      }
    }

    if (args.licenseType === 'DLM') {
      const orderTransact = await this.prisma.$transaction(async (tr) => {
        const order = await tr.order.create({
          data: {
            name: orderItems.map((item) => item.name).join(' + '),
            status: 'pending',
            totalPrice: totalPrice,
            discount: 0,
            tag: 'new-subscription',
            propertyId: userProperty.property.id,
            userId: userProperty.userId,
            expiredAt: BigInt(Date.now() + 24 * 60 * 60 * 1000),
          },
        });

        for (const items of orderItems) {
          const pkg = await tr.orderDetail.create({
            data: {
              name: items.name,
              orderId: order.id,
              duration: items.duration,
              price: items.basePrice,
              sku: items?.sku?.code,
              itemType: items.itemType,
              itemId: items.id,
              qty: items.qty,
              totalPrice: items.totalPrice,
              tax: items.taxPrice,
              discount: 0,
              taxItems: items.taxItems,
            },
          });

          if (items?.features) {
            await tr.orderPackageDetail.createMany({
              data: items?.features.map((ft) => {
                return {
                  orderDetailId: pkg.id,
                  featureId: ft.featureId,
                  qty: Number(ft.qty),
                };
              }),
            });
          }
        }
        return order;
      });

      const orderInvoice = await invoiceGenerator(
        this.prisma,
        'order',
        {},
        'ORD',
      );
      await this.prisma.order.update({
        where: {
          id: orderTransact.id,
        },
        data: {
          invoice: orderInvoice,
        },
      });

      const order = await this.prisma.order.findFirst({
        where: {
          id: orderTransact.id,
        },
        include: {
          details: {
            include: {
              orderPackageDetail: true,
            },
          },
        },
      });

      return {
        message: 'successfully order',
        data: order,
      };
    } else {
      payloadProp = {
        customer: {
          companyName: userProperty.property.companyName,
          companyEmail: userProperty.property.companyEmail,
          brandName: userProperty.property.brandName,
          postalCode: postal.code,
          categoryCode: categoryCode.categoryCode,
          unit: Number(
            userProperty.property.configuration.options['customfields'][
              'value'
            ],
          ),
          companyPhoneNumber: userProperty.property.companyPhoneNumber,
          licenseCode: args.licenseNumber,
          requestId: newReq,
        },
      };

      await this.integrationService.insertProperty(payloadProp);

      await this.prisma.property.update({
        where: {
          id: args.propertyId,
        },
        data: {
          // licenseKey: args.licenseNumber,
          licenseType: args.licenseType,
        },
      });

      return 'Successfully upgrade license. Please wait for the confirmation from our team. Thank you for your patience.';
    }
  }
}
