import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { ValidatePerformingLicenseCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { LicenseGrpcService } from 'apps/api-membership/src/internal-client/license.service';
import { InternalServerErrorException } from '@nestjs/common';

@CommandHandler(ValidatePerformingLicenseCommand)
export class ValidatePerformingLicenseHandler
  implements ICommandHandler<ValidatePerformingLicenseCommand>
{
  constructor(
    private readonly prisma: PrismaService,
  ) {}

  async execute(command: ValidatePerformingLicenseCommand) {
    const { args } = command;
    try {
      return 'validate performing license success';
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException()
    }
  }
}
