import { SetPerformingLicenseHandler } from './set-performing-license.handler';
import { ValidatePerformingLicenseHandler } from './validate-performing-license.handler';
import { UpgradeLicenseHandler } from './upgrade-license.handler';
import { ValidateConventionalHandler } from './validate-conventional.handler';

export const LicenseCommandHandlers = [
  SetPerformingLicenseHandler,
  ValidatePerformingLicenseHandler,
  UpgradeLicenseHandler,
  ValidateConventionalHandler,
];
