import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { ValidateConventionalCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { IntegrationDlpService } from '../../../integration/services';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';

@CommandHandler(ValidateConventionalCommand)
export class ValidateConventionalHandler
  implements ICommandHandler<ValidateConventionalCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly integrationDlpService: IntegrationDlpService,
  ) {}
  async execute(command: ValidateConventionalCommand) {
    const { args } = command;
    try {
      const result = await this.integrationDlpService.validateTmpLicense(
        args.key,
      );
      if (!result) {
        throw new NotFoundException();
      }

      if (result.status !== 'available') {
        throw new UnprocessableEntityException();
      }
      console.log('[License] Successfully processed conventional license');
      return result;
    } catch (err) {
      console.error(
        '[License] Failed to process conventional license:',
        err?.message,
      );

      if (UnprocessableEntityException) {
        throw new UnprocessableEntityException({ key: ['key already used'] });
      }

      if (NotFoundException) {
        throw new BadRequestException({ key: ['key not found'] });
      }

      throw new HttpException(
        err?.message || 'Failed to process conventional license',
        err?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
