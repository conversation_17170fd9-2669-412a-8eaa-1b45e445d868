import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  NotFoundException,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CalculateCategoryDto } from './dto/calculate.dto';
import { IntegrationDlpService } from '../integration/services';
import { ApiBearerAuth, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { GetPropertySubResponse } from '@app/proto-schema/index.internal';
import { PropertyTypeGrpcService } from '../property-type/service/property-type.grpc.service';
import { Id } from '@app/proto-schema/index.common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { FilterLicenseDto } from './dto/filter-license.dto';
import { DetailLicenseQuery } from './queries';
import { ConventionalDto } from './dto/conventional.dto';
import { UpgradeLicenseCommand, ValidateConventionalCommand } from './commands';
import { UpgradeLicenseDto } from './dto/upgrade-license.dto';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { JwtGuard } from '../auth/guards/jwt.guard';

@Controller('license')
@ApiTags('license')
export class LicenseController {
  constructor(
    private integrationDlpService: IntegrationDlpService,
    private propertyTypeService: PropertyTypeGrpcService,
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Get('detail')
  @ApiQuery({ name: 'propertyId', required: true })
  async detailLicense(@Query() args: FilterLicenseDto) {
    console.log('[License] Handling detail license request', args);
    try {
      const result = await this.queryBus.execute(new DetailLicenseQuery(args));
      console.log('[License] Successfully retrieved license details');
      return result;
    } catch (err: any) {
      console.error('[License] Failed to fetch license details:', err?.message);
      throw new HttpException(
        err?.message || 'Failed to fetch license details',
        err?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('calculate')
  async calculate(@Body() args: CalculateCategoryDto) {
    console.log('[License] Handling license calculation request', args);
    try {
      const result = await this.integrationDlpService.calculateLicense(args);
      console.log('[License] Successfully calculated license');
      return result;
    } catch (err: any) {
      console.error(
        '[License] Failed to calculate license:',
        err?.response?.data || err.message,
      );
      if (err instanceof HttpException) {
        throw err;
      }
      throw new HttpException(
        err?.response?.data?.message || 'Failed to calculate license',
        err?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('conventional')
  async conventional(@Body() args: ConventionalDto) {
    console.log('[License] Handling conventional license request', args);
    return await this.commandBus.execute(new ValidateConventionalCommand(args));
  }

  @Post('upgrade')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  async upgradeLicense(
    @User() user: ICurrentUser,
    @Body() args: UpgradeLicenseDto,
  ) {
    return await this.commandBus.execute(new UpgradeLicenseCommand(user, args));
  }

  @Get('category')
  async listCategory() {
    console.log('[License] Handling list categories request');
    try {
      const result = await this.integrationDlpService.listCategory();
      console.log('[License] Successfully retrieved categories list');
      return result;
    } catch (err: any) {
      console.error(
        '[License] Failed to fetch categories:',
        err?.response?.data || err.message,
      );
      if (err instanceof HttpException) {
        throw err;
      }
      throw new HttpException(
        err?.response?.data?.message || 'Failed to fetch categories',
        err?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':code')
  @ApiParam({ name: 'code', description: 'category code' })
  async detailCategoryByCode(@Param('code') code: string) {
    console.log(`[License] Handling category detail request for code: ${code}`);
    try {
      const result =
        await this.integrationDlpService.DetailCategoryByCode(code);
      console.log(
        `[License] Successfully retrieved category details for code: ${code}`,
      );
      return result;
    } catch (err: any) {
      console.error(
        `[License] Failed to fetch category details for code ${code}:`,
        err?.response?.data || err.message,
      );
      if (err instanceof HttpException) {
        throw err;
      }
      throw new HttpException(
        err?.response?.data?.message || 'Failed to fetch category details',
        err?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('list/:propertyTypeId')
  @ApiParam({ name: 'propertyTypeId', description: 'parentId' })
  async getOnePropertySubLicense(
    @Param('propertyTypeId') id: string,
  ): Promise<GetPropertySubResponse> {
    console.log(
      `[License] Handling property sub-license request for ID: ${id}`,
    );
    const requestId: Id = { id };
    try {
      const result = await this.propertyTypeService
        .getPropertySubLicense(requestId)
        .toPromise();
      console.log(
        `[License] Successfully retrieved property sub-license for ID: ${id}`,
      );
      return result;
    } catch (err: any) {
      console.error(
        `[License] Failed to fetch property sub-license for ID ${id}:`,
        err.message,
      );
      if (err.status === 400) {
        throw new BadRequestException(err.message);
      } else if (err.status === 404) {
        throw new NotFoundException(err.message);
      }
      throw new HttpException(
        err.message || 'Internal Server Error',
        err.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
