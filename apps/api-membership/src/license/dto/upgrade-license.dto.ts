import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import {
  CustomFieldBody,
  LicenseType,
} from '../../property/dto/create-property.dto';
import { Type } from 'class-transformer';

export class UpgradeLicenseDto {
  @ApiProperty({ enum: LicenseType })
  @IsEnum(LicenseType)
  @IsNotEmpty()
  licenseType: LicenseType;

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((prop) => prop.licenseType === 'CONVENTIONAL')
  @IsNotEmpty({
    message: 'licenseNumber is required when licenseType is CONVENTIONAL.',
  })
  licenseNumber?: string;

  @ApiProperty()
  @IsNotEmpty()
  propertyId: string;

  @ApiPropertyOptional({ type: CustomFieldBody })
  @Type(() => CustomFieldBody)
  @IsOptional()
  @ValidateIf((prop) => prop.licenseType === 'DLM')
  @ValidateNested()
  @IsNotEmpty({ message: 'customFields is required when licenseType is DLM' })
  customFields?: CustomFieldBody;
}
