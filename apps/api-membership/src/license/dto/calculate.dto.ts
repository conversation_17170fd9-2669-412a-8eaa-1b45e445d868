import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { Type } from "class-transformer"
import { IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from "class-validator"

export class CalculatePercentageDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  percentageId: string

  @IsNumber()
  @ApiProperty()
  value: number
}

export class CalculateCategoryDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  id: string

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional()
  amount?: number

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CalculatePercentageDto)
  @ApiPropertyOptional({ type: [CalculatePercentageDto] })
  percentages?: CalculatePercentageDto[]
}
