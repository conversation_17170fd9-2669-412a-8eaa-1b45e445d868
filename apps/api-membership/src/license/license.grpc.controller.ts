import { Status } from "@app/proto-schema/index.common";
import { LICENSE_SERVICE_NAME, LicenseServiceController, SetLicenseRequest } from "@app/proto-schema/index.membership";
import { Metadata } from "@grpc/grpc-js";
import { Controller } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { SetPerformingLicenseCommand } from "./commands";
import { CommandBus } from "@nestjs/cqrs";

@Controller()
export class LicenseRpcController implements LicenseServiceController {
  constructor(
    private readonly commandBus: CommandBus,
  ) {}
  @GrpcMethod(LICENSE_SERVICE_NAME, 'setPerformingLicense')
  setPerformingLicense(request: SetLicenseRequest, metadata: Metadata, ...rest: any): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(new SetPerformingLicenseCommand(request));
  }
}