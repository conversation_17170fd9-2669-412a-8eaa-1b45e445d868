import { Module } from '@nestjs/common';
import { LicenseController } from './license.controller';
import { IntegrationModule } from '../integration/integration.module';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { PropertyTypeModule } from '../property-type/property-type.module';
import { CqrsModule } from '@nestjs/cqrs';
import { LicenseQueryHandlers } from './queries';
import { LicenseCommandHandlers } from './commands';
import { LicenseRpcController } from './license.grpc.controller';
import { PostalModule } from '../postal/postal.module';

@Module({
  imports: [
    CqrsModule,
    IntegrationModule,
    PropertyTypeModule,
    InternalClientModule,
    PostalModule,
  ],
  providers: [...LicenseQueryHandlers, ...LicenseCommandHandlers],
  controllers: [LicenseController, LicenseRpcController],
})
export class LicenseModule {}
