import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { DetailLicenseQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import {
  BadGatewayException,
  BadRequestException,
  HttpException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { IntegrationDlpService } from 'apps/api-membership/src/integration/services';
import { LicenseGrpcService } from 'apps/api-membership/src/internal-client/license.service';

@QueryHandler(DetailLicenseQuery)
export class DetailLicenseHandler implements IQueryHandler<DetailLicenseQuery> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly integrationService: IntegrationDlpService,
    private readonly licenseService: LicenseGrpcService,
  ) {}

  async execute(query: DetailLicenseQuery) {
    const { args } = query;

    let property;
    try {
      property = await this.prisma.property.findFirst({
        where: {
          id: args.propertyId,
        },
        include: {
          configuration: true,
        },
      });
    } catch (error) {
      console.error('[License] Database connection error:', error);
      throw new BadGatewayException(
        'Database connection error. Please try again later.',
      );
    }

    if (!property) {
      throw new NotFoundException('Property not found');
    }

    if (!property.configuration?.options) {
      throw new BadRequestException('Property configuration not found');
    }

    try {
      const id = property.configuration.options['categoryId'];
      if (!id) {
        throw new BadRequestException(
          'Category ID is missing in configuration',
        );
      }

      // Get base license first
      const license = await this.licenseService.getLicense({ id }).toPromise();
      if (!license) {
        throw new NotFoundException('License not found');
      }

      const licenseType = property.configuration.options['licenseType'];
      if (licenseType === 'DLM') {
        const customFields = property.configuration.options['customfields'];
        if (customFields?.id) {
          if (
            ['ranges', 'lupsum', 'split', 'progressives'].includes(
              customFields.type,
            )
          ) {
            const amount = Number(customFields.value);
            if (isNaN(amount) || amount <= 0) {
              throw new BadRequestException(
                'Invalid amount value. Must be a positive number',
              );
            }

            const payload = {
              id: customFields.id,
              amount,
            };
            try {
              const resp =
                await this.integrationService.calculateLicense(payload);
              console.log('[DLP] Received calculation response:', resp);
              license.price = license.price + resp.nominal;
            } catch (error) {
              // Extract the actual error message from DLP service
              const dlpError = error.response?.data?.message || error.message;
              throw new BadRequestException(dlpError);
            }
          } else {
            throw new BadRequestException('Unrecognized custom fields type');
          }
        }
      }

      return license;
    } catch (err) {
      console.error('[License] Detail license error:', err);
      if (err instanceof BadRequestException) {
        throw err;
      }
      if (err instanceof NotFoundException) {
        throw err;
      }
      throw new InternalServerErrorException(
        'Failed to process license details',
      );
    }
  }
}
