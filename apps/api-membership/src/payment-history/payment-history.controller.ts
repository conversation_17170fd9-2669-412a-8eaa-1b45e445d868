import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { JwtGuard } from "../auth/guards/jwt.guard";
import { CommandBus, QueryBus } from "@nestjs/cqrs";
import { FilterPaymentHistoryDto } from "./dto/filter-payment-history.dto";
import { GetPaymentHistoryQuery, GetPaymentHistoriesQuery } from "./queries";

@Controller('payment-history')
@ApiTags('Payment History')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class PaymentHistoryController {
    constructor(
        private queryBus: QueryBus,
        private commandBus: CommandBus,
    ) { }

    @Get(':id')
    @ApiOperation({ summary: 'get single payment history' })
    findOne(@Param('id') id: string) {
        return this.queryBus.execute(new GetPaymentHistoryQuery(id));
    }

    @Get()
    @ApiOperation({ summary: 'get many payment histories' })
    findAll(@Query() filter: FilterPaymentHistoryDto) {
        return this.queryBus.execute(new GetPaymentHistoriesQuery(filter))
    }
}