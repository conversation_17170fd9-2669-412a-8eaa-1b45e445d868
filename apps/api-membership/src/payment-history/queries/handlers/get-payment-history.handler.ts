import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetPaymentHistoryQuery } from '../impl';

@QueryHandler(GetPaymentHistoryQuery)
export class GetPaymentHistoryHandler
  implements IQueryHandler<GetPaymentHistoryQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPaymentHistoryQuery) {
    const { id } = query;

    // Fetch payment history with related order and property details
    const paymentHistory = await this.prisma.paymentHistory.findUnique({
      where: { id },
      include: {
        order: true,
        property: true,
      },
    });

    // Throw NotFoundException if payment history not found
    if (!paymentHistory) {
      throw new NotFoundException(`Payment History with ID ${id} not found`);
    }

    return paymentHistory;
  }
}
