import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetPaymentHistoriesQuery } from '../impl';
import { FilterPaymentHistoryDto } from '../../dto/filter-payment-history.dto';
import { Prisma } from '@prisma/client';
import { Pagination } from '@app/common';
import { BadRequestException } from '@nestjs/common';

@QueryHandler(GetPaymentHistoriesQuery)
export class GetPaymentHistoriesHandler
  implements IQueryHandler<GetPaymentHistoriesQuery>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetPaymentHistoriesQuery) {
    const {
      propertyId,
      startDate,
      endDate,
      page = 1,
      limit = 10,
      search,
      sort = 'createdAt',
      sortType = 'desc',
    } = query.args;

    // Validate propertyId is provided
    if (!propertyId) {
      throw new BadRequestException('PropertyId is required');
    }

    // Prepare dynamic filter conditions
    const whereConditions: Prisma.PaymentHistoryWhereInput = {
      propertyId, // Always include propertyId
      ...(startDate &&
        endDate && {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        }),
      ...(search && {
        OR: [
          { description: { contains: search, mode: 'insensitive' } },
          { order: { name: { contains: search, mode: 'insensitive' } } },
        ],
      }),
    };

    // Prepare order by conditions
    const orderByConditions: Prisma.PaymentHistoryOrderByWithRelationInput = {
      [sort]: sortType,
    };

    // Use Pagination utility for consistent pagination
    const paymentHistories = await Pagination<
      Prisma.PaymentHistoryGetPayload<{
        include: { order: true; property: true };
      }>,
      Prisma.PaymentHistoryFindManyArgs
    >(
      this.prisma.paymentHistory,
      {
        where: whereConditions,
        orderBy: orderByConditions,
        include: {
          order: { include: { details: true, payment: true } },
          property: true,
        },
      },
      { page, limit },
    );

    return paymentHistories;
  }
}
