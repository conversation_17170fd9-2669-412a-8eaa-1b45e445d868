import { BadRequestException } from '@nestjs/common';
import { <PERSON><PERSON>andler, ICommandHandler } from '@nestjs/cqrs';
import * as bcrypt from 'bcrypt';
import { UpdateUserPasswordCommand } from '../impl';
import { UserGrpcService } from '../../../internal-client/user.service';
import { firstValueFrom } from 'rxjs';
import { status } from '@grpc/grpc-js';

@CommandHandler(UpdateUserPasswordCommand)
export class UpdateUserPasswordHandler
  implements ICommandHandler<UpdateUserPasswordCommand> {
  constructor(
    private userService: UserGrpcService
  ) {}

  async execute(command: UpdateUserPasswordCommand) {
    const { id, args } = command;

    try {
      // Validate password match
      if (args.newPassword !== args.confirmPassword) {
        throw new BadRequestException('Passwords do not match');
      }

      // Validate password requirements
      if (!args.newPassword || args.newPassword.length < 8) {
        throw new BadRequestException('Password must be at least 8 characters long');
      }

      // Hash new password
      // const hashedPassword = bcrypt.hashSync(args.newPassword, 10);

      const result = await firstValueFrom(
        this.userService.updateUserPassword({
          userId: id,
          password: args.newPassword
        })
      );

      if (!result || result.code !== status.OK) {
        throw new BadRequestException(result?.message || 'Failed to update user password');
      }

      return 'Password updated successfully';
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to update password');
    }
  }
}
