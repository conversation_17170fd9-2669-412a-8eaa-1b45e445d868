import { BadRequestException } from '@nestjs/common';
import { <PERSON>Handler, ICommandHandler } from '@nestjs/cqrs';
import { UpdateUserCommand } from '../impl';
import { UserGrpcService } from '../../../internal-client/user.service';
import { firstValueFrom } from 'rxjs';
import { status } from '@grpc/grpc-js';

@CommandHandler(UpdateUserCommand)
export class UpdateUserHandler implements ICommandHandler<UpdateUserCommand> {
  constructor(
    private userService: UserGrpcService,
  ) { }

  async execute(command: UpdateUserCommand) {
    const { id, args } = command;

    try {
      const result = await firstValueFrom(
        this.userService.updateUser({
          userId: id,
          firstName: args.firstName,
          lastName: args.lastName,
          email: args.email,
          mobileNumber: args.mobileNumber,
          gender: args.gender,
          dateOfBirth: args.dateOfBirth,
          address: args.address,
          activationId: args.activationId,
          zoneName: args.zoneName
        })
      );

      if (!result || result.code !== status.OK) {
        throw new BadRequestException(result?.message || 'Failed to update commercial user');
      }

      return 'Successfully updated user';
    } catch (error) {
      throw new BadRequestException('Failed to update user');
    }
  }
}
