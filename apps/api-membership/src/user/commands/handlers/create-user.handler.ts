import { generateRandomString, generateUsername, Tracer } from '@app/common';
import { Command<PERSON>andler, ICommandHandler } from '@nestjs/cqrs';
import * as bcrypt from 'bcrypt';
import { PrismaService } from 'nestjs-prisma';
import { CreateUserCommand } from '../impl';
import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Metadata, status } from '@grpc/grpc-js';
import { PackageService } from 'apps/api-membership/src/internal-client/services';
import { UserGrpcService } from 'apps/api-membership/src/internal-client/user.service';
import { firstValueFrom } from 'rxjs';
import { BusinessType } from 'apps/api-membership/src/auth/dto/register.dto';

interface IUserCreationData {
  isAdmin: boolean;
  businessType: string;
  commercialOnly?: boolean;
}

interface ParentUser {
  id: string;
  isActive: boolean;
  isAdmin: boolean;
  businessType: string;
  parentId: string | null;
  createCommercialOnly?: boolean;
  createAsOperator?: boolean;
}


@CommandHandler(CreateUserCommand)
export class CreateUserHandler implements ICommandHandler<CreateUserCommand> {
  constructor(
    private prisma: PrismaService,
    private packageService: PackageService,
    private userService: UserGrpcService,
  ) {}

  @Tracer
  async execute(command: CreateUserCommand) {
    const { currentUser, args } = command;
    const parentUser = await this.getParentUser(currentUser.id);
    
    if (!parentUser.isActive) {
      throw new BadRequestException('Parent user is inactive');
    }

    await this.validateUserData(parentUser, args);

    const userCreationData = await this.determineUserCreationStrategy(parentUser, args.propertyId);

    if (userCreationData.type === 'COMMERCIAL_ADMIN') {
      const res = await this.registerOperatorUser(args, {
        isAdmin: true,
        businessType: BusinessType.SINGLE
      });

      return res;
    } else if (userCreationData.type === 'REGULAR_ADMIN') {
      const user = await this.createUser(args, currentUser.id, {
        isAdmin: true,
        businessType: BusinessType.SINGLE
      });
      user.password = args.password;
      const res = await this.registerUserCommercial(user, args.zoneName, args.activationId);
      return res;
    } else {
      const res = await this.registerOperatorUser(args, {
        isAdmin: false,
        businessType: BusinessType.SINGLE
      });
      return res;
    }

    return 'Successfully created user';
  }

  @Tracer
  private async determineUserCreationStrategy(parentUser: ParentUser, propertyId: string): Promise<{
    type: 'COMMERCIAL_ADMIN' | 'REGULAR_ADMIN' | 'OPERATOR';
    message: string;
  }> {
    if (parentUser.businessType === BusinessType.MULTIPLE) {
      const existingAdmin = await this.prisma.user.findFirst({
        where: {
          isAdmin: true,
          businessType: BusinessType.SINGLE,
          properties: { some: { propertyId } },
        },
      });
      return {
        type: existingAdmin ? 'OPERATOR' : 'REGULAR_ADMIN',
        message: existingAdmin ? 'Creating operator under multiple business' : 'Creating admin under multiple business'
      };
    }

    if (parentUser.businessType === BusinessType.SINGLE) {
      if (!parentUser.isAdmin) {
        throw new BadRequestException('You do not have permission to create users');
      }

      await this.validateUserLimit(parentUser, propertyId);

      const hasCommercialPackage = await this.checkCommercialPackage(propertyId);
      if (hasCommercialPackage && parentUser.parentId === null) {
        return {
          type: 'COMMERCIAL_ADMIN',
          message: 'Creating commercial-only admin user'
        };
      }

      return {
        type: 'OPERATOR',
        message: 'Creating operator under single business'
      };
    }

    throw new BadRequestException('Invalid business type configuration');
  }

  @Tracer
  private async registerOperatorUser(
    args: CreateUserCommand['args'],
    userData: IUserCreationData,
  ) {
    // const hashedPassword = bcrypt.hashSync(args.password, 10);

    try {
      const baseProperty = await this.prisma.property.findFirst({
        where: {
          id: args.propertyId
        },
        select: {
          id: true,
          cid: true
        }
      });
      const propertyUsers = await firstValueFrom(
        this.userService.getUsersByPropertyId({
          propertyId: [baseProperty.id]
        }),
      );

      let username = `${baseProperty.cid}${String(propertyUsers.users.length+1).padStart(6, '0')}`;
      if (propertyUsers.users.length > 0) {
        const existing = propertyUsers.users[propertyUsers.users.length-1].username;
        const numericPart = existing.slice(-6);
        if (numericPart === 'NaN') {
          username = `${baseProperty.cid}${String(Math.floor(Math.random() * 1000)).padStart(6, '0')}`;
        } else {
          username = `${baseProperty.cid}${String(Number(numericPart) + 1).padStart(6, '0')}`;
        }
      }
      
      const pass = args.password ? args.password : generateRandomString(16);

      const result = await firstValueFrom(
        this.userService.createUser({
          // username: generateUsername(args.firstName, '', 4) || '',
          username: username,
          email: args.email,
          mobileNumber: args.mobileNumber || '',
          // password: args.password || '',
          password: pass || '',
          businessType: userData.businessType || '',
          isAdmin: userData.isAdmin || false,
          firstName: args.firstName || '',
          lastName: args.lastName || '',
          placeOfBirth: '',
          dateOfBirth: args.dateOfBirth || '',
          gender: args.gender || '',
          address: args.address || '',
          activationId: args.activationId || '',
          propertyId: args.propertyId || '',
          zoneName: args.zoneName || '',
        }),
      );

      if (!result) {
        throw new BadRequestException('Empty response from commercial service');
      }

      return {
        username: username,
        password: pass,
        isAdmin: userData.isAdmin
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Tracer
  private async registerUserCommercial(user: any, zoneName: string, activationId: string) {
    try {
      const baseProperty = await this.prisma.property.findFirst({
        where: {
          id: user.properties?.[0]?.propertyId
        },
        select: {
          id: true,
          cid: true
        }
      });
      const propertyUsers = await firstValueFrom(
        this.userService.getUsersByPropertyId({
          propertyId: [baseProperty.id]
        }),
      );

      let username = `${baseProperty.cid}${String(propertyUsers.users.length+1).padStart(6, '0')}`;
      if (propertyUsers.users.length > 0) {
        const existing = propertyUsers.users[propertyUsers.users.length-1].username;
        const numericPart = existing.slice(-6);
        if (numericPart === 'NaN') {
          username = `${baseProperty.cid}${String(Math.floor(Math.random() * 1000)).padStart(6, '0')}`;
        } else {
          username = `${baseProperty.cid}${String(Number(numericPart) + 1).padStart(6, '0')}`;
        }
      }

      const pass = user.password ? user.password : generateRandomString(16);
      const result = await firstValueFrom(
        this.userService.createUser({
          // username: user.username || '',
          username: username,
          email: user.email,
          mobileNumber: user.mobileNumber || '',
          // password: user.password || '',
          password: pass || '',
          businessType: user.businessType || '',
          isAdmin: user.isAdmin || false,
          firstName: user.profile?.firstName || '',
          lastName: user.profile?.lastName || '',
          placeOfBirth: user.profile?.placeOfBirth || '',
          dateOfBirth: user.profile?.dateOfBirth?.toISOString() || '',
          gender: user.profile?.gender || '',
          address: user.profile?.address || '',
          activationId: activationId || '',
          propertyId: user.properties?.[0]?.propertyId || '',
          zoneName: zoneName || '',
        }),
      );

      if (!result) {
        throw new BadRequestException('Empty response from commercial service');
      }

      return {
        username: username,
        password: pass,
        isAdmin: user.isAdmin
      };
    } catch (error) {
      await this.cleanupFailedRegistration(user.id);
      throw new InternalServerErrorException(error.message);
    }
  }

  @Tracer
  private async checkCommercialPackage(propertyId: string): Promise<boolean> {
    const meta = new Metadata();
    try {
      const response = await this.packageService.client
        .listPackage({ query: JSON.stringify({ propertyId }) }, meta)
        .toPromise();

      if (!response || response.status?.code !== status.OK) {
        return false;
      }

      const packageList = response.data || [];
      return packageList.some(
        (pkg) =>
          pkg.isActive &&
          pkg.status === 'active' &&
          pkg.packageFeature?.some((feature) => feature.id === 'f-001'),
      );
    } catch (error) {
      return false;
    }
  }

  @Tracer
  private async validateUserLimit(parentUser: any, propertyId: string) {
    const { userLimit } =
      await this.validatePackagesAndGetUserLimit(propertyId);

    const currentUserCount = await this.prisma.user.count({
      where: {
        parentId: parentUser.id,
        isActive: true,
        properties: {
          some: { propertyId },
        },
      },
    });

    if (currentUserCount >= userLimit) {
      throw new BadRequestException(
        `User limit reached. Maximum allowed users: ${userLimit}`,
      );
    }
  }

  @Tracer
  private async validatePackagesAndGetUserLimit(
    propertyId: string,
  ): Promise<{ userLimit: number }> {
    const meta = new Metadata();

    try {
      const response = await this.packageService.client
        .listPackage({ query: JSON.stringify({ propertyId }) }, meta)
        .toPromise();

      if (!response || response.status?.code !== status.OK) {
        throw new BadRequestException(
          response?.status?.message || 'Failed to fetch packages',
        );
      }

      const packageList = response.data || [];

      if (!Array.isArray(packageList) || packageList.length === 0) {
        throw new BadRequestException(
          'No active packages found for this property',
        );
      }

      const activePackage = packageList.find(
        (pkg) =>
          pkg.isActive &&
          pkg.status === 'active' &&
          pkg.packageFeature?.some((feature) => feature.id === 'f-002'),
      );

      if (!activePackage) {
        throw new BadRequestException(
          'No active package with user limit feature found',
        );
      }

      const userLimitFeature = activePackage.packageFeature.find(
        (feature) => feature.id === 'f-002',
      );
      return { userLimit: userLimitFeature?.qty || 0 };
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(
        `Failed to validate packages: ${error.message}`,
      );
    }
  }

  @Tracer
  private async createUser(
    args: CreateUserCommand['args'],
    parentId: string,
    userData: IUserCreationData,
  ) {
    const hashedPassword = bcrypt.hashSync(args.password, 10);

    return this.prisma.user.create({
      data: {
        email: args.email,
        type: 'commercial',
        username: generateUsername(args.firstName, '', 4),
        provider: 'membership',
        mobileNumber: args.mobileNumber,
        password: hashedPassword,
        status: 'active',
        isActive: true,
        activationAt: new Date(),
        parentId,
        businessType: userData.businessType,
        isAdmin: userData.isAdmin,
        profile: {
          create: {
            firstName: args.firstName,
            lastName: args.lastName,
            gender: args.gender,
            dateOfBirth: args.dateOfBirth
              ? new Date(args.dateOfBirth).toISOString()
              : null,
            address: args.address,
          },
        },
        properties: {
          create: {
            propertyId: args.propertyId,
            isDefault: true,
          },
        },
      },
      include: {
        profile: true,
        properties: true,
      },
    });
  }

  @Tracer
  private async validateUserData(parentUser: ParentUser, args: CreateUserCommand['args']) {
    if (parentUser.businessType === BusinessType.MULTIPLE) {
      const existingAdmin = await this.prisma.user.findFirst({
        where: {
          isAdmin: true,
          businessType: BusinessType.SINGLE,
          properties: { some: { propertyId: args.propertyId } },
        },
      });

      if (!existingAdmin) {
        if (!args.email) {
          throw new BadRequestException("email is required!");
        }

        if (!args.password && !args.passwordConfirmation) {
          throw new BadRequestException("password is required!");
        }

        if (!args.passwordConfirmation) {
          throw new BadRequestException("passwordConvirmation is required!");
        }

        if (args.password !== args.passwordConfirmation) {
          throw new BadRequestException("Password doesn't match!");
        }
    
        await this.validateUniqueFields(args.email);
      }
    }

    this.validateDateOfBirth(args.dateOfBirth);
  }

  @Tracer
  private async validateUniqueFields(email: string) {
    const [existingEmail] = await Promise.all([
      this.prisma.user.findFirst({ where: { email } }),
    ]);

    if (existingEmail) throw new BadRequestException('Email already exists');
  }

  @Tracer
  private validateDateOfBirth(dateOfBirth?: string) {
    if (dateOfBirth && new Date(dateOfBirth) > new Date()) {
      throw new BadRequestException('Date of birth cannot be in the future');
    }
  }

  @Tracer
  private async getParentUser(userId: string) {
    return this.prisma.user.findUniqueOrThrow({
      where: { id: userId },
    });
  }

  @Tracer
  private async cleanupFailedRegistration(userId: string) {
    try {
      await this.prisma.$transaction([
        this.prisma.profile.deleteMany({ where: { userId } }),
        this.prisma.userProperty.deleteMany({ where: { userId } }),
        this.prisma.user.delete({ where: { id: userId } }),
      ]);
    } catch (error) { }
  }
}
