import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { DeleteUserCommand } from '../impl/delete-user.command';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { UserGrpcService } from 'apps/api-membership/src/internal-client/user.service';
import { firstValueFrom } from 'rxjs';
import { status } from '@grpc/grpc-js';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(DeleteUserCommand)
export class DeleteUserHandler implements ICommandHandler<DeleteUserCommand> {
  constructor(
    private userService: UserGrpcService,
    private prisma: PrismaService,
  ) {}

  async execute(command: DeleteUserCommand) {
    const { id, currentUser } = command;

    try {
      const userResponse = await firstValueFrom(
        this.userService.getUserById({ id }),
      );

      if (!userResponse || !userResponse.user) {
        throw new NotFoundException('User not found in commercial service');
      }

      if (userResponse.user.isAdmin) {
        throw new BadRequestException('Primary user cannot be deleted');
      }

      // if (userResponse.user.isAdmin) {
      //   const internalUser = await this.prisma.user.findFirst({
      //     where: {
      //       parentId: currentUser.id,
      //       isAdmin: true,
      //     },
      //     include: {
      //       profile: true,
      //       properties: true,
      //     },
      //   });
      //
      //   if (internalUser && internalUser.parentId) {
      //     await this.prisma.$transaction([
      //       this.prisma.profile.deleteMany({
      //         where: { userId: internalUser.id },
      //       }),
      //       this.prisma.userProperty.deleteMany({
      //         where: { userId: internalUser.id },
      //       }),
      //       this.prisma.user.delete({ where: { id: internalUser.id } }),
      //     ]);
      //   }
      // }

      const result = await firstValueFrom(this.userService.deleteUser({ id }));

      if (!result) {
        throw new BadRequestException('Empty response from commercial service');
      }

      if (result.code === status.NOT_FOUND) {
        throw new NotFoundException(result.message || 'User not found');
      }
      if (result.code !== status.OK) {
        throw new BadRequestException(
          result.message || 'Commercial service returned non-OK status',
        );
      }

      return 'Successfully deleted user';
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      if (error.code === status.UNAVAILABLE) {
        throw new InternalServerErrorException(
          'Commercial service temporarily unavailable',
        );
      }

      console.error('[DeleteUser] Error:', error);
      throw new InternalServerErrorException(
        'Failed to delete user. Please try again later.',
      );
    }
  }
}
