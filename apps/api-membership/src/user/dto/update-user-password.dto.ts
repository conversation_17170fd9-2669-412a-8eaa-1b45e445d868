import { ApiProperty } from '@nestjs/swagger';
import { Match } from 'libs/validator/src/match/match';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class UpdateUserPasswordDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  @ApiProperty()
  newPassword: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  @Match('newPassword', { message: 'Passwords do not match' })
  @ApiProperty()
  confirmPassword: string;
}
