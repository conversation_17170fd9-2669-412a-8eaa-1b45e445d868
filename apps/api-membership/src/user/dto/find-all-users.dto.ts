import { BaseFilterDto } from '@app/common';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { UserType } from '@prisma/client';

export enum UserStatusFilter {
  ACTIVE = 'active',
  INACTIVE = 'inActive',
}

export class FindAllUsersDto extends PartialType(BaseFilterDto) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  propertyId?: string;

  @ApiPropertyOptional({ description: 'Filter by business unit/type' })
  @IsOptional()
  @IsString()
  businessUnit?: string;

  @ApiPropertyOptional({
    enum: UserType,
    description: 'Filter by user type'
  })
  @IsOptional()
  @IsEnum(UserType)
  type?: UserType;

  @ApiPropertyOptional({
    enum: UserStatusFilter,
    description: 'Filter by user status'
  })
  @IsOptional()
  @IsEnum(UserStatusFilter)
  status?: UserStatusFilter;

  @ApiPropertyOptional({ description: 'Search by username' })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({ description: 'Search by email' })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiPropertyOptional({ description: 'Search by zone name' })
  @IsOptional()
  @IsString()
  zoneName?: string;

  @ApiPropertyOptional({ description: 'Search by user name (first + last name)' })
  @IsOptional()
  @IsString()
  userName?: string;
}
