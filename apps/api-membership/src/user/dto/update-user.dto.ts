import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsMobilePhone,
  IsOptional,
  IsString,
  MaxLength,
  ValidateIf,
} from 'class-validator';
import { $Enums } from '@prisma/client';
import { DateFormat } from 'libs/validator/src/date-format/date-format';
import { IsValidEmail } from '../../../../../libs/validator/src/is-valid-email/is-valid-email';

export class UpdateUserDto {
  @ApiProperty()
  @IsString()
  @MaxLength(50)
  @IsOptional()
  firstName?: string;

  @ApiProperty()
  @IsString()
  @MaxLength(50)
  @IsOptional()
  lastName?: string;

  @ApiProperty()
  @IsEmail()
  @IsValidEmail()
  @IsOptional()
  email?: string;

  @ApiProperty()
  @IsMobilePhone('id-ID')
  mobileNumber: string;

  @ApiProperty({ enum: $Enums.GenderType })
  @IsEnum($Enums.GenderType)
  gender: $Enums.GenderType;

  @ApiProperty()
  @IsOptional()
  @ValidateIf((prop) => prop.dateOfBirth != '')
  @DateFormat('YYYY-MM-DD')
  dateOfBirth?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @MaxLength(250)
  address?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  activationId?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @MaxLength(50)
  zoneName?: string;
}
