import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { DateFormat } from 'libs/validator/src/date-format/date-format';
import { IsUnique } from 'libs/validator/src/is-unique/is-unique';
import {
  IsEmail,
  IsEnum,
  IsMobilePhone,
  IsOptional,
  IsString,
  MaxLength,
  ValidateIf,
} from 'class-validator';
import { Match } from 'libs/validator/src/match/match';
import { IsValidEmail } from '../../../../../libs/validator/src/is-valid-email/is-valid-email';

export class CreateUserDto {
  @ApiProperty()
  @IsString()
  propertyId: string;

  @ApiProperty()
  @IsString()
  @MaxLength(50)
  firstName: string;

  @ApiProperty()
  @IsString()
  @MaxLength(50)
  lastName: string;

  @ApiPropertyOptional()
  @IsEmail()
  @IsValidEmail()
  @IsUnique({ model: 'user' })
  @IsOptional()
  email: string;

  @ApiProperty()
  @IsMobilePhone('id-ID')
  @IsOptional()
  // @IsUnique({ model: 'user' })
  mobileNumber?: string;

  @ApiProperty({ enum: $Enums.GenderType })
  @IsEnum($Enums.GenderType)
  gender: $Enums.GenderType;

  @ApiPropertyOptional()
  // @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  @IsOptional()
  password: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @MaxLength(50)
  zoneName?: string;

  @ApiPropertyOptional()
  // @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  @Match('password', { message: 'Passwords do not match' })
  @IsOptional()
  passwordConfirmation: string;

  @ApiProperty()
  @IsOptional()
  @ValidateIf((prop) => prop.dateOfBirth != '')
  @DateFormat('YYYY-MM-DD')
  dateOfBirth?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @MaxLength(250)
  address?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  activationId?: string;
}
