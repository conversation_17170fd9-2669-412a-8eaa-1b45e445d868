import { Permission } from '@app/common';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Permissions } from '../auth/decorator/permissions.decorator';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/types/user.type';
import {
  CreateUserCommand,
  UpdateUserCommand,
  UpdateUserPasswordCommand,
} from './commands';
import { UpdateUserPasswordDto } from './dto/update-user-password.dto';
import { GetUserQuery, GetUsersQuery } from './queries';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { DeleteUserCommand } from './commands/impl/delete-user.command';
import { FindAllUsersDto } from './dto/find-all-users.dto';

@Controller({ version: '1', path: 'user' })
@ApiTags('User')
@ApiBearerAuth()
@UseGuards(JwtGuard) // Remove RolesGuard from here
export class UserController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Post()
  @Permissions([Permission.USER_MANAGE, Permission.USER_CREATE])
  create(
    @User() currentUser: ICurrentUser,
    @Body() createUserDto: CreateUserDto,
  ) {
    return this.commandBus.execute(
      new CreateUserCommand(currentUser, createUserDto),
    );
  }

  @Get()
  @Permissions([Permission.USER_MANAGE, Permission.USER_READ])
  findAll(@Query() args: FindAllUsersDto, @User() user: ICurrentUser) {
    return this.queryBus.execute(new GetUsersQuery(args, user));
  }

  @Get(':id')
  @Permissions([Permission.USER_MANAGE, Permission.USER_READ])
  findOne(@Param('id') id: string) {
    return this.queryBus.execute(new GetUserQuery(id));
  }

  @Patch(':id')
  @Permissions([Permission.USER_MANAGE, Permission.USER_UPDATE])
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.commandBus.execute(new UpdateUserCommand(id, updateUserDto));
  }

  @Patch(':id/password')
  @Permissions([Permission.USER_MANAGE, Permission.USER_UPDATE])
  updatePassword(
    @Param('id') id: string,
    @Body() updatePasswordDto: UpdateUserPasswordDto,
  ) {
    return this.commandBus.execute(
      new UpdateUserPasswordCommand(id, updatePasswordDto),
    );
  }

  @Delete(':id')
  @Permissions([Permission.USER_MANAGE, Permission.USER_DELETE])
  deleteOperator(@User() currentUser: ICurrentUser, @Param('id') id: string) {
    return this.commandBus.execute(new DeleteUserCommand(id, currentUser));
  }
}
