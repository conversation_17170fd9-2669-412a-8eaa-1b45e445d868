import { Modu<PERSON> } from '@nestjs/common';
import { UserController } from './user.controller';
import { CqrsModule } from '@nestjs/cqrs';
import { UserQueryHandlers } from './queries';
import { UserCommandHandlers } from './commands';
import { PrismaModule } from 'nestjs-prisma';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { UserRpcController } from './user.rpc.controller';

@Module({
  imports: [
    CqrsModule,
    PrismaModule,
    InternalClientModule
  ],
  controllers: [UserController, UserRpcController],
  providers: [...UserQueryHandlers, ...UserCommandHandlers],
})
export class UserModule { }
