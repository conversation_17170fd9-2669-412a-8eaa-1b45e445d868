import { USER_SERVICE_NAME, UserServiceController, UserResponse, UserWithPropertyRequest } from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { GetUserAndPropertyQueryGrpc, GetUserQueryGrpc } from './queries';
import { Id } from '@app/proto-schema/index.common';

@Controller()
export class UserRpcController implements UserServiceController {
  constructor(
    private readonly queryBus: QueryBus,
  ) {}

  @GrpcMethod(USER_SERVICE_NAME, 'getUserByIdPropertyId')
  getUserByIdPropertyId(request: UserWithPropertyRequest, metadata: Metadata, ...rest: any): Promise<UserResponse> | Observable<UserResponse> | UserResponse {
    return this.queryBus.execute(new GetUserAndPropertyQueryGrpc(request));
  }

  @GrpcMethod(USER_SERVICE_NAME, 'getUserById')
  getUserById(request: Id, metadata: Metadata): Promise<UserResponse> | Observable<UserResponse> | UserResponse {
    return this.queryBus.execute(new GetUserQueryGrpc(request.id));
  }
}

