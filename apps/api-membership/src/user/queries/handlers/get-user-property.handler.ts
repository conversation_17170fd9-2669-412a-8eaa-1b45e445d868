import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetUserAndPropertyQueryGrpc, GetUserQueryGrpc } from "../impl";
import { PrismaService } from 'nestjs-prisma';
import { UserResponse, UserData, ProfileData } from "@app/proto-schema/index.membership";
import { NotFoundException } from "@nestjs/common";

@QueryHandler(GetUserAndPropertyQueryGrpc)
export class GrpcGetUserAndPropertyHandler implements IQueryHandler<GetUserAndPropertyQueryGrpc> {
    constructor(private prisma: PrismaService) { }

    async execute(query: GetUserAndPropertyQueryGrpc): Promise<UserResponse> {
        const { args } = query;

        const user = await this.prisma.userProperty.findFirst({
            where: {
              AND: [
                {
                  property: {
                    id: args.propertyId
                  }
                },
                {
                  user: {
                    id: args.userId
                  }
                }
              ]
            },
            include: {
                user: {
                  include: {
                    profile: true
                  }
                },
                property: {
                  include: {
                    configuration: true
                  }
                }
            }
        });

        if (!user) {
            throw new NotFoundException('User property not found');
        }

        return {
            user: {
                id: user.user.id,
                username: user.user.username || '',
                email: user.user.email,
                password: user.user.password,
                provider: user.user.provider,
                mobileNumber: user.user.mobileNumber || '',
                business_type: user.user.businessType || '',
                type: user.user.type || '',
                status: user.user.status,
                is_active: user.user.isActive,
                refferal_code: user.user.refferalCode || '',
                parent_id: user.user.parentId || '',
                is_admin: user.user.isAdmin,
                zone: user.user.zone || '',
                profile: user.user.profile ? {
                    id: user.user.profile.id,
                    first_name: user.user.profile.firstName || '',
                    last_name: user.user.profile.lastName || '',
                    placeOfBirth: user.user.profile.placeOfBirth || '',
                    dateOfBirth: user.user.profile.dateOfBirth?.toISOString() || '',
                    gender: user.user.profile.gender,
                    address: user.user.profile.address || '',
                    mediaId: user.user.profile.mediaId || ''
                } : null
            },
            property: user?.property ? {
                id: user.property.id,
                cid: user.property.cid || '',
                companyName: user.property.companyName || '',
                brandName: user.property.brandName,
                companyEmail: user.property.companyEmail,
                companyPhoneNumber: user.property.companyPhoneNumber,
                npwp: user.property.npwp || '',
                address: user.property.address || '',
                createdAt: user.property.createdAt.toISOString(),
                updatedAt: user.property.updatedAt.toISOString(),
                postalId: user.property.postalId || '',
                propertyTypeId: user.property.propertyTypeId || '',
                unit: user.property.configuration.options['customfields']['value'] || '',
                categoryId: user.property.configuration.options['categoryId'],
                licenseKey: user.property.configuration.options['licenseNumber'],
                licenseType: user.property.licenseType,
                requestId: user.property.requestId,
            } : null,
            userProperty: user ? {
                id: user.id,
                userId: user.userId,
                propertyId: user.propertyId,
                isDefault: user.isDefault,
                createdAt: user.createdAt.toISOString(),
                updatedAt: user.updatedAt.toISOString()
            } : null
        };
    }
}
