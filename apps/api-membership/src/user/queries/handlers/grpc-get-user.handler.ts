import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetUserQueryGrpc } from "../impl";
import { PrismaService } from 'nestjs-prisma';
import { UserResponse, UserData, ProfileData } from "@app/proto-schema/index.membership";
import { NotFoundException } from "@nestjs/common";

@QueryHandler(GetUserQueryGrpc)
export class GrpcGetUserHandler implements IQueryHandler<GetUserQueryGrpc> {
    constructor(private prisma: PrismaService) { }

    async execute(query: GetUserQueryGrpc): Promise<UserResponse> {
        const { id } = query;

        const user = await this.prisma.user.findUnique({
            where: { id },
            include: {
                profile: true,
                properties: {
                    include: {
                        property: {
                            include: {
                                configuration: true
                            }
                        }
                    },
                    take: 1
                },
            }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        const userProperty = user.properties[0];

        return {
            user: {
                id: user.id,
                username: user.username || '',
                email: user.email,
                password: user.password,
                provider: user.provider,
                mobileNumber: user.mobileNumber || '',
                business_type: user.businessType || '',
                type: user.type || '',
                status: user.status,
                is_active: user.isActive,
                refferal_code: user.refferalCode || '',
                parent_id: user.parentId || '',
                is_admin: user.isAdmin,
                zone: user.zone || '',
                profile: user.profile ? {
                    id: user.profile.id,
                    first_name: user.profile.firstName || '',
                    last_name: user.profile.lastName || '',
                    placeOfBirth: user.profile.placeOfBirth || '',
                    dateOfBirth: user.profile.dateOfBirth?.toISOString() || '',
                    gender: user.profile.gender,
                    address: user.profile.address || '',
                    mediaId: user.profile.mediaId || ''
                } : null
            },
            property: userProperty?.property ? {
                id: userProperty.property.id,
                cid: userProperty.property.cid || '',
                companyName: userProperty.property.companyName || '',
                brandName: userProperty.property.brandName,
                companyEmail: userProperty.property.companyEmail,
                companyPhoneNumber: userProperty.property.companyPhoneNumber,
                npwp: userProperty.property.npwp || '',
                address: userProperty.property.address || '',
                createdAt: userProperty.property.createdAt.toISOString(),
                updatedAt: userProperty.property.updatedAt.toISOString(),
                postalId: userProperty.property.postalId || '',
                propertyTypeId: userProperty.property.propertyTypeId || '',
                unit: userProperty.property.configuration.options['customfields']['value'] || '',
                categoryId: userProperty.property.configuration.options['categoryId'],
                licenseKey: userProperty.property.configuration.options['licenseNumber']
            } : null,
            userProperty: userProperty ? {
                id: userProperty.id,
                userId: userProperty.userId,
                propertyId: userProperty.propertyId,
                isDefault: userProperty.isDefault,
                createdAt: userProperty.createdAt.toISOString(),
                updatedAt: userProperty.updatedAt.toISOString()
            } : null
        };
    }
}
