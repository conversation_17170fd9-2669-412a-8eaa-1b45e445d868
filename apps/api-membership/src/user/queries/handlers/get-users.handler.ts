import { BadRequestException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { GetUsersQuery } from '../impl';
import { UserGrpcService } from 'apps/api-membership/src/internal-client/user.service';
import { lastValueFrom } from 'rxjs';
import { status } from '@grpc/grpc-js';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetUsersQuery)
export class GetUsersHandler implements IQueryHandler<GetUsersQuery> {
  constructor(
    private readonly userService: UserGrpcService,
    private prisma: PrismaService,
  ) {}

  async execute(query: GetUsersQuery) {
    const { args, user } = query;
    let propertyIds: string[] = [];

    if (args.propertyId) {
      propertyIds = [args.propertyId];
    } else {
      const userProperties = await this.prisma.userProperty.findMany({
        where: {
          userId: user.id,
        },
        select: {
          propertyId: true,
        },
      });
      propertyIds = userProperties.map((p) => p.propertyId);
    }

    try {
      const { users = [] } =
        (await lastValueFrom(
          this.userService.getUsersByPropertyId({
            propertyId: propertyIds,
          }),
        )) || {};

      // Apply filters and search
      let filteredUsers = users;

      // Apply search filters
      if (args.search && args.search.trim() !== '') {
        const searchTerm = args.search.toLowerCase();
        filteredUsers = filteredUsers.filter((u) => {
          const fullName = `${u.profile?.firstName || ''} ${u.profile?.lastName || ''}`.toLowerCase();
          return (
            u.username?.toLowerCase().includes(searchTerm) ||
            u.email?.toLowerCase().includes(searchTerm) ||
            u.zoneName?.toLowerCase().includes(searchTerm) ||
            fullName.includes(searchTerm)
          );
        });
      }

      // Apply specific field searches
      if (args.username && args.username.trim() !== '') {
        const searchTerm = args.username.toLowerCase();
        filteredUsers = filteredUsers.filter((u) =>
          u.username?.toLowerCase().includes(searchTerm)
        );
      }

      if (args.email && args.email.trim() !== '') {
        const searchTerm = args.email.toLowerCase();
        filteredUsers = filteredUsers.filter((u) =>
          u.email?.toLowerCase().includes(searchTerm)
        );
      }

      if (args.zoneName && args.zoneName.trim() !== '') {
        const searchTerm = args.zoneName.toLowerCase();
        filteredUsers = filteredUsers.filter((u) =>
          u.zoneName?.toLowerCase().includes(searchTerm)
        );
      }

      if (args.userName && args.userName.trim() !== '') {
        const searchTerm = args.userName.toLowerCase();
        filteredUsers = filteredUsers.filter((u) => {
          const fullName = `${u.profile?.firstName || ''} ${u.profile?.lastName || ''}`.toLowerCase();
          return fullName.includes(searchTerm);
        });
      }

      // Apply filters
      if (args.businessUnit && args.businessUnit.trim() !== '') {
        filteredUsers = filteredUsers.filter((u) =>
          u.businessType?.toLowerCase() === args.businessUnit.toLowerCase()
        );
      }

      if (args.type) {
        filteredUsers = filteredUsers.filter((u) =>
          u.businessType?.toLowerCase() === args.type.toLowerCase()
        );
      }

      if (args.status) {
        filteredUsers = filteredUsers.filter((u) =>
          u.status?.toLowerCase() === args.status.toLowerCase()
        );
      }

      // Apply pagination if provided
      const page = Number(args?.page || 1);
      const limit = Number(args?.limit || 50);
      const skip = (page - 1) * limit;

      const total = filteredUsers.length;
      const paginatedUsers = filteredUsers.slice(skip, skip + limit);

      return {
        data: paginatedUsers,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error(error.message);
      if (error?.code === status.NOT_FOUND) {
        return {
          data: [],
          meta: {
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0,
          },
        };
      }
      throw new BadRequestException('Failed to fetch users');
    }
  }
}
