import { BadRequestException, NotFoundException } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { GetUserQuery } from '../impl';
import { UserGrpcService } from '../../../internal-client/user.service';
import { firstValueFrom } from 'rxjs';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetUserQuery)
export class GetUserHandler implements IQueryHandler<GetUserQuery> {
  constructor(
    private readonly userService: UserGrpcService,
  ) {}

  async execute(query: GetUserQuery) {
    const { id } = query;

    try {
      const { user } = await firstValueFrom(
        this.userService.getUserById({ id })
      );

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to fetch user');
    }
  }
}
