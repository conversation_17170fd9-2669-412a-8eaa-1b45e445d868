import { Controller, Get, NotFoundException, Param, Query } from '@nestjs/common';
import { AddonGrpcService } from './service/add-on.grpc.service';
import { ApiParam, ApiTags, ApiQuery } from '@nestjs/swagger';
import { ListAddonResponse, AddonRequest, AddonDetail } from '@app/proto-schema/index.internal';
import { Id } from '@app/proto-schema/index.common';
import { status } from '@grpc/grpc-js';

@ApiTags('Addon')
@Controller('addon')
export class AddonController {
  constructor(private addonGrpcService: AddonGrpcService) { }

  @Get()
  @ApiQuery({ name: 'subfolderId', required: false })
  async listAddon(
    @Query('subfolderId') subfolderId?: string,
  ): Promise<ListAddonResponse> {
    const request: AddonRequest = { subfolderId }
    return await this.addonGrpcService.listAddon(request).toPromise()
  }

  @Get(':id')
  @ApiParam({ name: 'id', description: 'Addon ID' })
  async detailAddon(
    @Param('id') id: string
  ): Promise<AddonDetail> {
    const request: Id = { id }
    try {
      return await this.addonGrpcService.detailAddon(request).toPromise()
    } catch(err) {
      if (err.code = status.NOT_FOUND) {
        throw new NotFoundException('Addon Not Found!')
      }
    }
  }
}
