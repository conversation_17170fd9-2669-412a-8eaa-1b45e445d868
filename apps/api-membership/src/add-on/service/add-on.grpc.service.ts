import { ListAddonResponse, Addon, ADDON_SERVICE_NAME, AddonRequest, AddonServiceClient, AddonDetail } from "@app/proto-schema/index.internal";
import { Id } from "@app/proto-schema/index.common";
import { Metadata } from "@grpc/grpc-js";
import { Inject, Injectable } from "@nestjs/common";
import { ClientGrpc } from "@nestjs/microservices";
import { Observable } from "rxjs";

@Injectable()
export class AddonGrpcService {
  private addonService: AddonServiceClient

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.addonService = this.client.getService<AddonServiceClient>(ADDON_SERVICE_NAME)
  }

  listAddon(request: AddonRequest): Observable<ListAddonResponse> {
    const meta = new Metadata
    return this.addonService.listAddon(request, meta)
  }

  detailAddon(request: Id): Observable<AddonDetail> {
    const meta = new Metadata()
    return this.addonService.detailAddon(request, meta)
  }
}
