import { Modu<PERSON> } from "@nestjs/common";
import { ClientsModule } from "@nestjs/microservices";
import { AddonGrpcService } from "./service/add-on.grpc.service";
import { AddonController } from "./add-on.controller";
import { InternalClient } from "libs/clients/internal.client";
import { ConfigService } from "@nestjs/config";
import { INTERNAL_PACKAGE } from "libs/clients";

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => InternalClient(configService),
        name: INTERNAL_PACKAGE
      }
    ])
  ],
  controllers: [AddonController],
  providers: [AddonGrpcService],
  exports: [AddonGrpcService]
})

export class AddonModule { }
