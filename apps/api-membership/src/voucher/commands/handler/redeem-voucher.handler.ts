import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { RedeemVoucher } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { VoucherService } from '../../../internal-client/services';
import { Metadata, status } from '@grpc/grpc-js';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';

@CommandHandler(RedeemVoucher)
export class RedeemVoucherHandler implements ICommandHandler<RedeemVoucher> {
  constructor(
    private voucherService: VoucherService,
    private prisma: PrismaService,
  ) {}

  async execute(command: RedeemVoucher) {
    const { args } = command;

    const order = await this.prisma.order.findFirst({
      where: {
        id: args.orderId,
      },
      include: {
        details: true,
      },
    });

    if (order.status != 'pending') {
      throw new BadRequestException(`can't redeem voucher`);
    }

    try {
      const meta = new Metadata();
      const voucherResult = await this.voucherService.client
        .useVoucher({ code: args.code, orderId: args.orderId }, meta)
        .toPromise();

      await this.prisma.$transaction(async (tr) => {
        await tr.orderDetail.update({
          where: {
            id: voucherResult.itemId,
          },
          data: {
            voucherId: voucherResult.voucherId,
            discount: voucherResult.discountPrice,
            totalPrice: voucherResult.totalPriceAfter,
          },
        });

        await tr.order.update({
          where: { id: args.orderId },
          data: {
            voucherCode: args.code,
            discount: voucherResult.discountPrice,
            totalPrice: Number(order.totalPrice) - voucherResult.discountPrice,
          },
        });
      });

      return {
        status: 'Success',
        message: 'Success redeem voucher',
        data: voucherResult,
      };
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException(err.message);
      } else if (err.code === status.INVALID_ARGUMENT) {
        throw new BadRequestException(err.message);
      } else {
        throw new InternalServerErrorException(err.message);
      }
    }
  }
}
