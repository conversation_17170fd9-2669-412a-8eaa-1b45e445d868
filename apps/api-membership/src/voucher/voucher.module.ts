import { Module } from '@nestjs/common';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { VoucherController } from './voucher.controller';
import { VoucherCommandHandlers } from './commands/handler';
import { CqrsModule } from '@nestjs/cqrs';

@Module({
  imports: [CqrsModule, InternalClientModule],
  controllers: [VoucherController],
  providers: [...VoucherCommandHandlers],
})
export class VoucherModule {}
