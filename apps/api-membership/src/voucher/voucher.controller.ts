import { Metadata, status } from '@grpc/grpc-js';
import {
  BadRequestException,
  Body,
  Controller,
  InternalServerErrorException,
  NotFoundException,
  Post,
} from '@nestjs/common';
import { VoucherService } from '../internal-client/services';
import { GenerateVoucherDto } from './dto/generate-voucher.dto';
import { MatchingVoucherDto } from './dto/matching-voucher.dto';
import { RedeemVoucherDto } from './dto/redeem-voucher.dto';
import { CommandBus } from '@nestjs/cqrs';
import { RedeemVoucher } from './commands';

@Controller('voucher')
export class VoucherController {
  constructor(
    private commandBus: CommandBus,
    private voucherService: VoucherService,
  ) {}

  @Post('matching')
  async getByCode(@Body() args: MatchingVoucherDto) {
    try {
      const meta = new Metadata();
      return await this.voucherService.client
        .checkVoucherByCode({ code: args.code }, meta)
        .toPromise();
    } catch (err) {
      if (err.code == status.NOT_FOUND) {
        throw new NotFoundException('voucher not found');
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }

  @Post('generate')
  async generate(@Body() args: GenerateVoucherDto) {
    try {
      const meta = new Metadata();
      return await this.voucherService.client
        .checkVoucherExist({ id: args.id }, meta)
        .toPromise();
    } catch (err) {
      if (err.code == status.NOT_FOUND) {
        throw new NotFoundException('voucher not found');
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }

  @Post('redeem')
  async redeem(@Body() args: RedeemVoucherDto) {
    return await this.commandBus.execute(new RedeemVoucher(args));
  }
}
