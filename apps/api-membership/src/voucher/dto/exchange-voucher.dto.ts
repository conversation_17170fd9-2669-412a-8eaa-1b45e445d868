import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, IsString, ValidateIf } from "class-validator";

export class ExchangeVoucherDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @ValidateIf((prop) => prop.dateOfBirth != '')
  voucherId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @ValidateIf((prop) => prop.dateOfBirth != '')
  orderId: string;
}
