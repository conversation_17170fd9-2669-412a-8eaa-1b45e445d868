import { NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { UpdateRoleCommand } from '../impl';

@CommandHandler(UpdateRoleCommand)
export class UpdateRoleHandler implements ICommandHandler<UpdateRoleCommand> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UpdateRoleCommand) {
    const { id, args } = command;
    const item = await this.prisma.role.findFirst({ where: { id: id } });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      await this.prisma.role.update({
        where: { id: item.id },
        data: {
          name: args.name,
          type: 'internal',
          permission: {
            deleteMany: {},
            createMany: {
              data: args.permission.map((pr) => {
                return {
                  create: pr.create,
                  update: pr.update,
                  delete: pr.delete,
                  read: pr.read,
                  manage: pr.manage,
                  permissionId: pr.id,
                };
              }),
              skipDuplicates: true,
            },
          },
        },
      });
      return 'successfully updated role';
    } catch (error) {
      return 'failed updated role';
    }
  }
}
