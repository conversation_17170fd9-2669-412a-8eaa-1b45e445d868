import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { DeleteRoleCommand } from '../impl';

@CommandHandler(DeleteRoleCommand)
export class Delete<PERSON><PERSON><PERSON>andler implements ICommandHandler<DeleteRoleCommand> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeleteRoleCommand) {
    const { id } = command;
    const item = await this.prisma.role.findFirst({
      where: { AND: [{ id: id }, { type: 'internal' }] },
    });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      await this.prisma.role.delete({ where: { id: item.id } });
      return 'successfully deleted role';
    } catch (error) {
      return 'failed deleted role';
    }
  }
}
