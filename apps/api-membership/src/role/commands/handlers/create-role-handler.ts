import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { CreateRoleCommand } from '../impl';

@CommandHandler(CreateRoleCommand)
export class CreateRoleHandler implements ICommandHandler<CreateRoleCommand> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: CreateRoleCommand) {
    const { args } = command;
    try {
      await this.prisma.role.create({
        data: {
          name: args.name,
          type: args.type,
          propertyId : args.propertyId,
          permission: {
            createMany: {
              data: args.permission.map((pr) => {
                return {
                  create: pr.create,
                  update: pr.update,
                  delete: pr.delete,
                  read: pr.read,
                  manage: pr.manage,
                  permissionId: pr.id,
                };
              }),
              skipDuplicates: true,
            },
          },
        },
      });
      return 'successfully created role';
    } catch (error) {
      return 'failed created role';
    }
  }}
