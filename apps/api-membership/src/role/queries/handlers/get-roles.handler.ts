import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Prisma, Role } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { GetRolesQuery } from '../impl';

@QueryHandler(GetRolesQuery)
export class GetRolesHandler implements IQueryHandler<GetRolesQuery> {
  constructor(private prisma: PrismaService) { }

  async execute(query: GetRolesQuery) {
    const { args } = query;
    const search = args?.search || '';
    const item = await Pagination<Prisma.RoleGetPayload<{
      include: {
        property: {
          select: {
            id: true,
            companyName: true,
            brandName: true,
            companyEmail: true,
            companyPhoneNumber: true
          }
        }
      };
    }>, Prisma.RoleFindManyArgs>(
      this.prisma.role,
      {
        where: {
          AND: [
            { name: { contains: search, mode: 'insensitive' } },
            { type: 'internal' },
          ],
        },
        include: {
          property: {
            select: {
              id: true,
              companyName: true,
              brandName: true,
              companyEmail: true,
              companyPhoneNumber: true
            }
          }
        }
      },
      { page: args.page, limit: args.page },
    );
    return {
      ...item,
      data: item.data.map(role => ({
        id: role.id,
        name: role.name,
        property: {
          id: role.property.id,
          companyName: role.property.companyName,
          brandName: role.property.brandName,
          companyEmail: role.property.companyEmail,
          companyPhoneNumber: role.property.companyPhoneNumber
        }
      }))
    };
  }
}
