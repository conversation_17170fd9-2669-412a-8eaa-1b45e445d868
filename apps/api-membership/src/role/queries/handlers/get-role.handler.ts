import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetRoleQuery } from '../impl';

@QueryHandler(GetRoleQuery)
export class Get<PERSON>oleHandler implements IQueryHandler<GetRoleQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetRoleQuery) {
    const { id } = query;
    const item = await this.prisma.role.findFirst({
      where: { AND: [{ id: id }, { type: 'internal' }] },
      include: { 
        permission: { include: { permission: true } },
        property: {
          select: {
            id: true,
            companyName: true,
            brandName: true,
            companyEmail: true,
            companyPhoneNumber: true
          }
        }
      },
    });
    if (!item) {
      throw new NotFoundException();
    }
    const reMap = {
      id: item.id,
      name: item.name,
      property: {
        id: item.property.id,
        companyName: item.property.companyName,
        brandName: item.property.brandName,
        companyEmail: item.property.companyEmail,
        companyPhoneNumber: item.property.companyPhoneNumber
      },
      permission: item.permission.map((item) => {
        return {
          id: item.permissionId,
          module: item.permission.module,
          manage: item.manage,
          read: item.read,
          create: item.create,
          update: item.update,
          delete: item.delete,
        };
      }),
    };
    return reMap;
  }
}
