import { BaseFilterDto, Permission } from '@app/common';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Permissions } from '../auth/decorator/permissions.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { RolesGuard } from '../auth/guards/role.guard';
import {
  CreateRoleCommand,
  DeleteRoleCommand,
  UpdateRoleCommand,
} from './commands';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { GetRoleQuery, GetRolesQuery } from './queries';

@Controller({ version: '1', path: 'role' })
@ApiTags('Role')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
export class RoleController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) {}

  @Post()
  @Permissions([Permission.ROLE_MANAGE, Permission.ROLE_CREATE])
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.commandBus.execute(new CreateRoleCommand(createRoleDto));
  }

  @Get()
  @Permissions([Permission.ROLE_MANAGE, Permission.ROLE_READ])
  findAll(@Query() filter: BaseFilterDto) {
    return this.queryBus.execute(new GetRolesQuery(filter));
  }

  @Get(':id')
  @Permissions([Permission.ROLE_MANAGE, Permission.ROLE_READ])
  findOne(@Param('id') id: string) {
    return this.queryBus.execute(new GetRoleQuery(id));
  }

  @Patch(':id')
  @Permissions([Permission.ROLE_MANAGE, Permission.ROLE_UPDATE])
  update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.commandBus.execute(new UpdateRoleCommand(id, updateRoleDto));
  }

  @Delete(':id')
  @Permissions([Permission.ROLE_MANAGE, Permission.ROLE_DELETE])
  remove(@Param('id') id: string) {
    return this.commandBus.execute(new DeleteRoleCommand(id));
  }
}
