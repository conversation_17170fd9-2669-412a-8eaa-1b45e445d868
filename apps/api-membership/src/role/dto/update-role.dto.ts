import { ContextDto } from '@app/common';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsUnique } from 'libs/validator/src/is-unique/is-unique';
import { Type } from 'class-transformer';
import { IsString, MaxLength, ValidateNested } from 'class-validator';
import { RolePermissionDto } from './role-permission.dto';

export class UpdateRoleDto extends PartialType(ContextDto) {
  @ApiProperty()
  @IsString()
  @MaxLength(50)
  @IsUnique({ model: 'Role', ignore: true })
  name: string;
  @ApiProperty({ isArray: true, type: RolePermissionDto })
  @ValidateNested({ each: true })
  @Type(() => RolePermissionDto)
  permission: RolePermissionDto[];
}
