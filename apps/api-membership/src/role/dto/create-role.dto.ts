import { ApiProperty } from '@nestjs/swagger';
import { IsUnique } from 'libs/validator/src/is-unique/is-unique';
import { Type } from 'class-transformer';
import { IsString, MaxLength, ValidateNested, IsEnum, IsNotEmpty } from 'class-validator';
import { RolePermissionDto } from './role-permission.dto';
import { RoleType } from '@prisma/client';

export class CreateRoleDto {
  @ApiProperty()
  @IsString()
  @MaxLength(50)
  @IsUnique({ model: 'Role' })
  name: string;

  @ApiProperty({ enum: RoleType, default: RoleType.internal })
  @IsEnum(RoleType)
  type: RoleType;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  propertyId: string;

  @ApiProperty({ type: [RolePermissionDto] })
  @ValidateNested({ each: true })
  @Type(() => RolePermissionDto)
  permission: RolePermissionDto[];
}
