import { ApiProperty } from '@nestjs/swagger';
import { IsExist } from 'libs/validator/src/is-exist/is-exist';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export class RolePermissionDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @IsExist({ model: 'Permission' })
  id: string;
  @ApiProperty()
  @IsBoolean()
  create: boolean;
  @ApiProperty()
  @IsBoolean()
  read: boolean;
  @ApiProperty()
  @IsBoolean()
  manage: boolean;
  @ApiProperty()
  @IsBoolean()
  update: boolean;
  @ApiProperty()
  @IsBoolean()
  delete: boolean;
}
