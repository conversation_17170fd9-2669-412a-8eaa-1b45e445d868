import { NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { UploadAvatarCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(UploadAvatarCommand)
export class UploadAvatarHandler
  implements ICommandHandler<UploadAvatarCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UploadAvatarCommand) {
    const { user, args } = command;

    const profile = await this.prisma.profile.findFirst({
      where: {
        user: { id: user.id } ,
      },
      include: { media: true, user: true },
    });

    if (!profile) {
      throw new NotFoundException();
    }

    const media = await this.prisma.media.findFirst({
      where: {
        id: args.mediaId
      }
    });

    if (!media) {
      throw new NotFoundException();
    }

    await this.prisma.profile.update({
      where: {
        id: profile.id,
      },
      data: {
        mediaId: media.id,
      },
    });

    return 'successfully uploaded avatar';
  }
}
