import {
  Body,
  Controller,
  FileTypeValidator,
  MaxFileSizeValidator,
  ParseFilePipe,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { UploadAvatarCommand } from './commands';
import { UploadAvatarDto } from './dto/upload-avatar.dto';
import { Throttle } from '@nestjs/throttler';

@ApiTags('Avatar')
@Controller('avatar')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class AvatarController {
  constructor(
    private commandBus: CommandBus,
  ) {}

  @Post()
  @Throttle({ default: { limit: 10, ttl: 60000 } })
  upload(
    @User() user: ICurrentUser,
    @Body() uploadAvatarDto: UploadAvatarDto,
  ) {
    return this.commandBus.execute(new UploadAvatarCommand(user, uploadAvatarDto));
  }
}
