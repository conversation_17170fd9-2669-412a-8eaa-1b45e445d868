export interface OrderCreateData {
    propertyId: string;
    userId: string;
    name: string;
    description: string;
    status: 'pending';
    totalPrice: number;
    tag: string;
    details: OrderDetailData;
}

export interface RenewPackageItem {
    packageId?: string;
    newItemId?: string;
    qty: number;
}

export interface PackageFeature {
    featureId?: string;
    qty: number;
    feature?: {
        id: string;
        name: string;
    };
}
export interface Tax {
    id: string;
    name: string;
    nominal: number;
    description: string;
    type: string;
    startDate: string;
    endDate: string;
}

export interface OrderDetailData {
    name: string;
    itemType: string;
    itemId: string;
    sku: string;
    duration: string;
    price: number;
    qty: number;
    totalPrice: number;
    tax: number;
    taxItems: any[];
    features?: PackageFeature[];
}

export interface OrderDetailWithPackage {
    id: string;
    orderId: string;
    name: string;
    totalPrice: any; // Decimal
    discount: any; // Decimal
    createdAt: Date;
    updatedAt: Date;
    serial: string;
    duration: string;
    price: any; // Decimal
    itemType: string;
    itemId: string;
    sku: string;
    qty: number;
    tax: any; // Decimal
    taxItems: any;
    referralId: string;
    orderPackageDetail?: {
        featureId: string;
        qty: number;
    }[];
}
