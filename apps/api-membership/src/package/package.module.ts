import { Module } from '@nestjs/common';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { PackageController } from './package.controller';
import { PackageQueryHandlers } from './queries';
import { CqrsModule } from '@nestjs/cqrs';
import { PackageCommandHandlers } from './commands';
import { IntegrationModule } from '../integration/integration.module';

@Module({
  imports: [InternalClientModule, CqrsModule,IntegrationModule],
  controllers: [PackageController],
  providers: [...PackageQueryHandlers, ...PackageCommandHandlers],
})
export class PackageModule { }
