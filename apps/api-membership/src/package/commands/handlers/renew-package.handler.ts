import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from "@nestjs/cqrs";
import { RenewPackageCommand } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { PropertyGrpcService } from "apps/api-membership/src/internal-client/property.service";
import { firstValueFrom } from "rxjs";
import { HttpException, InternalServerErrorException, NotFoundException, UnprocessableEntityException } from "@nestjs/common";
import { calculateTax, invoiceGenerator, Tracer } from "@app/common";
import { OrderCreateData, OrderDetailData, OrderDetailWithPackage, RenewPackageItem } from "../../type";
import { GetPropertyBillPackageResponse } from "@app/proto-schema/index.internal";

@CommandHandler(RenewPackageCommand)
export class RenewPackageHandler implements ICommandHandler<RenewPackageCommand> {
    constructor(
        private readonly prisma: PrismaService,
        private readonly propertyService: PropertyGrpcService,
    ) { }

    @Tracer
    async execute(command: RenewPackageCommand) {
        const { user, args, id } = command;

        const propertyId = id;

        try {
            await this.validateProperty(propertyId);

            await this.validateNoPendingOrder(propertyId);

            const propertyPackage = await this.getPropertyPackage(propertyId, args);

            const order = await this.processOrders(user.id, propertyId, propertyPackage);

            await this.generateInvoice(order.id);

            return order;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException({
                message: error.message || 'Failed to process package renewal'
            });
        }
    }

    @Tracer
    private async validateProperty(propertyId: string) {
        const property = await this.prisma.property.findUnique({
            where: { id: propertyId }
        });
        if (!property) {
            throw new NotFoundException(`Property with ID ${propertyId} not found`);
        }
        return property;
    }

    @Tracer
    private async validateNoPendingOrder(propertyId: string) {
        const existingOrder = await this.prisma.order.findFirst({
            where: {
                AND: {
                    propertyId,
                    status: 'pending'
                }
            }
        })

        if (existingOrder) {
            throw new UnprocessableEntityException(`Pending order already exists.`)
        }
    }


    @Tracer
    private async getPropertyPackage(propertyId: string, item: RenewPackageItem): Promise<GetPropertyBillPackageResponse> {
        const { packages } = await firstValueFrom(
            this.propertyService.getPropertyBill({ id: propertyId })
        );

        if (!packages?.length) {
            throw new NotFoundException(`No packages found for property ${propertyId}`);
        }

        const pkg = packages.find(p => p.packageId === item.packageId);
        if (!pkg) {
            throw new NotFoundException(`Package ${item.packageId} not found`);
        }

        if (pkg.type === 'LICENSE') {
            throw new UnprocessableEntityException('License order aren\'t implemented.')
        }
        
        return {
            ...pkg,
            qty: item.qty // Overide with item qty incase user want to add quantity
        } as unknown as GetPropertyBillPackageResponse;
    }

    /**
     * Create monthly payment subscription order using same configuration and features from the previous order
     */
    @Tracer
    private async createSubscriptionOrder(userId: string, propertyId: string, pkg: GetPropertyBillPackageResponse) {
        const previousOrder = await this.getPreviousOrder(pkg);
        const orderDetail = await this.createSubscriptionOrderDetail(previousOrder);
        const basePrice = Number(orderDetail.price) * Number(orderDetail.qty || 1);
        const { totalTax, taxItems } = await calculateTax(basePrice, pkg.taxes || []);
    
        orderDetail.totalPrice = basePrice + totalTax;
        orderDetail.tax = totalTax;
        orderDetail.taxItems = taxItems;
    
        const orderData = this.createOrderData(userId, propertyId, pkg, orderDetail, 'next-subscription');
        return this.createOrder(orderData);
    }
    
    /**
     * Create a contract renewal order using the latest package master data
     */
    @Tracer
    private async createContractOrder(userId: string, propertyId: string, pkg: GetPropertyBillPackageResponse) {
        const orderDetail = await this.createPlanOrderDetail(pkg);
        const orderData = this.createOrderData(userId, propertyId, pkg, orderDetail, 'extend-subscription');
        return this.createOrder(orderData);
    }

    @Tracer
    private async getPreviousOrder(packageData: GetPropertyBillPackageResponse): Promise<OrderDetailWithPackage> {
        const previousOrder = await this.prisma.orderDetail.findFirst({
            where: {
                orderId: packageData.orderId,
                itemId: packageData.itemId
            },
            include: {
                orderPackageDetail: true
            }
        });

        if (!previousOrder) {
            throw new UnprocessableEntityException('No previous order found for the package');
        }

        return previousOrder;
    }

    @Tracer
    private async processOrders(userId: string, propertyId: string, propertyPackage:GetPropertyBillPackageResponse) {
        if (propertyPackage.renewType === 'SUBSCRIPTION') {
            return await this.createSubscriptionOrder(userId, propertyId, propertyPackage);
        }
    
        if (propertyPackage.renewType === 'CONTRACT') {
            return await this.createContractOrder(userId, propertyId, propertyPackage);
        }
    
        throw new UnprocessableEntityException(`Renew type ${propertyPackage.renewType} not implemented`)
    }    

    @Tracer
    private createSubscriptionOrderDetail(previousOrder: OrderDetailWithPackage): Promise<OrderDetailData> {
        return Promise.resolve({
            name: previousOrder.name,
            itemType: previousOrder.itemType,
            itemId: previousOrder.itemId,
            sku: previousOrder.sku,
            duration: previousOrder.duration,
            price: Number(previousOrder.price),
            qty: Number(previousOrder.qty) || 1,
            totalPrice: Number(previousOrder.totalPrice),
            tax: Number(previousOrder.tax),
            taxItems: previousOrder.taxItems,
            features: previousOrder.orderPackageDetail?.map(detail => ({
                featureId: detail.featureId,
                qty: Number(detail.qty) || 1
            }))
        })
    }

    @Tracer
    private async createPlanOrderDetail(packageData: GetPropertyBillPackageResponse) {
        const qty = Number(packageData.qty) || 1;
        const basePrice = Number(packageData.price) * qty;
        const { totalTax, taxItems } = await calculateTax(basePrice, packageData.taxes || []);
        return {
            name: packageData.name,
            itemType: packageData.type,
            itemId: packageData.itemId,
            sku: packageData.sku,
            duration: packageData.billCycle,
            price: packageData.price,
            qty: Number(packageData.qty) || 1,
            totalPrice: packageData.total,
            tax: totalTax,
            taxItems: taxItems,
            features: packageData.features?.map(feature => ({
                featureId: feature.featureId,
                qty: Number(feature.qty) || 1
            }))
        };
    }

    @Tracer
    private createOrderData(
        userId: string,
        propertyId: string,
        pkg: GetPropertyBillPackageResponse,
        orderDetail: OrderDetailData,
        tag: string
    ): OrderCreateData {
        return {
            propertyId,
            userId,
            name: `${tag === 'next-subscription' ? 'Renew' : 'Extend'} ${pkg.name}`,
            description: `${tag === 'next-subscription' ? 'Renew subscription:' : 'Extend:'} ${pkg.name}`,
            status: 'pending',
            totalPrice: orderDetail.totalPrice,
            tag,
            details: orderDetail
        };
    }
    
    @Tracer
    private async createOrder(orderData: OrderCreateData) {
        return this.prisma.order.create({
            data: {
                ...orderData,
                details: {
                    create: {
                        name: orderData.details.name,
                        itemType: orderData.details.itemType,
                        itemId: orderData.details.itemId,
                        sku: orderData.details.sku,
                        duration: orderData.details.duration,
                        price: orderData.details.price,
                        qty: Number(orderData.details.qty) || 1,
                        totalPrice: orderData.details.totalPrice,
                        tax: orderData.details.tax,
                        taxItems: orderData.details.taxItems,
                        orderPackageDetail: orderData.details.features ? {
                            create: orderData.details.features.map(feature => ({
                                featureId: feature.featureId,
                                qty: Number(feature.qty) || 1
                            }))
                        } : undefined
                    }
                }
            },
            include: { details: true }
        });
    }

    @Tracer
    private async generateInvoice(orderId: string) {
        const orderInvoice = await invoiceGenerator(this.prisma, 'order', {}, 'ORD');
        await this.prisma.order.update({
            where: { id: orderId },
            data: { invoice: orderInvoice }
        });
    }
    
}