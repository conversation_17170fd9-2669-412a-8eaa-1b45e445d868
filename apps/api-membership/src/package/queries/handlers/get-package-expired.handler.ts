import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPackagesExpiredQuery } from '../impl/get-package-expired.query';
import { PropertyGrpcService } from '../../../internal-client/property.service';
import { firstValueFrom } from 'rxjs';
import { Id } from '@app/proto-schema/index.common';
import { PrismaService } from 'nestjs-prisma';
import { IntegrationDlpService } from '../../../integration/services';
import { UnprocessableEntityException } from '@nestjs/common';

@QueryHandler(GetPackagesExpiredQuery)
export class GetPackagesExpiredHandler implements IQueryHandler<GetPackagesExpiredQuery> {
    constructor(
        private readonly propertyService: PropertyGrpcService,
        private readonly prisma: PrismaService,
        private readonly integrationService: IntegrationDlpService
    ) { }

    private async calculateLicensePrice(property: any, basePrice: number) {
        const { customfields } = property.configuration?.options || {};
        
        if (!customfields?.id) {
            throw new UnprocessableEntityException('Invalid property configuration for license');
        }

        const payload = {
            id: customfields.id,
            amount: Number(customfields.value)
        };

        try {
            const licenseCalculation = await this.integrationService.calculateLicense(payload);
            return Number(basePrice) + Number(licenseCalculation.nominal);
        } catch (error) {
            console.error('Failed to calculate license price:', error);
            return basePrice;
        }
    }

    async execute(query: GetPackagesExpiredQuery) {
        const request: Id = { id: query.propertyId };

        const response = await firstValueFrom(
            this.propertyService.getPropertyBill(request)
        );

        const property = await this.prisma.property.findUnique({
            where: { id: query.propertyId },
            include: { configuration: true }
        });

        const packages = await Promise.all(response.packages?.map(async pkg => {
            const basePackage = {
                packageId: pkg.packageId,
                type: pkg.type,
                name: pkg.name,
                status: pkg.status,
                billingCycle: pkg.billCycle,
                renewType: pkg.renewType,
                taxes: pkg.taxes,
                dueDate: new Date(Number(pkg.renewDue)).toISOString(),
                sku: pkg.sku,
                qty: pkg.qty
            };

            if (pkg.renewType?.toLowerCase() === 'subscription') {
                const orderDetail = await this.prisma.orderDetail.findFirst({
                    where: {
                        orderId: pkg.orderId,
                        itemId: pkg.itemId
                    },
                    include: {
                        orderPackageDetail: true
                    }
                });

                if (orderDetail) {
                    const features = orderDetail.orderPackageDetail.map(detail => {
                        const pkgFeature = pkg.features?.find(f => f.featureId === detail.featureId);
                        return {
                            featureId: detail.featureId,
                            qty: detail.qty,
                            featureName: pkgFeature?.feature?.name || ''
                        };
                    });

                    const price = pkg.type === 'LICENSE' 
                        ? await this.calculateLicensePrice(property, Number(orderDetail.price))
                        : Number(orderDetail.price);
                    
                    return {
                        ...basePackage,
                        price,
                        total: Number(orderDetail.totalPrice),
                        features
                    };
                }
            }

            const features = pkg.features?.map(feature => ({
                featureId: feature.featureId,
                qty: Number(feature.qty),
                featureName: feature.feature?.name || ''
            })) || [];

            const price = pkg.type === 'LICENSE' && property
                ? await this.calculateLicensePrice(property, Number(pkg.price))
                : Number(pkg.price);

            return {
                ...basePackage,
                price,
                total: pkg.total,
                features
            };
        }) || []);

        return packages;
    }
}