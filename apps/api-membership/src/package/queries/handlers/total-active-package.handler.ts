import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { TotalActivePackageQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PackageService } from 'apps/api-membership/src/internal-client/services';
import { Metadata } from '@grpc/grpc-js';
import { Id } from '@app/proto-schema/index.common';

@QueryHandler(TotalActivePackageQuery)
export class TotalActivePackageHandler
  implements IQueryHandler<TotalActivePackageQuery>
{
  constructor(
    private prisma: PrismaService,
    private packageService: PackageService,
  ) {}

  async execute(query: TotalActivePackageQuery): Promise<any> {
    const { Id } = query;
    const meta = new Metadata();

    const properties = await this.prisma.userProperty.findMany({
      where: { userId: Id },
      select: { propertyId: true },
    });

    const propertyIds = properties.map((p) => p.propertyId);
    return await this.packageService.client
      .getTotalActivePackage({ Ids: propertyIds }, meta)
      .toPromise();
  }
}
