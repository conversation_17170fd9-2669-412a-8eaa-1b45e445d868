import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListExpiredPackageQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PackageService } from 'apps/api-membership/src/internal-client/services';
import { Metadata } from '@grpc/grpc-js';

@QueryHandler(ListExpiredPackageQuery)
export class ListExpiredPackageHandler
  implements IQueryHandler<ListExpiredPackageQuery>
{
  constructor(
    private prisma: PrismaService,
    private packageService: PackageService,
  ) {}

  async execute(query: ListExpiredPackageQuery): Promise<any> {
    const { Id } = query;
    const meta = new Metadata();

    const properties = await this.prisma.userProperty.findMany({
      where: { userId: Id },
      select: { propertyId: true },
    });

    const propertyIds = properties.map((p) => p.propertyId);
    return await this.packageService.client
      .listExpiredPackage({ Ids: propertyIds }, meta)
      .toPromise();
  }
}
