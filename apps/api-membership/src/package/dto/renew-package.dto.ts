import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class RenewPackageDto {
    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    packageId?: string;

    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    newItemId?: string;

    @ApiProperty({ default: 1, required: true })
    @IsNumber()
    @IsNotEmpty()
    qty: number
}