import { Metadata, status } from '@grpc/grpc-js';
import {
  Controller,
  Get,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Query,
  UseGuards,
  Post,
  Body,
} from '@nestjs/common';
import { PackageService } from '../internal-client/services';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { User } from '../auth/decorator/user.decorator';
import { FilterPackageDto } from './dto/filter-package.dto';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { Id } from '@app/proto-schema/index.common';
import { QueryBus } from '@nestjs/cqrs';
import {
  GetPackagesExpiredQuery,
  ListExpiredPackageQuery,
  TotalActivePackageQuery,
} from './queries';
import { CommandBus } from '@nestjs/cqrs';
import { RenewPackageCommand } from './commands/impl/renew-package.command';
import { RenewPackageDto } from './dto/renew-package.dto';

@ApiTags('Package')
@Controller('package')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class PackageController {
  constructor(
    private packageService: PackageService,
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Get()
  async getPackages(
    @User() user: ICurrentUser,
    @Query() filter: FilterPackageDto,
  ) {
    const query: any = {
      limit: filter?.limit,
      page: filter?.page,
      search: filter?.search,
      sort: filter?.sort,
      sortType: filter?.sortType,
    };
    if (filter.propertyId) {
      query.propertyId = filter.propertyId;
    }
    try {
      const meta = new Metadata();
      const response = await this.packageService.client
        .listPackage(
          {
            query: JSON.stringify(query),
          },
          meta,
        )
        .toPromise();

      return response;
    } catch (err) {
      console.error(err.message);
      throw new InternalServerErrorException('Internal Server Error');
    }
  }

  @Get('/total-active-package')
  async totalActivePackage(@User() user: ICurrentUser) {
    try {
      return this.queryBus.execute(new TotalActivePackageQuery(user.id));
    } catch (err) {
      console.log(err);
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException('Package not found');
      }
      throw new InternalServerErrorException('Internal Server Error');
    }
  }

  @Get('/list-expired-package')
  async listExpiredPackage(@User() user: ICurrentUser) {
    try {
      return this.queryBus.execute(new ListExpiredPackageQuery(user.id));
    } catch (err) {
      console.log(err);
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException('Package not found');
      }
      throw new InternalServerErrorException('Internal Server Error');
    }
  }

  @Get(':id')
  async generate(@User() user: ICurrentUser, @Param('id') id: string) {
    const query = {
      id,
      propertyId: user.propertyId,
    };
    try {
      const meta = new Metadata();
      return await this.packageService.client
        .detailPackage({ query: JSON.stringify(query) }, meta)
        .toPromise();
    } catch (err) {
      if (err.code == status.NOT_FOUND) {
        throw new NotFoundException('package not found');
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }

  @Get(':propertyId/expired')
  async packageExpired(@Param('propertyId') id: string) {
    return await this.queryBus.execute(new GetPackagesExpiredQuery(id));
  }

  @Post(':propertyId/renew')
  async renewPackage(
    @User() user: ICurrentUser,
    @Param('propertyId') id: string,
    @Body() Payload: RenewPackageDto,
  ) {
    return await this.commandBus.execute(
      new RenewPackageCommand(user, id, Payload),
    );
  }
}
