import { $Enums } from '@prisma/client';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from 'nestjs-prisma';

@ValidatorConstraint({ async: true })
export class IsRoleExistConstraint implements ValidatorConstraintInterface {
  constructor(private readonly prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const {
      model,
      field = 'id',
      type,
    }: IsRoleExistOptions = args.constraints[0];

    const check = await this.prisma[model as string].findFirst({
      where: { AND: [{ [field]: value }, { type: type }] },
    });

    if (check) {
      return true;
    }
    return false;
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} not exist`;
  }
}

export function IsRoleExist(
  property: IsRoleExistOptions,
  validationOptions?: ValidationOptions,
) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsRoleExist',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: IsRoleExistConstraint,
    });
  };
}

export type IsRoleExistOptions = {
  model: string;
  field?: string;
  type?: $Enums.RoleType;
};
