import { <PERSON>, Get, Param, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiParam, <PERSON>piTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { JobPositionGrpcService } from '../internal-client/job-position.service';
import { Empty, Id } from '@app/proto-schema/index.common';
import { JobPosition } from '@app/proto-schema/index.internal';

@ApiTags('Job Position')
@ApiBearerAuth()
@UseGuards(JwtGuard)
@Controller('job-positions')
export class JobPositionController {
  constructor(
    private jobPositionGrpcService: JobPositionGrpcService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get list of job positions' })
  async listJobPositions(): Promise<JobPosition[]> {
    const request = {} as Empty;
    const response = await this.jobPositionGrpcService.listJobPosition(request).toPromise();
    return response.data;
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get job position details' })
  @ApiParam({ name: 'id', required: true, description: 'Job Position ID' })
  async getJobPosition(@Param('id') id: string): Promise<JobPosition> {
    const request: Id = { id };
    return this.jobPositionGrpcService.detailJobPosition(request).toPromise();
  }
}
