import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { ClientsModule } from "@nestjs/microservices";
import { JobPositionController } from "./job-position.controller";
import { InternalClient } from "libs/clients/internal.client";
import { CqrsModule } from "@nestjs/cqrs";
import { ConfigService } from "@nestjs/config";
import { INTERNAL_PACKAGE } from "libs/clients";
import { JobPositionGrpcService } from "../internal-client/job-position.service";

@Module({
  imports: [
    CqrsModule,
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => InternalClient(configService),
        name: INTERNAL_PACKAGE
      }
    ])
  ],
  controllers: [JobPositionController],
  providers: [
    JobPositionGrpcService
  ],
  exports: [JobPositionGrpcService]
})

export class JobPositionModule { }
