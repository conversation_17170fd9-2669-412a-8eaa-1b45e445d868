import {
  Body,
  Controller,
  Delete,
  Get,
  Ip,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RegisterDto } from './dto/register.dto';
import {
  ChangePasswordCommand,
  CreateUserChildCommand,
  CreateUserCommand,
  DeleteChildCommand,
  DoLoginCommand,
  DoLogoutCommand,
  ForgotPasswordCommand,
  RefreshTokenCommand,
  SendOtpCodeCommand,
  SsoLoginCommand,
  UpdateBusinessTypeCommand,
  UpdateUserCommand,
  VerifyEmailCommand,
  VerifyOtpCodeCommand,
} from './commands';
import { DoLoginDto } from './dto/dologin.dto';
import { LoggedSessionCreate } from './types';
import { JwtGuard } from './guards/jwt.guard';
import { ICurrentUser } from './strategies/types/user.type';
import { User } from './decorator/user.decorator';
import { GoogleCallbackQuery, MeQuery, VerifyOtpEmailQuery } from './queries';
import { VerifyEmailDto } from './dto/verify-email.dto';
import { SendOtpCodeDto } from './dto/send-otp-code.dto';
import { VerifyOtpCodeDto } from './dto/verify-otp-code.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { SsoLoginDto } from './dto/sso-login.dto';
import { GoogleGuard } from './guards/google.guard';
import { Request, Response } from 'express';
import { Throttle } from '@nestjs/throttler';
import { RtGuard } from './guards/rt.guard';
import { UpdateUserDto } from './dto/update-user.dto';
import { UpdateBusinessTypeDto } from './dto/update-business-type.dto';
import { RegisterChildDto } from './dto/register-child.dto';

@Controller('auth')
@ApiTags('Auth')
export class AuthController {
  constructor(
    private commandBus: CommandBus,
    private queryBus: QueryBus,
  ) {}

  @Throttle({ default: { limit: 3, ttl: 60000 } })
  @Post('login')
  login(@Body() doLogin: DoLoginDto, @Ip() ip: string, @Req() req: Request) {
    const loggedSession: LoggedSessionCreate = {
      ip: ip,
      media: req.headers['user-agent'],
    };
    return this.commandBus.execute(new DoLoginCommand(doLogin, loggedSession));
  }

  @Throttle({ default: { limit: 10, ttl: 60000 } })
  @Post('refresh')
  @ApiBearerAuth()
  @UseGuards(RtGuard)
  refreshToken(
    @User('sub') userId: string,
    @User('jwtToken') refreshToken: string,
  ) {
    return this.commandBus.execute(
      new RefreshTokenCommand(userId, refreshToken),
    );
  }

  @Post('sso')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  loginSSO(
    @User() user: ICurrentUser,
    @Body() ssoLogin: SsoLoginDto,
    @Ip() ip: string,
    @Req() req: Request,
  ) {
    const loggedSession: LoggedSessionCreate = {
      ip: ip,
      media: req.headers['user-agent'],
    };
    return this.commandBus.execute(
      new SsoLoginCommand(user, ssoLogin, loggedSession),
    );
  }

  @Throttle({ default: { limit: 3, ttl: 60000 } })
  @Post('register')
  register(
    @Body() registerDto: RegisterDto,
    @Ip() ip: string,
    @Req() req: Request,
  ) {
    const loggedSession: LoggedSessionCreate = {
      ip: ip,
      media: req.headers['user-agent'],
    };
    return this.commandBus.execute(
      new CreateUserCommand(registerDto, loggedSession),
    );
  }

  @Post('send-otp-code')
  sendOtp(@Body() sendOtpCodeDto: SendOtpCodeDto) {
    return this.commandBus.execute(new SendOtpCodeCommand(sendOtpCodeDto));
  }

  @Post('verify-otp-code')
  verifyOtp(@Body() verifyOtpCodeDto: VerifyOtpCodeDto) {
    return this.commandBus.execute(new VerifyOtpCodeCommand(verifyOtpCodeDto));
  }

  @Post('forgot-password')
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.commandBus.execute(
      new ForgotPasswordCommand(forgotPasswordDto),
    );
  }

  @Post('change-password')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  changePassword(
    @User() user: ICurrentUser,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    return this.commandBus.execute(
      new ChangePasswordCommand(user, changePasswordDto),
    );
  }

  @Throttle({ default: { limit: 2, ttl: 60000 } })
  @Post('verify-email')
  verifyAccount(@Body() verifyEmailDto: VerifyEmailDto) {
    return this.commandBus.execute(new VerifyEmailCommand(verifyEmailDto));
  }

  @Get('verify-otp-email')
  verifyOtpEmail(@Query('token') token: string) {
    return this.queryBus.execute(new VerifyOtpEmailQuery(token));
  }

  @Post('logout')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  logout(@User() user: ICurrentUser, @Req() req: Request, @Ip() ip: string) {
    const loggedSession: LoggedSessionCreate = {
      ip: req.headers['X-forward-for'] as string,
      media: req.headers['user-agent'],
    };
    return this.commandBus.execute(new DoLogoutCommand(user.id, loggedSession));
  }

  @Get('me')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  getMe(@User() user: ICurrentUser) {
    return this.queryBus.execute(new MeQuery(user));
  }

  @Patch('me')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  UpdateMe(@User() user: ICurrentUser, @Body() updateUserDto: UpdateUserDto) {
    return this.commandBus.execute(new UpdateUserCommand(user, updateUserDto));
  }

  @Get('callback')
  @UseGuards(GoogleGuard)
  async googleCallback(@Req() req, @Res() res: Response, @Ip() ip: string) {
    const loggedSession: LoggedSessionCreate = {
      ip: ip,
      media: req.headers['user-agent'],
    };
    return this.queryBus.execute(
      new GoogleCallbackQuery(req, res, loggedSession),
    );
  }

  @Post('business-type')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  businessType(
    @User() user: ICurrentUser,
    @Body() payload: UpdateBusinessTypeDto,
  ) {
    return this.commandBus.execute(
      new UpdateBusinessTypeCommand(user, payload),
    );
  }

  @Post('register-child/:id')
  @ApiOperation({ summary: 'Register child with propertyId' })
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  registerChild(
    @Param('id') id: string,
    @User() user: ICurrentUser,
    @Body() registerChildDto: RegisterChildDto,
  ) {
    return this.commandBus.execute(
      new CreateUserChildCommand(id, user, registerChildDto),
    );
  }

  @Delete('delete-child/:id')
  @ApiOperation({ summary: 'Delete child with propertyId' })
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  deleteChild(@Param('id') id: string, @User() user: ICurrentUser) {
    return this.commandBus.execute(new DeleteChildCommand(id, user));
  }
}
