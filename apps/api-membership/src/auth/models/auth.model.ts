import { AggregateRoot } from '@nestjs/cqrs';
import { LoggedSessionCreate } from '../types';
import {
  LoggedSessionEvent,
  SendVerifyEmailEvent,
  UpdatedHashRtEvent,
  UpdateSessionEvent,
} from '../events';

export class AuthModel extends AggregateRoot {
  constructor(private userId: string) {
    super();
    this.autoCommit = true;
  }

  createSession(loggedSessionCreate: LoggedSessionCreate) {
    this.apply(new LoggedSessionEvent(this.userId, loggedSessionCreate));
  }

  updateSession(hashAt: string) {
    this.apply(new UpdateSessionEvent(this.userId, hashAt));
  }

  updatedHashRt(hash?: string) {
    this.apply(new UpdatedHashRtEvent(this.userId, hash));
  }

  sendVerifyEmail(email: string) {
    this.apply(new SendVerifyEmailEvent(email));
  }
}
