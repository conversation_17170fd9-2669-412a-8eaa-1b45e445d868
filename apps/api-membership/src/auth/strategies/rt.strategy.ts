import { ForbiddenException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { IUserPayload } from './types/user.type';
import { PrismaService } from 'nestjs-prisma';

@Injectable()
export class RtStrategy extends PassportStrategy(Strategy, 'rt-jwt') {
  constructor(
    config: ConfigService,
    private prisma: PrismaService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: config.get<string>('RT_SECRET'),
      passReqToCallback: true,
    });
  }
  validate(req: Request, payload: IUserPayload) {
    const refreshToken: string =
      (req.headers['Authorization'] as string) ||
      (req.headers['authorization'] as string);

    if (!refreshToken || !refreshToken.startsWith('Bearer')) {
      throw new ForbiddenException('Refresh token malformed');
    }

    const jwtToken = refreshToken.substring(7, refreshToken.length);

    return {
      ...payload,
      jwtToken,
    };
  }
}
