import { generateUsername } from '@app/common';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { PrismaService } from 'nestjs-prisma';
import { Profile, Strategy } from 'passport-google-oauth20';

@Injectable()
export class GoogleOauthStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    config: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    super({
      clientID: config.get<string>('GOOGLE_CLIENT_ID'),
      clientSecret: config.get<string>('GOOGLE_CLIENT_SECRET'),
      callbackURL: config.get<string>('CALLBACK_URL'),
      scope: [
        'profile',
        'email',
        'https://www.googleapis.com/auth/user.birthday.read',
        'https://www.googleapis.com/auth/user.gender.read',
        'https://www.googleapis.com/auth/user.addresses.read',
        'https://www.googleapis.com/auth/user.phonenumbers.read',
      ],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: Profile) {
    const { name, emails, _json } = profile;

    const email = emails[0].value;
    const firstName = name.givenName || '';
    const lastName = name.familyName || '';
    const gender = _json.gender || 'unknown';
    const dateOfBirth = _json.birthday || null;
    const address = _json.addresses ? _json.addresses[0]?.formatted || '' : '';
    const phoneNumber = _json.phoneNumbers
      ? _json.phoneNumbers[0]?.value || ''
      : '';

    console.log('Google Profile JSON:', JSON.stringify(_json, null, 2));

    const user = await this.prisma.user.findFirst({
      where: {
        email: {
          equals: email,
          mode: 'insensitive',
        },
      },
    });

    if (!user) {
      const createdUser = await this.prisma.user.create({
        data: {
          email,
          username: generateUsername(firstName, lastName, 4),
          status: 'active',
          provider: 'google',
          mobileNumber: phoneNumber,
          activationAt: new Date(),
          isActive: true,
          profile: {
            create: {
              firstName,
              lastName,
              dateOfBirth,
              address,
              gender,
            },
          },
        },
      });

      return createdUser;
    }

    return user;
  }
}
