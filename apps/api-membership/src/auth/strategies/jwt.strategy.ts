import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { PrismaService } from 'nestjs-prisma';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ICurrentUser, IUserPayload } from './types/user.type';
import { Request } from 'express';
import { createHash } from 'crypto';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    config: ConfigService,
    private prisma: PrismaService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: config.get<string>('AT_SECRET'),
      passReqToCallback: true,
    });
  }

  async validate(req: Request, payload: IUserPayload) {
    const user = await this.prisma.user.findFirst({
      where: {
        id: payload.sub,
      },
      include: {
        properties: {
          where: {
            isDefault: true,
          },
          include: {
            property: true,
          },
          take: 1,
        },
      },
    });

    if (!user) {
      throw new UnauthorizedException();
    }

    const defaultProperty = user.properties[0];

    if (!defaultProperty) {
      const result = {
        id: user.id,
        email: user.email,
        propertyId: defaultProperty?.propertyId,
        businessType: user.businessType,
      };
      return result;
    }

    const token = this.getBearerToken(req);
    const hashAt = createHash('sha256').update(token).digest('base64');

    const session = await this.prisma.userSession.findFirst({
      where: {
        AND: [{ hashAt: hashAt }, { isActive: true }],
      },
    });

    if (!session) {
      throw new UnauthorizedException();
    }

    const result: ICurrentUser = {
      id: user.id,
      email: user.email,
      propertyId: defaultProperty.propertyId,
      businessType: user.businessType,
    };

    return result;
  }

  getBearerToken(req: Request): string | null {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.split(' ')[1];
    }
    return null;
  }
}
