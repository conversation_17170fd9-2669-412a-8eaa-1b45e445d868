export interface IUserPayload {
  sub: string;
  username: string;
  iat: number;
  exp: number;
}

export interface ICurrentUser {
  id: string;
  email: string;
  // username: string;
  businessType: string;
  propertyId: string;
  // permissions: ICurrentPermission[];
  // provider: string;
  // deviceId: string
}

export interface ICurrentPermission {
  propertyId: any;
  feature: string;
  limiter: boolean | number | string;
}

export type IPermissionMeta = {
  can: string[];
};
