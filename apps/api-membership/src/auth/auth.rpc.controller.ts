import {
  AUTH_SERVICE_NAME,
  AuthServiceController,
  GetUserByIdRequest,
  PartnershipRequest,
  Property,
  PropertyServiceController,
  PropertyUserRequest,
  User,
} from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { GetUserQuery } from './queries';
import { Status } from '@app/proto-schema/common-proto/common';
import { CreatePartnershipCommand, CreateUserCommand } from './commands';
import { LoggedSessionCreate } from './types';

@Controller()
export class AuthRpcController implements AuthServiceController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) {}

  @GrpcMethod(AUTH_SERVICE_NAME, 'getUserById')
  getUserById(
    request: GetUserByIdRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<User> | Observable<User> | User {
    return this.queryBus.execute(new GetUserQuery(request.userId));
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'registerPartnership')
  registerPartnership(
    request: PartnershipRequest,
    metadata: Metadata,
    ...rest
  ): Promise<User> | Observable<User> | User {
    return this.commandBus.execute(new CreatePartnershipCommand(request));
  }
}
