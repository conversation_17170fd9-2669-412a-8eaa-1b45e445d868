import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { JwtModule } from '@nestjs/jwt';
import { AuthController } from './auth.controller';
import { UserCommandHandlers } from './commands';
import { AuthQueryHandlers } from './queries';
import { JwtStrategy } from './strategies/jwt.strategy';
import { PropertyTypeModule } from '../property-type/property-type.module';
import { AuthEventHandlers } from './events';
import { AuthRpcController } from './auth.rpc.controller';
import { RtStrategy } from './strategies/rt.strategy';
import { PassportModule } from '@nestjs/passport';
import { GoogleOauthStrategy } from './strategies/google-oauth.strategy';
import { InternalClientModule } from '../internal-client/internal-client.module';

@Module({
  imports: [
    CqrsModule,
    JwtModule.register({}),
    PropertyTypeModule,
    PassportModule.register({ defaultStrategy: 'google' }),
    InternalClientModule
  ],
  controllers: [
    AuthController,
    AuthRpcController
  ],
  providers: [
    ...UserCommandHandlers,
    ...AuthQueryHandlers,
    ...AuthEventHandlers,
    JwtStrategy,
    RtStrategy,
    GoogleOauthStrategy
  ],
})
export class AuthModule {}
