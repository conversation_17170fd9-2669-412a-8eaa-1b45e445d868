import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from "@nestjs/cqrs";
import { UpdatedHashRtEvent } from "../impl";
import { PrismaService } from "nestjs-prisma";

@EventsHandler(UpdatedHashRtEvent)
export class UpdateHashRtHandler implements IEventHandler<UpdatedHashRtEvent> {
  constructor(private prisma: PrismaService) {

  }

  async handle(event: UpdatedHashRtEvent) {
    const { userId, hash } = event;
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        hashRt: hash ? hash : undefined,
      },
    });
  }
}
