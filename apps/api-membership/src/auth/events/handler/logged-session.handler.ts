import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from "@nestjs/cqrs";
import { LoggedSessionEvent } from "../impl";
import { PrismaService } from "nestjs-prisma";

@EventsHandler(LoggedSessionEvent)
export class LoggedSessionHandler implements IEventHandler<LoggedSessionEvent> {
  constructor(
    private readonly prisma: PrismaService
  ) {
  }
  async handle(event: LoggedSessionEvent) {
    const { userId, args } = event;
    try {
      await this.prisma.userSession.updateMany({
        where: {
          AND: [
            {
              isActive: true
            },
            {
              user: { id: userId },
            }
          ]
        },
        data: {
          isActive: false,
          hashAt: null
        }
      })

      await this.prisma.userSession.create({
        data: {
          isActive: args?.isActive || true,
          ip: args.ip,
          hashAt: args.hashAt,
          media: args.media,
          user: { connect: { id: userId } },
        },
      });
    } catch (e) {
      console.log(e)
    }
  }
}
