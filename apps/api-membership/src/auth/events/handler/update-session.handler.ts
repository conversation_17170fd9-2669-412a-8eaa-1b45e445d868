import { <PERSON><PERSON><PERSON><PERSON>, IEvent<PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { UpdateSessionEvent } from "../impl";
import { PrismaService } from "nestjs-prisma";

@EventsHandler(UpdateSessionEvent)
export class UpdateSessionHandler implements IEventHandler<UpdateSessionEvent> {
  constructor(
    private readonly prisma: PrismaService
  ) {

  }
  async handle(event: UpdateSessionEvent) {
    const { userId, hashAt } = event;

    const activeSession = await this.prisma.userSession.findFirst({
      where: {
        AND: [
          {
            user: {
              id: userId
            }
          },
          {
            isActive: true
          },
        ]
      }
    })

    await this.prisma.userSession.update({
      where: {
        id: activeSession.id
      },
      data: {
        hashAt
      },
    });
  }
}
