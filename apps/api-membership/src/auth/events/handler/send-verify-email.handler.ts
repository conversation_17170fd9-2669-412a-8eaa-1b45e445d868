import { <PERSON><PERSON><PERSON><PERSON>, IEvent<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { SendVerifyEmailEvent } from '../impl';
import { MailerService } from '@nestjs-modules/mailer';
import { PrismaService } from 'nestjs-prisma';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { join } from 'path';

@EventsHandler(SendVerifyEmailEvent)
export class SendVerifyEmailHandler
  implements IEventHandler<SendVerifyEmailEvent>
{
  constructor(
    private readonly mailerService: MailerService,
    private readonly prisma: PrismaService,
    private readonly config: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  async handle(event: SendVerifyEmailEvent) {
    const { email } = event;
    try {
      const user = await this.prisma.user.findFirst({
        where: {
          email: {
            equals: email,
            mode: 'insensitive',
          },
        },
      });

      if (!user) {
        throw new BadRequestException('User not found');
      }

      if (user.status === 'active') {
        throw new BadRequestException('Email already verified');
      }

      const token = await this.jwtService.signAsync(
        { sub: user.id.toString(), email: user.email },
        { secret: this.config.get<string>('AT_SECRET'), expiresIn: '24h' },
      );

      await this.prisma.user.update({
        where: {
          id: user.id,
        },
        data: {
          verificationToken: token,
        },
      });

      const year = new Date().getFullYear();
      const verificationLink = `${this.config.get<string>('CLIENT_REDIRECT_URL')}/verify-email?email=${user.email}&token=${token}`;
      // const verificationLink = `${this.config.get<string>('CLIENT_REDIRECT_URL')}/verify-email?token=${token}`;

      await this.mailerService.sendMail({
        to: email,
        subject: 'Email Verification',
        template: 'confirm-registration',
        context: {
          verificationLink: verificationLink,
          year: year,
        },
      });

      return 'Email verification sent';
    } catch (error) {
      console.error('Error in SendVerifyEmailHandler:', error);
      throw new InternalServerErrorException(error.message);
    }
  }
}
