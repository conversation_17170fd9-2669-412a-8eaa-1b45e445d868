import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DlpIntegrationGuard implements CanActivate {
  constructor(private readonly configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const apiKeyHeader = request.headers['x-api-key'];
    console.log('api-key');
    console.log(apiKeyHeader);

    const validApiKey =
      '2T5YBkgd1dmqa46MSPBXd6aM7N0PU8jPr6Qy7j6LrUu77dX9rCuUHNQryEDBzmtB'; //this.configService.get<string>('DLP_API_KEY');
    // const validApiKey = this.configService.get<string>('DLP_API_KEY');
    console.log(validApiKey);

    if (apiKeyHeader !== validApiKey) {
      throw new UnauthorizedException('Invalid API key');
    }

    return true;
  }
}
