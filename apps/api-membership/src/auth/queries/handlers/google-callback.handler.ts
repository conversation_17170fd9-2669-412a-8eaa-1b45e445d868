import { <PERSON><PERSON>ublish<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GoogleCallbackQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { createHash } from 'crypto';
import { AuthModel } from '../../models/auth.model';

@QueryHandler(GoogleCallbackQuery)
export class GoogleCallbackQueryHandler
  implements IQueryHandler<GoogleCallbackQuery>
{
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private publisher: EventPublisher,
  ) {}

  async execute(query: GoogleCallbackQuery) {
    const { req, res, session } = query;

    const user = await this.prisma.user.findFirst({
      where: { username: req.user.username },
    });

    if (!user) {
      return res.redirect(
        `${this.configService.get<string>('CLIENT_REDIRECT_URL')}/sign-in?nouser=1`,
      );
    }

    // const token = await this.jwtService.signAsync(
    //   { sub: req.user.id, username: req.user.username },
    //   { secret: this.configService.get<string>('AT_SECRET'), expiresIn: '24h' },
    // );

    const [token, rtToken] = await Promise.all([
      this.jwtService.signAsync(
        { sub: req.user.id.toString(), username: req.user.username },
        {
          secret: this.configService.get<string>('AT_SECRET'),
          expiresIn: '24h',
        },
      ),
      this.jwtService.signAsync(
        { sub: req.user.id.toString(), username: req.user.username },
        {
          secret: this.configService.get<string>('RT_SECRET'),
          expiresIn: '7d',
        },
      ),
    ]);

    const hashRt = createHash('sha256').update(rtToken).digest('base64');
    session.hashAt = createHash('sha256').update(token).digest('base64');

    const userSession = await this.prisma.userSession.findFirst({
      where: { AND: [{ isActive: true }, { user: { id: user.id } }] },
    });

    if (!userSession) {
      const authModel = this.publisher.mergeClassContext(AuthModel);

      const auth = new authModel(user.id);
      auth.updatedHashRt(hashRt);
      auth.createSession(session);
    }

    const authModel = this.publisher.mergeClassContext(AuthModel);

    const auth = new authModel(user.id);
    auth.updatedHashRt(hashRt);
    auth.createSession(session);

    await this.prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        isActive: true,
        verificationToken: null,
        activationAt: new Date(),
      },
    });

    res.redirect(
      `${this.configService.get<string>('CLIENT_REDIRECT_URL')}/sign-in/oauth?token=${token}`,
    );
  }
}
