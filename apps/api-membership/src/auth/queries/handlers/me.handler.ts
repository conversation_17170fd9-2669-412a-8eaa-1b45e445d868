import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { MeQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { ConfigService } from '@nestjs/config';
import { PropertyTypeGrpcService } from 'apps/api-membership/src/property-type/service/property-type.grpc.service';
import { Id } from '@app/proto-schema/index.common';
import { Logger, NotFoundException } from '@nestjs/common';

@QueryHandler(MeQuery)
export class MeHandler implements IQueryHandler<MeQuery> {
  private readonly logger = new Logger(MeHandler.name);

  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
    private propertyTypeService: PropertyTypeGrpcService,
  ) {}

  async execute(query: MeQuery) {
    const { args } = query;

    try {
      const user = await this.fetchUserWithRelations(args.id);

      const defaultProperties = this.filterDefaultProperties(user);
      const countBusiness = user.properties.length;

      const propertyType = await this.fetchPropertyType(defaultProperties);
      const canCreateBusiness = this.determineBusinessCreationEligibility(
        user.businessType,
        countBusiness,
      );

      return this.mapUserResponse(
        user,
        defaultProperties[0],
        propertyType,
        canCreateBusiness,
      );
    } catch (error) {
      this.logger.error(
        `Error fetching user details: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async fetchUserWithRelations(userId: string) {
    const user = await this.prisma.user.findFirst({
      where: { id: userId },
      include: {
        profile: {
          include: {
            media: true,
          },
        },
        properties: {
          include: {
            property: {
              include: {
                media: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  private filterDefaultProperties(user) {
    return user.properties.filter((prop) => prop.isDefault);
  }

  private async fetchPropertyType(defaultProperties) {
    const defaultProperty = defaultProperties[0];

    if (!defaultProperty?.property?.propertyTypeId) {
      return null;
    }

    try {
      const propertyTypeId: Id = {
        id: defaultProperty.property.propertyTypeId,
      };

      return await this.propertyTypeService
        .getOnePropertyType(propertyTypeId)
        .toPromise();
    } catch (error) {
      this.logger.warn(`Failed to fetch property type: ${error.message}`);
      return null;
    }
  }

  private determineBusinessCreationEligibility(
    businessType: string,
    countBusiness: number,
  ): boolean {
    if (businessType !== 'Single' && countBusiness <= 19) {
      return true;
    }

    if (businessType === 'Single' && countBusiness < 1) {
      return true;
    }

    return false;
  }

  private mapUserResponse(
    user,
    defaultProperty,
    propertyType,
    canCreateBusiness,
  ) {
    return {
      id: user.id.toString(),
      email: user.email,
      mobileNumber: user.mobileNumber,
      firstName: user.profile?.firstName ?? null,
      lastName: user.profile?.lastName ?? null,
      placeOfBirth: user.profile?.placeOfBirth ?? null,
      dateOfBirth: user.profile?.dateOfBirth ?? null,
      gender: user.profile?.gender ?? null,
      address: user.profile?.address ?? null,
      avatar: user.profile?.media
        ? {
            id: user.profile.media.id,
            url: this.generateMediaUrl(
              user.profile.media.path,
              user.profile.media.fileName,
            ),
          }
        : null,
      type: user.businessType,
      provider: user.provider,
      verifiedAt: user.activationAt,
      canCreateBusiness,
      countBusinessUnit: user.properties.length,
      property: this.mapPropertyDetails(defaultProperty, propertyType),
    };
  }

  private generateMediaUrl(path: string, fileName: string): string {
    return `https://${this.config.get<string>('MINIO_ENDPOINT')}/${path}/${fileName}`;
  }

  private mapPropertyDetails(defaultProperty, propertyType) {
    if (!defaultProperty) return null;

    const { property, isDefault } = defaultProperty;

    return {
      id: property.id.toString(),
      brandName: property.brandName,
      companyName: property.companyName,
      propertyType,
      logo: property.media
        ? {
            id: property.media.id,
            url: this.generateMediaUrl(
              property.media.path,
              property.media.fileName,
            ),
          }
        : null,
    };
  }
}
