import { <PERSON>Publisher, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { VerifyOtpEmailQuery } from '../impl';
import { BadRequestException } from '@nestjs/common';
import { createHash } from 'crypto';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthModel } from '../../models/auth.model';

@QueryHandler(VerifyOtpEmailQuery)
export class VerifyOtpEmailHandler
  implements IQueryHandler<VerifyOtpEmailQuery>
{
  constructor(
    private readonly prisma: PrismaService,
    private jwtService: JwtService,
    private config: ConfigService,
    private publisher: EventPublisher,
  ) {}

  async execute(query: VerifyOtpEmailQuery) {
    const { token } = query;

    const user = await this.prisma.user.findFirst({
      where: { verificationToken: token },
    });

    if (!user) {
      throw new BadRequestException({ token: ['Invalid Token'] });
    }

    if (user.status == 'active') {
      throw new BadRequestException({ email: ['Email already verified'] });
    }

    try {
      const [atToken, rtToken] = await Promise.all([
        this.jwtService.signAsync(
          { sub: user.id, username: user.username },
          { secret: this.config.get<string>('AT_SECRET'), expiresIn: '24h' },
        ),

        this.jwtService.signAsync(
          { sub: user.id, username: user.username },
          { secret: this.config.get<string>('RT_SECRET'), expiresIn: '7d' },
        ),
      ]);

      const hashRt = createHash('sha256').update(rtToken).digest('base64');
      const hashAt = createHash('sha256').update(atToken).digest('base64');
      const authModel = this.publisher.mergeClassContext(AuthModel);
      const auth = new authModel(user.id);
      auth.updatedHashRt(hashRt);
      auth.updateSession(hashAt);

      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          isActive: true,
          status: 'active',
          verificationToken: null,
          activationAt: new Date(),
        },
      });

      return {
        success: true,
        data: { access_token: atToken, refresh_token: rtToken },
        message: 'Email verified successfully',
      };
    } catch (error) {
      console.error(error);
      throw new BadRequestException('Verification process failed');
    }
  }
}
