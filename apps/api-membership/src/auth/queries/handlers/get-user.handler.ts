import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetUserQuery, MeQ<PERSON>y } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { RpcException } from "@nestjs/microservices";
import { status } from "@grpc/grpc-js";

@QueryHandler(GetUserQuery)
export class Get<PERSON>serHandler implements IQueryHandler<GetUserQuery> {
  constructor(
    private prisma: PrismaService,
  ) {}

  async execute(query: GetUserQuery) {
    const { userId } = query;
    const item = await this.prisma.user.findFirst({
      where: {
        id: userId
      },
      include: {
        profile: true,
      }
    });
    if (!item) {
      throw new RpcException({ code: status.NOT_FOUND, message: 'user not found'});
    }

    return {
      id: item.id,
      email: item.email,
      mobileNumber: item.mobileNumber,
      profile: {
        id: item.profile.id,
        firstName: item.profile.firstName,
        lastName: item.profile.lastName,
        placeOfBirth: item.profile.placeOfBirth,
        dateOfBirth: item.profile.dateOfBirth,
        address: item.profile.address,
        createdAt: item.profile.createdAt,
        updatedAt: item.profile.updatedAt
      },
      createdAt: item.createdAt,
      updatedAt: item.updatedAt
    };
  }
}
