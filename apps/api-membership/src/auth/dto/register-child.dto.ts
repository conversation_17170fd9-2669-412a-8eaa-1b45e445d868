import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import {
  IsEmail,
  IsEnum,
  IsMobilePhone,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  ValidateIf,
} from 'class-validator';
import { DateFormat } from 'libs/validator/src/date-format/date-format';
import { IsUnique } from 'libs/validator/src/is-unique/is-unique';
import { IsValidEmail } from '../../../../../libs/validator/src/is-valid-email/is-valid-email';

export class RegisterChildDto {
  @ApiProperty()
  @IsString()
  @MaxLength(50)
  firstName: string;

  @ApiProperty()
  @IsString()
  @MaxLength(50)
  lastName: string;

  @ApiProperty()
  @IsEmail()
  @IsValidEmail()
  @IsUnique({ model: 'user' })
  email: string;

  @ApiProperty()
  @IsMobilePhone('id-ID')
  @IsUnique({ model: 'user' })
  mobileNumber: string;

  @ApiProperty({ enum: $Enums.GenderType })
  @IsEnum($Enums.GenderType)
  gender: $Enums.GenderType;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  password: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  passwordConfirmation: string;

  @ApiProperty()
  @IsOptional()
  @ValidateIf((prop) => prop.dateOfBirth != '')
  @DateFormat('YYYY-MM-DD')
  dateOfBirth?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @MaxLength(250)
  address?: string;
}
