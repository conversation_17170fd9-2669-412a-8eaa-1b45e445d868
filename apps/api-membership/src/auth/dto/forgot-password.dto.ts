import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { IsExist } from 'libs/validator/src/is-exist/is-exist';
import { IsUnique } from 'libs/validator/src/is-unique/is-unique';

export class ForgotPasswordDto {
  @ApiProperty()
  @IsString()
  @IsExist({ model: 'user', field: 'email' })
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  otp: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  new_password: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  passwordConfirmation: string;
}
