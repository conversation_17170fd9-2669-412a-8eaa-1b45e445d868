import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { IsExist } from 'libs/validator/src/is-exist/is-exist';

export class ChangePasswordDto {
  // @ApiProperty()
  // @IsString()
  // @IsExist({ model: 'user', field: 'email' })
  // @IsNotEmpty()
  // email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  password_lama: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  password_baru: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  konfirmasi_password_baru: string;
}
