import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { SsoLoginCommand } from '../impl';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'nestjs-prisma';
import {
  InternalServerErrorException,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import { AuthGrpcService } from 'apps/api-membership/src/internal-client/auth.service';
import { status } from '@grpc/grpc-js';

@CommandHandler(SsoLoginCommand)
export class SsoLoginHandler implements ICommandHandler<SsoLoginCommand> {
  constructor(
    private jwtService: JwtService,
    private config: ConfigService,
    private prisma: PrismaService,
    private authGrpcService: AuthGrpcService,
  ) { }

  async execute(command: SsoLoginCommand) {
    const { user, args, session } = command;

    const currentUser = await this.prisma.user.findUnique({
      where: { id: user.id },
    });

    try {
      const res = await this.authGrpcService
        .generateUserToken({
          userId: currentUser.id,
          propertyId: args.propertyId,
          pass: currentUser.password,
          ip: session.ip,
          agent: session.media,
        })
        .toPromise();

      return { access_token: res.accessToken, refresh_token: res.refreshToken };
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException(err.message || 'Resource not found');
      }
      if (err.code === status.PERMISSION_DENIED) {
        throw new UnauthorizedException();
      }
      throw new InternalServerErrorException('Internal Server Error');
    }
  }
}
