import { ForbiddenException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { JwtService } from '@nestjs/jwt';
import { createHash } from 'crypto';
import { AuthModel } from '../../models/auth.model';
import { RefreshTokenCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(RefreshTokenCommand)
export class RefreshTokenHandler
  implements ICommandHandler<RefreshTokenCommand>
{
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private config: ConfigService,
    private publisher: EventPublisher,
  ) {}

  async execute(command: RefreshTokenCommand) {
    const { userId, refreshToken } = command;

    const item = await this.prisma.user.findFirst({ where: { id: userId } });
    const encodeRefreshToken = createHash('sha256')
      .update(refreshToken)
      .digest('base64');

    if (!item || !item.hashRt || encodeRefreshToken != item.hashRt) {
      throw new ForbiddenException();
    }

    try {
      const [atToken, rtToken] = await Promise.all([
        this.jwtService.signAsync(
          { sub: item.id, username: item.username },
          { secret: this.config.get<string>('AT_SECRET'), expiresIn: '24h' },
        ),
        this.jwtService.signAsync(
          { sub: item.id, username: item.username },
          { secret: this.config.get<string>('RT_SECRET'), expiresIn: '7d' },
        ),
      ]);

      const hashRt = createHash('sha256').update(rtToken).digest('base64');
      const hashAt = createHash('sha256').update(atToken).digest('base64');
      const authModel = this.publisher.mergeClassContext(AuthModel);
      const auth = new authModel(item.id);
      auth.updatedHashRt(hashRt);
      auth.updateSession(hashAt)

      return { access_token: atToken, refresh_token: rtToken };
    } catch (error) {
      return 'failed refreshed token';
    }
  }
}
