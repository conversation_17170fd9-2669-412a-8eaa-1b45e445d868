import { DeleteChildCommand } from '../impl';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(DeleteChildCommand)
export class DeleteChildHandler implements ICommandHandler<DeleteChildCommand> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeleteChildCommand) {
    const { id, user } = command;

    // Start a transaction
    const transaction = await this.prisma.$transaction(async (prisma) => {
      const userProperty = await prisma.userProperty.findFirst({
        where: {
          user: { parentId: user.id },
          propertyId: id,
        },
      });

      if (!userProperty) {
        throw new Error('User property not found');
      }

      await prisma.userProperty.delete({
        where: {
          id: userProperty.id,
        },
      });

      await prisma.user.delete({
        where: {
          id: userProperty.userId,
        },
      });

      return 'Operator user deleted successfully';
    });

    return transaction;
  }
}
