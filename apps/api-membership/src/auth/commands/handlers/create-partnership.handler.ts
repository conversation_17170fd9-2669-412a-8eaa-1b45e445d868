import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Publisher,
  ICommandHandler,
} from '@nestjs/cqrs';
import { CreatePartnershipCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { BadRequestException } from '@nestjs/common';
import { generateUsername } from '@app/common';

@CommandHandler(CreatePartnershipCommand)
export class CreatePartnershipHandler
  implements ICommandHandler<CreatePartnershipCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly config: ConfigService,
    private publisher: EventPublisher,
    private commandBus: CommandBus,
  ) {}

  async execute(command: CreatePartnershipCommand) {
    const { args } = command;
    const password = bcrypt.hashSync(args.password, 10);

    if (args.password !== args.passwordConfirmation) {
      throw new BadRequestException("password doesn't match!");
    }

    return await this.prisma.user.create({
      data: {
        email: args.email,
        username: generateUsername(args.firstName, '', 4),
        provider: 'membership',
        mobileNumber: args.mobileNumber,
        password: password,
        status: 'Active',
        activationAt: new Date(),
        isActive: true,
        businessType: 'Partnership',
        profile: {
          create: {
            firstName: args.firstName,
            lastName: args.lastName,
            address: args.address,
          },
        },
      },
    });
  }
}
