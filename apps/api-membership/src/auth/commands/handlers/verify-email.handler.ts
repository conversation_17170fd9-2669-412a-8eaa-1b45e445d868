import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { VerifyEmailCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { BadRequestException } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { join } from 'path';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

@CommandHandler(VerifyEmailCommand)
export class VerifyEmailHandler implements ICommandHandler<VerifyEmailCommand> {
  constructor(
    private prisma: PrismaService,
    private readonly mailerService: MailerService,
    private readonly config: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  async execute(command: VerifyEmailCommand) {
    const { args } = command;
    const user = await this.prisma.user.findFirst({
      where: {
        email: {
          equals: args.email,
          mode: 'insensitive',
        },
      },
    });

    if (user.status == 'active') {
      throw new BadRequestException('Email already verified');
    }

    const token = await this.jwtService.signAsync(
      { sub: user.id.toString(), email: user.email },
      { secret: this.config.get<string>('AT_SECRET'), expiresIn: '24h' },
    );

    await this.prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        verificationToken: token,
      },
    });

    const year = new Date().getFullYear();
    // const verificationLink = `${this.config.get<string>('CLIENT_REDIRECT_URL')}/verify-email?token=${token}`;
    const verificationLink = `${this.config.get<string>('CLIENT_REDIRECT_URL')}/verify-email?email=${user.email}&token=${token}`;

    await this.mailerService.sendMail({
      to: args.email,
      subject: 'Email Verification',
      template: 'confirm-registration',
      context: {
        verificationLink: verificationLink,
        year: year,
      },
    });

    return 'Email verification sent';
  }
}
