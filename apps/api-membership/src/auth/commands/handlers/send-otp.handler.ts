import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { SendOtpCodeCommand } from '../impl';
import { MailerService } from '@nestjs-modules/mailer';
import { PrismaService } from 'nestjs-prisma';
import { randomNumber } from '@app/common';
import { ConfigService } from '@nestjs/config';

@CommandHandler(SendOtpCodeCommand)
export class SendOtpCodeHandler implements ICommandHandler<SendOtpCodeCommand> {
  constructor(
    private readonly mailerService: MailerService,
    private readonly prisma: PrismaService,
    private readonly config: ConfigService,
  ) {}

  async execute(command: SendOtpCodeCommand) {
    const { args } = command;

    const codeOtp = randomNumber(4).toString();
    const year = new Date().getFullYear();
    const otpExpirationTime = addMinutes(new Date(), 5);
    const otpDigits = codeOtp.split('');

    const dataUser = await this.prisma.user.findFirst({
      where: {
        email: {
          equals: args.email,
          mode: 'insensitive',
        },
      },
    });

    if (dataUser) {
      // const url = `${this.config.get<string>('CLIENT_REDIRECT_URL')}/recovery-sent?email=${args.email}`;

      await this.mailerService.sendMail({
        to: args.email,
        subject: 'OTP Verification Code',
        template: 'reset-password',
        context: {
          // url: url,
          codeOtpDigits: otpDigits,
          year: year,
        },
      });

      await this.prisma.user.update({
        where: { id: dataUser.id },
        data: {
          resetPasswordCode: codeOtp,
          resetPasswordCodeExpiresAt: otpExpirationTime,
        },
      });
    }

    return { email: dataUser.email };
  }
}

function addMinutes(date: Date, minutes: number): Date {
  return new Date(date.getTime() + minutes * 60000);
}
