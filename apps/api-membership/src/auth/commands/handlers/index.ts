import { ChangePasswordHandler } from './change-password.handler';
import { <PERSON>reate<PERSON>serHandler } from './create-user.handler';
import { Do<PERSON>oginHandler } from './dologin.handler';
import { Do<PERSON>ogoutHandler } from './dologout.handler';
import { ForgotPasswordHandler } from './forgot-password.handler';
import { RefreshTokenHandler } from './refresh-token.handler';
import { SendOtpCodeHandler } from './send-otp.handler';
import { SsoLoginHandler } from './sso-login.handler';
import { UpdateBusinessTypeHandler } from './update-business-type.handler';
import { UpdateUserHandler } from './update-user.handler';
import { VerifyEmailHandler } from './verify-email.handler';
import { VerifyOtpCodeHandler } from './verify-otp.handler';
import { CreateChildHandler } from './create-child.handler';
import { CreatePartnershipHandler } from './create-partnership.handler';
import { Delete<PERSON>hildHandler } from './delete-child.handler';

export const UserCommandHandlers = [
  <PERSON>reate<PERSON><PERSON><PERSON>and<PERSON>,
  DoLoginHandler,
  DoLogoutHandler,
  VerifyEmailHandler,
  SendOtpCodeHandler,
  VerifyOtpCodeHandler,
  ForgotPasswordHandler,
  ChangePasswordHandler,
  RefreshTokenHandler,
  UpdateUserHandler,
  UpdateBusinessTypeHandler,
  SsoLoginHandler,
  CreateChildHandler,
  CreatePartnershipHandler,
  DeleteChildHandler,
];
