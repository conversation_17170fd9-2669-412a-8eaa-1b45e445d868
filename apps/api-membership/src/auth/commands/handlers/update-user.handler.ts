import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from "@nestjs/cqrs";
import { UpdateUserCommand } from "../impl/update-user.command";
import { PrismaService } from "nestjs-prisma";

@CommandHandler(UpdateUserCommand)
export class UpdateUserHandler implements ICommandHandler<UpdateUserCommand> {
  constructor(private prisma: PrismaService) {

  }

  async execute(command: UpdateUserCommand) {
    const { user, args } = command;
    try {
      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          email: args.email,
          mobileNumber: args.mobileNumber,
          profile: {
            update: {
              firstName: args.firstName,
              lastName: args.lastName,
              address: args.address,
              placeOfBirth: args.placeOfBirth,
              dateOfBirth: args.dateOfBirth
                ? new Date(args.dateOfBirth)
                : undefined,
              gender: args.gender,
            },
          },
        },
      });
      return 'successfully updated profile';
    } catch (error) {
      return 'failed updated profile';
    }
  }
}