import {
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import * as bcrypt from 'bcrypt';
import { PrismaService } from 'nestjs-prisma';
import { ForgotPasswordCommand } from '../impl';

@CommandHandler(ForgotPasswordCommand)
export class ForgotPasswordHandler
  implements ICommandHandler<ForgotPasswordCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: ForgotPasswordCommand) {
    const { args } = command;

    const dataUser = await this.prisma.user.findFirst({
      where: { email: { equals: args.email, mode: 'insensitive' } },
    });

    const currentTime = new Date();
    if (
      dataUser.resetPasswordCodeExpiresAt < currentTime ||
      !dataUser.resetPasswordCode
    ) {
      throw new BadRequestException('your otp code has expired!');
    }

    if (dataUser.resetPasswordCode !== args.otp) {
      throw new BadRequestException('Invalid OTP code');
    }

    if (bcrypt.compareSync(args.new_password, dataUser.password)) {
      throw new BadRequestException('New password cannot same as old password');
    }

    if (args.new_password !== args.passwordConfirmation) {
      throw new BadRequestException("password doesn't match!");
    }

    try {
      await this.prisma.user.update({
        where: { id: dataUser.id },
        data: {
          password: bcrypt.hashSync(args.new_password, 10),
          resetPasswordCode: null,
          resetPasswordCodeExpiresAt: null,
        },
      });

      await this.prisma.user.update({
        where: {
          id: dataUser.id,
        },
        data: {
          hashRt: null,
        },
      });

      const session = await this.prisma.userSession.findFirst({
        where: {
          AND: [
            {
              userId: dataUser.id,
            },
            {
              isActive: true,
            },
          ],
        },
      });

      if (session) {
        await this.prisma.userSession.update({
          where: {
            id: session.id,
          },
          data: {
            isActive: false,
            hashAt: null,
          },
        });
      }

      return 'success reset password';
    } catch (error) {
      console.error(error);
      throw new BadRequestException('Failed to reset password');
    }
  }
}
