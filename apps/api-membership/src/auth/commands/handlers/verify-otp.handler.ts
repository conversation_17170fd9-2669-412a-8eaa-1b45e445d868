import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>andler } from '@nestjs/cqrs';
import { VerifyOtpCodeCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import {
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';

@CommandHandler(VerifyOtpCodeCommand)
export class VerifyOtpCodeHandler
  implements ICommandHandler<VerifyOtpCodeCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: VerifyOtpCodeCommand) {
    const { args } = command;

    const dataUser = await this.prisma.user.findFirst({
      where: {
        email: {
          equals: args.email,
          mode: 'insensitive',
        },
      },
    });

    if (!dataUser) {
      throw new NotFoundException('User not found!');
    }

    if (
      dataUser.resetPasswordCodeExpiresAt &&
      dataUser.resetPasswordCodeExpiresAt < new Date()
    ) {
      throw new UnauthorizedException('Your OTP code has expired!');
    }

    if (dataUser.resetPasswordCode === args.otp) {
      return {
        data: { email: dataUser.email, otp: dataUser.resetPasswordCode },
      };
    }

    throw new UnauthorizedException('OTP not correct!');
  }
}
