import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { CreateUserChildCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { generateUsername } from '@app/common';
import { createHash } from 'crypto';
import { AuthModel } from '../../models/auth.model';

@CommandHandler(CreateUserChildCommand)
export class CreateChildHandler
  implements ICommandHandler<CreateUserChildCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly config: ConfigService,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(command: CreateUserChildCommand) {
    const { id, currentUser, args } = command;

    const password = bcrypt.hashSync(args.password, 10);

    if (args.password !== args.passwordConfirmation) {
      throw new BadRequestException("password doesn't match!");
    }

    const user = await this.prisma.user.create({
      data: {
        email: args.email,
        username: generateUsername(args.firstName, '', 4),
        provider: 'membership',
        mobileNumber: args.mobileNumber,
        password: password,
        status: 'Active',
        activationAt: new Date(),
        isActive: true,
        businessType: 'Single',
        parent: { connect: { id: currentUser.id } },
        profile: {
          create: {
            firstName: args.firstName,
            lastName: args.lastName,
            gender: args.gender,
            dateOfBirth: new Date(args.dateOfBirth).toISOString(),
            address: args.address,
          },
        },
      },
    });

    await this.prisma.userProperty.create({
      data: {
        userId: user.id,
        propertyId: id,
        isDefault: true,
      },
    });

    // const authModel = this.publisher.mergeClassContext(AuthModel);
    // const auth = new authModel(user.id);
    // auth.updatedHashRt(hashRt);
    // auth.createSession(session);
    // auth.sendVerifyEmail(user.email);

    return 'Successfully created new user operator';
  }
}
