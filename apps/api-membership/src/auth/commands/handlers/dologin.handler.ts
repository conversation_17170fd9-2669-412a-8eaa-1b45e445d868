import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { DoLoginCommand } from '../impl';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'nestjs-prisma';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { createHash } from 'crypto';
import { AuthModel } from '../../models/auth.model';
import addMinutes from '@app/common/helpers/minutes.helper';

@CommandHandler(DoLoginCommand)
export class <PERSON><PERSON><PERSON>in<PERSON>and<PERSON> implements ICommandHandler<DoLoginCommand> {
  constructor(
    private jwtService: JwtService,
    private config: ConfigService,
    private prisma: PrismaService,
    private publisher: EventPublisher,
    // private publisher: EventPublisher,
    // private webGateway: WebGatewa
  ) {}

  async execute(command: DoLoginCommand) {
    const { doLogin, session } = command;

    const item = await this.prisma.user.findFirst({
      where: {
        AND: [
          {
            OR: [
              { email: { equals: doLogin.username, mode: 'insensitive' } },
              { mobileNumber: doLogin.username },
            ],
          },
        ],
      },
    });

    if (!item) {
      throw new BadRequestException('email not found');
    }

    // Check if user is admin
    if (!item.isAdmin) {
      throw new UnauthorizedException(
        'Access denied. Only admin users are allowed to login.',
      );
    }

    if (item.failedLoginAttempts >= 3 && item.lastFailedLoginAt) {
      const freezeUntil = addMinutes(item.lastFailedLoginAt, 1);
      if (new Date() < freezeUntil) {
        throw new UnauthorizedException(
          'Account is temporarily locked due to too many failed login attempts. Please try again later.',
        );
      }
    }

    if (!bcrypt.compareSync(doLogin.password, item.password)) {
      await this.prisma.user.update({
        where: { id: item.id },
        data: {
          failedLoginAttempts: item.failedLoginAttempts + 1,
          lastFailedLoginAt: new Date(),
        },
      });
      throw new BadRequestException(`Password didn't match`);
    }

    await this.prisma.user.update({
      where: { id: item.id },
      data: {
        failedLoginAttempts: 0,
        lastFailedLoginAt: null,
      },
    });

    try {
      const [atToken, rtToken] = await Promise.all([
        this.jwtService.signAsync(
          { sub: item.id.toString(), email: item.email },
          { secret: this.config.get<string>('AT_SECRET'), expiresIn: '24h' },
        ),
        this.jwtService.signAsync(
          { sub: item.id.toString(), email: item.email },
          { secret: this.config.get<string>('RT_SECRET'), expiresIn: '7d' },
        ),
      ]);

      const hashRt = createHash('sha256').update(rtToken).digest('base64');
      session.hashAt = createHash('sha256').update(atToken).digest('base64');

      const userSession = await this.prisma.userSession.findFirst({
        where: {
          AND: [
            {
              isActive: true,
            },
            {
              user: {
                id: item.id,
              },
            },
          ],
        },
      });

      if (!userSession) {
        const authModel = this.publisher.mergeClassContext(AuthModel);

        const auth = new authModel(item.id);
        auth.updatedHashRt(hashRt);
        auth.createSession(session);

        return { access_token: atToken, refresh_token: rtToken };
      }

      const authModel = this.publisher.mergeClassContext(AuthModel);
      const auth = new authModel(item.id);
      auth.updatedHashRt(hashRt);
      auth.createSession(session);

      return { access_token: atToken, refresh_token: rtToken };
    } catch (error) {
      console.error(error.message);
      return 'failed login';
    }
  }
}
