import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { DoLogoutCommand } from "../impl";
import { PrismaService } from "nestjs-prisma";

@CommandHandler(DoLogoutCommand)
export class <PERSON><PERSON>ogoutHandler implements ICommandHandler<DoLogoutCommand> {
  constructor(
    private prisma: PrismaService
  ) {

  }
  async execute(command: DoLogoutCommand) {
    const { userId, args } = command;
    try {
      await this.prisma.user.update({
        where: {
          id: userId
        },
        data: {
          hashRt: null,
        }
      });

      const session = await this.prisma.userSession.findFirst({
        where: {
          AND: [
            {
              userId: userId
            },
            {
              isActive: true
            },
            {
              media: args.media
            }
          ]
        }
      });

      if (session) {
        await this.prisma.userSession.update({
          where: {
            id: session.id
          },
          data: {
            isActive: false,
            hashAt: null
          }
        });
      }
      return 'success logout';
    } catch (error) {
      return 'failed logout';
    }
  }
}
