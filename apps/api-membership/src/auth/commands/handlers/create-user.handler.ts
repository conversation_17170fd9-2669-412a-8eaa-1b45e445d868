import {
  <PERSON><PERSON><PERSON>,
  Command<PERSON><PERSON><PERSON>,
  EventPublisher,
  ICommandHandler,
} from '@nestjs/cqrs';
import { CreateUserCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { dayjs, generateUsername } from '@app/common';
import { BadRequestException } from '@nestjs/common';
import { AuthModel } from '../../models/auth.model';
import { createHash } from 'crypto';

@CommandHandler(CreateUserCommand)
export class CreateUserHandler implements ICommandHandler<CreateUserCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly config: ConfigService,
    private publisher: EventPublisher,
    private commandBus: CommandBus,
  ) {}

  async execute(command: CreateUserCommand): Promise<any> {
    const { args, session } = command;
    const password = bcrypt.hashSync(args.password, 10);

    if (args.password !== args.passwordConfirmation) {
      throw new BadRequestException("password doesn't match!");
    }

    const dob = dayjs(args.dateOfBirth, 'YYYY-MM-DD', true);
    if (!dob.isValid()) {
      throw new BadRequestException('Invalid date format. Use YYYY-MM-DD');
    }

    const minAgeDate = dayjs().subtract(17, 'year');
    const maxAgeDate = dayjs().subtract(150, 'year');

    if (dob.isAfter(minAgeDate)) {
      throw new BadRequestException('You must be at least 17 years old');
    }

    if (dob.isBefore(maxAgeDate)) {
      throw new BadRequestException(
        'Date of birth cannot be more than 150 years ago',
      );
    }

    const businessType = args.businessType || 'Single';

    const user = await this.prisma.user.create({
      data: {
        email: args.email,
        username: generateUsername(args.firstName, '', 4),
        provider: 'membership',
        mobileNumber: args.mobileNumber || '',
        password: password,
        status: 'inActive',
        isActive: true,
        isAdmin: true,
        businessType: businessType,
        profile: {
          create: {
            firstName: args.firstName,
            lastName: args.lastName,
            gender: args.gender,
            dateOfBirth: new Date(args.dateOfBirth).toISOString(),
            address: args.address,
          },
        },
      },
    });

    const [atToken, rtToken] = await Promise.all([
      this.jwtService.signAsync(
        { sub: user.id.toString(), email: user.email },
        { secret: this.config.get<string>('AT_SECRET'), expiresIn: '24h' },
      ),
      this.jwtService.signAsync(
        { sub: user.id.toString(), email: user.email },
        { secret: this.config.get<string>('RT_SECRET'), expiresIn: '7d' },
      ),
    ]);

    const hashRt = createHash('sha256').update(rtToken).digest('base64');
    session.hashAt = createHash('sha256').update(atToken).digest('base64');

    const authModel = this.publisher.mergeClassContext(AuthModel);
    const auth = new authModel(user.id);
    auth.updatedHashRt(hashRt);
    auth.createSession(session);
    auth.sendVerifyEmail(user.email);

    return { access_token: atToken, refresh_token: rtToken };
  }
}
