import {
  BadRequestException,
  HttpStatus,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import * as bcrypt from 'bcrypt';
import { PrismaService } from 'nestjs-prisma';
import { ChangePasswordCommand } from '../impl';
import { MailerService } from '@nestjs-modules/mailer';

@CommandHandler(ChangePasswordCommand)
export class ChangePasswordHandler
  implements ICommandHandler<ChangePasswordCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly mailerService: MailerService,
  ) {}

  async execute(command: ChangePasswordCommand) {
    const { user, args } = command;

    const dataUser = await this.prisma.user.findFirst({
      where: { id: user.id },
    });

    if (!dataUser) {
      throw new NotFoundException();
    }

    if (!bcrypt.compareSync(args.password_lama, dataUser.password)) {
      throw new UnauthorizedException('Old password is incorrect!');
    }

    if (args.password_baru === args.password_lama) {
      throw new BadRequestException('New password cannot same as old password');
    }

    if (args.password_baru !== args.konfirmasi_password_baru) {
      throw new BadRequestException(
        'New password and confirmation doesnt match!',
      );
    }

    const password = bcrypt.hashSync(args.password_baru, 10);
    const year = new Date().getFullYear();

    if (dataUser && bcrypt.compareSync(args.password_lama, dataUser.password)) {
      await this.prisma.user.update({
        where: { id: dataUser.id },
        data: { password: password },
      });

      await this.mailerService.sendMail({
        to: dataUser.email,
        subject: 'Change Password Success',
        template: 'password-changed',
        context: {
          year: year,
        },
      });

      return {
        statusCode: HttpStatus.CREATED,
        message: 'successfully updated password',
      };
    }
    return 'failed change password!';
  }
}
