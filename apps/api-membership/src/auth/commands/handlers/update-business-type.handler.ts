import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { UpdateBusinessTypeCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import {
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { BusinessType } from '../../dto/register.dto';

@CommandHandler(UpdateBusinessTypeCommand)
export class UpdateBusinessTypeHandler
  implements ICommandHandler<UpdateBusinessTypeCommand>
{
  constructor(private readonly prisma: PrismaService) {}
  async execute(command: UpdateBusinessTypeCommand) {
    const { user, args } = command;

    const item = await this.prisma.user.findFirst({
      where: {
        AND: [
          {
            id: user.id,
          },
          {
            businessType: null,
          },
        ],
      },
    });

    if (!item) {
      throw new NotFoundException();
    }

    const updateData = {
      businessType: args.businessType,
      isAdmin:
        args.businessType === BusinessType.MULTIPLE ? true : item.isAdmin,
    };

    try {
      await this.prisma.user.update({
        where: {
          id: item.id,
        },
        data: updateData,
      });

      return 'success update business type';
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }
}
