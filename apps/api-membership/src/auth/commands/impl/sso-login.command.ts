import { ICommand } from "@nestjs/cqrs";
import { LoggedSessionCreate } from "../../types";
import { SsoLoginDto } from "../../dto/sso-login.dto";
import { ICurrentUser } from "../../strategies/types/user.type";

export class SsoLoginCommand implements ICommand {
  constructor(
    public readonly user: ICurrentUser,
    public readonly args: SsoLoginDto,
    public readonly session: LoggedSessionCreate
  ) {

  }
}