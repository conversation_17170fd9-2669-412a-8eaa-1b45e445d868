import { Api<PERSON>aram, ApiTags } from '@nestjs/swagger';
import {
  Controller,
  Get,
  NotFoundException,
  Param,
  Query,
} from '@nestjs/common';
import { PartnershipGrpcService } from './service/partnership.grpc.service';
import { Query as CommonQuery, Id } from '@app/proto-schema/index.common';
import { ListPartnershipResponse } from '@app/proto-schema/internal-proto/partnership';
import { status } from '@grpc/grpc-js';
import { PartnershipPropertiesDto } from './dto/partnership-properties.dto';

@ApiTags('Partnership')
@Controller('partnership')
export class PartnershipRpcController {
  constructor(private partnershipService: PartnershipGrpcService) {}

  @Get()
  async listPartnership(
    @Query() query: CommonQuery,
  ): Promise<ListPartnershipResponse> {
    return await this.partnershipService.listPartnership(query).toPromise();
  }

  @Get(':id')
  @ApiParam({ name: 'id' })
  async getPartnership(@Param('id') id: string) {
    const requestId: Id = { id };
    try {
      return await this.partnershipService
        .getPartnership(requestId)
        .toPromise();
    } catch (err) {
      if ((err.code = status.NOT_FOUND)) {
        throw new NotFoundException();
      }
    }
  }

  @Get(':id/properties')
  @ApiParam({ name: 'id', description: 'Partnership ID' })
  async listPartnershipProperties(
    @Param('id') id: string,
    @Query() queryParams: PartnershipPropertiesDto,
  ) {
    try {
      const { search, page, limit } = queryParams;

      const response = await this.partnershipService
        .listPartnershipPropertiesResponse(
          id,
          search,
          page as unknown as string,
          limit as unknown as string,
        )
        .toPromise();

      return {
        data: response.data.map(
          ({ createdAt, updatedAt, configuration, contactPerson, ...rest }) =>
            rest,
        ),
        meta: response.meta,
      };
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException();
      }
    }
  }
}
