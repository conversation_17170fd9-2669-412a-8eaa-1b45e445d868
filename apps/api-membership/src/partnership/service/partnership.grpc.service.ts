import { Inject, Injectable } from '@nestjs/common';
import {
  ListPartnershipPropertiesRequest,
  ListPartnershipPropertiesResponse,
  ListPartnershipResponse,
  Partnership,
  PARTNERSHIP_SERVICE_NAME,
  PartnershipServiceClient,
} from '@app/proto-schema/internal-proto/partnership';
import { ClientGrpc } from '@nestjs/microservices';
import { Id, Query } from '@app/proto-schema/common-proto/common';
import { Metadata } from '@grpc/grpc-js';
import { Observable } from 'rxjs';

@Injectable()
export class PartnershipGrpcService {
  private PartnershipService: PartnershipServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.PartnershipService = this.client.getService<PartnershipServiceClient>(
      PARTNERSHIP_SERVICE_NAME,
    );
  }

  listPartnership(request: Query): Observable<ListPartnershipResponse> {
    const meta = new Metadata();
    return this.PartnershipService.listPartnership(request, meta);
  }

  getPartnership(request: Id): Observable<Partnership> {
    const meta = new Metadata();
    return this.PartnershipService.getPartnership(request, meta);
  }

  listPartnershipPropertiesResponse(
    partnershipId: string,
    search?: string,
    page?: string,
    limit?: string,
  ): Observable<ListPartnershipPropertiesResponse> {
    const meta = new Metadata();

    const request: ListPartnershipPropertiesRequest = {
      query: {
        query: search || '',
        params: {
          page: page || '1',
          limit: limit || '10',
        },
      } as unknown as Query,
      partnershipId: partnershipId,
    };
    console.log(request);

    return this.PartnershipService.listPartnershipProperties(request, meta);
  }
}
