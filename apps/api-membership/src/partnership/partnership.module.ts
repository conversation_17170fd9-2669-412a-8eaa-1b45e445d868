import { ClientsModule } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { INTERNAL_PACKAGE, InternalClient } from '../../../../libs/clients';
import { PartnershipRpcController } from './partnership-rpc.controller';
import { PartnershipGrpcService } from './service/partnership.grpc.service';
import { Module } from '@nestjs/common';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) =>
          InternalClient(configService),
        name: INTERNAL_PACKAGE,
      },
    ]),
  ],
  controllers: [PartnershipRpcController],
  providers: [PartnershipGrpcService],
  exports: [PartnershipGrpcService],
})
export class PartnershipModule {}
