import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { ActivatePropertyCommand, SuspendPropertyCommand } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { status } from "@grpc/grpc-js";
import { RpcException } from "@nestjs/microservices";

@CommandHandler(ActivatePropertyCommand)
export class ActivatePropertyHandler implements ICommandHandler<ActivatePropertyCommand> {
  constructor(
    private readonly prisma: PrismaService,
  ) { }

  async execute(command: ActivatePropertyCommand) {
    const { propertyId } = command


    try {
      const property = await this.prisma.property.findUnique({
        where: { id: propertyId },
        include: { users: { include: { user: true } } },
      });

      if (!property) {
        throw new RpcException({ code: status.NOT_FOUND, message: 'Property not found' });
      }


      await this.prisma.$transaction(async (tr) => {
        await tr.property.update({
          where: { id: propertyId },
          data: { status: 'active' },
        });

        for (const userProperty of property.users) {
          await tr.user.update({
            where: { id: userProperty.user.id },
            data: { status: 'active' },
          });
        }
      });

      // TODO : send notification

      return {
        code: status.OK,
        message: 'Property activated successfully',
      };
    } catch (error) {
      if (error instanceof RpcException) {
        throw error;
      }
      throw new RpcException({ code: status.INTERNAL, message: 'Failed to activate property' });
    }
  }
}