import { <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ExtendTempLicenseCommand } from '../impl';
import { IntegrationDlpService } from '../../../integration/services';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PrismaService } from 'nestjs-prisma';
import { Id } from '@app/proto-schema/common-proto/common';
import { lastValueFrom } from 'rxjs';
import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { CustomFieldBody } from '../../dto/create-property.dto';
import { validate } from 'class-validator';
import { JsonValue } from '@prisma/client/runtime/library';

@CommandHandler(ExtendTempLicenseCommand)
export class ExtendTempLicenseHandler {
  constructor(
    private readonly prisma: PrismaService,
    private readonly integrationService: IntegrationDlpService,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
  ) {}

  async execute(command: ExtendTempLicenseCommand) {
    const { args, user } = command;
    let payload = {};

    const userCurrent = await this.prisma.user.findFirst({
      where: { id: user.id },
      include: {
        profile: true,
      },
    });

    const property = await this.prisma.property.findUnique({
      where: {
        id: args.propertyId,
      },
      include: { configuration: { select: { options: true } } },
    });

    if (property.configuration.options['extendedTemp']) {
      throw new BadRequestException('license already extended');
    }

    const propertyTypeRequest: Id = {
      id: property.configuration.options['industryPlan'],
    };
    const propertyTypeChild = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType(propertyTypeRequest),
    );

    const propertyType = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType({
        id: propertyTypeChild.parentId,
      }),
    );

    const categoryCode = await lastValueFrom(
      this.propertyTypeGrpc.getPropertySubLicense({
        id: propertyTypeChild.parentId,
        code: propertyTypeChild.categoryCode,
      }),
    );

    if (!categoryCode?.data?.[0]?.categoryCode) {
      throw new BadRequestException('Invalid category code from property type');
    }

    const codeLmkn = await this.integrationService.DetailCategoryByCode(
      categoryCode.data[0].categoryCode,
    );

    if (!codeLmkn) {
      throw new BadRequestException(
        `Category with code ${categoryCode.data[0].categoryCode} not found in DLP system`,
      );
    }

    let customField: CustomFieldBody | undefined;
    if (args.customFields) {
      customField = new CustomFieldBody();
      customField.id = codeLmkn.id;
      customField.type = codeLmkn.type;
      customField.value = args.customFields.value;

      const errors = await validate(customField);
      if (errors.length > 0) {
        throw new BadRequestException('Invalid custom fields');
      }
    }
    try {
      payload = {
        email: userCurrent.email,
        phoneNumber: userCurrent.mobileNumber,
        firstName: userCurrent.profile.firstName,
        lastName: userCurrent.profile.lastName,
        dateOfBirth:
          userCurrent.profile.dateOfBirth instanceof Date
            ? userCurrent.profile.dateOfBirth.toISOString().split('T')[0]
            : '',
        gender: userCurrent.profile.gender,
        unit: String(customField.value),
        placeOfBirth: userCurrent.profile.placeOfBirth,
        address: userCurrent.profile.address || '',
        businessType: userCurrent.businessType,
        categoryCode: propertyTypeChild.categoryCode,
        industry: propertyType.name,
        previousLicenseCode: property.licenseKey,
      };

      const resp = await this.integrationService.requestTemporary(payload);

      await this.prisma.property.update({
        where: { id: args.propertyId },
        data: {
          requestId: resp.requestId,
          configuration: {
            update: {
              options: {
                customfields: customField as unknown as JsonValue,
                categoryId: categoryCode.data[0].id,
                industryPlan: property.configuration.options['industryPlan'],
                extendedTemp: true,
              },
            },
          },
        },
      });

      return resp;
    } catch (error) {
      console.error(error.message);
      throw new InternalServerErrorException(error);
    }
  }
}
