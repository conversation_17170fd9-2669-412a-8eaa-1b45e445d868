import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpdateBillingAddressCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(UpdateBillingAddressCommand)
export class UpdateBillingAddressHandler
  implements ICommandHandler<UpdateBillingAddressCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UpdateBillingAddressCommand) {
    const { id, args } = command;
    try {
      await this.prisma.property.update({
        where: { id: id },
        data: {
          billingAddress: args.billingAddress,
        },
      });
      return 'successfully updated billing address';
    } catch (error) {
      return 'failed updated billing address';
    }
  }
}
