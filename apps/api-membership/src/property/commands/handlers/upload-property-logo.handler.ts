import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { UploadPropertyLogoCommand } from '../impl';

@CommandHandler(UploadPropertyLogoCommand)
export class UploadPropertyLogoHandler
  implements ICommandHandler<UploadPropertyLogoCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UploadPropertyLogoCommand) {
    const { user, args } = command;

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        userId: user.id,
      },
      include: {
        property: {
          include: { media: true },
        },
      },
    });

    if (!userProperty) {
      throw new NotFoundException('Property not found');
    }

    const media = await this.prisma.media.findFirst({
      where: {
        id: args.mediaId,
      },
    });

    if (!media) {
      throw new NotFoundException('Media not found');
    }

    await this.prisma.property.update({
      where: {
        id: userProperty.propertyId,
      },
      data: {
        mediaId: media.id,
      },
    });

    return 'Property logo updated successfully';
  }
}
