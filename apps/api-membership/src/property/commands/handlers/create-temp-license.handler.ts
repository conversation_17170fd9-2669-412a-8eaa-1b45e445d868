import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CreateTempLicenseCommand } from '../impl/create-temp-license.command';
import { PrismaService } from 'nestjs-prisma';
import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { IntegrationDlpService } from '../../../integration/services';
import { lastValueFrom } from 'rxjs';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { Id } from '@app/proto-schema/common-proto/common';
import { CustomFieldBody } from '../../dto/create-property.dto';
import { validate } from 'class-validator';
import { JsonValue } from '@prisma/client/runtime/library';

@CommandHandler(CreateTempLicenseCommand)
export class CreateTempLicenseHandler
  implements ICommandHandler<CreateTempLicenseCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly integrationService: IntegrationDlpService,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
  ) {}

  async execute(command: CreateTempLicenseCommand) {
    const { args, user } = command;
    let payload = {};

    const checkUserProperty = await this.prisma.userProperty.findFirst({
      where: { userId: user.id },
    });

    const userCurrent = await this.prisma.user.findFirst({
      where: { id: user.id },
      include: { profile: true },
    });

    const propertyTypeRequest: Id = { id: args.propertyTypeId };
    const propertyTypeChild = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType(propertyTypeRequest),
    );

    const propertyType = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType({
        id: propertyTypeChild.parentId,
      }),
    );

    const categoryCode = await lastValueFrom(
      this.propertyTypeGrpc.getPropertySubLicense({
        id: propertyTypeChild.parentId,
        code: propertyTypeChild.categoryCode,
      }),
    );

    if (!categoryCode?.data?.[0]?.categoryCode) {
      throw new BadRequestException('Invalid category code from property type');
    }

    const codeLmkn = await this.integrationService.DetailCategoryByCode(
      categoryCode.data[0].categoryCode,
    );

    if (!codeLmkn) {
      throw new BadRequestException(
        `Category with code ${categoryCode.data[0].categoryCode} not found in DLP system`,
      );
    }

    let customField: CustomFieldBody | undefined;
    if (args.customFields) {
      customField = new CustomFieldBody();
      customField.id = codeLmkn.id;
      customField.type = codeLmkn.type;
      customField.value = args.customFields.value;

      const errors = await validate(customField);
      if (errors.length > 0) {
        throw new BadRequestException('Invalid custom fields');
      }
    }

    if (
      customField.value === 0 ||
      (typeof customField.value === 'string' && customField.value === '0')
    ) {
      throw new BadRequestException({
        value: ['customfields value cannot be 0'],
      });
    }

    payload = {
      email: userCurrent.email,
      phoneNumber: userCurrent.mobileNumber,
      firstName: userCurrent.profile.firstName,
      lastName: userCurrent.profile.lastName,
      dateOfBirth:
        userCurrent.profile.dateOfBirth instanceof Date
          ? userCurrent.profile.dateOfBirth.toISOString().split('T')[0]
          : '',
      gender: userCurrent.profile.gender,
      unit: String(customField.value),
      placeOfBirth: userCurrent.profile.placeOfBirth,
      address: userCurrent.profile.address || '',
      businessType: userCurrent.businessType,
      categoryCode: propertyTypeChild.categoryCode,
      industry: propertyType.name,
    };

    const resp = await this.integrationService.requestTemporary(payload);

    try {
      await this.prisma.property.create({
        data: {
          requestId: resp.requestId,
          status: 'draft',
          configuration: {
            create: {
              options: {
                customfields: customField as unknown as JsonValue,
                categoryId: categoryCode.data[0].id,
                industryPlan: args.propertyTypeId,
                licenseType: 'TEMPORARY',
              },
            },
          },
          users: {
            create: {
              userId: user.id,
              isDefault: !checkUserProperty,
            },
          },
        },
      });

      return resp;
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException();
    }
  }
}
