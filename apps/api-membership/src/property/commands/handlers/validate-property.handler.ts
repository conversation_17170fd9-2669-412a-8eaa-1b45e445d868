import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { ValidatePropertyCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from 'apps/api-membership/src/property-type/service/property-type.grpc.service';
import { PostalGrpcService } from 'apps/api-membership/src/postal/service/postal.grpc.service';
import { IntegrationDlpService } from 'apps/api-membership/src/integration/services';
import { JobPositionGrpcService } from 'apps/api-membership/src/internal-client/job-position.service';
import { firstValueFrom } from 'rxjs';
import { Metadata } from '@grpc/grpc-js';
import { BadRequestException } from '@nestjs/common';

@CommandHandler(ValidatePropertyCommand)
export class ValidatePropertyHandler implements ICommandHandler<ValidatePropertyCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
    private readonly postalGrpc: PostalGrpcService,
    private readonly integrationService: IntegrationDlpService,
    private readonly jobPositionGrpcService: JobPositionGrpcService,
  ) {}

  async execute(command: ValidatePropertyCommand) {
    const { args, user } = command;
    const errors: Record<string, string[]> = {};

    if (user?.id) {
      const checkUserProperty = await this.prisma.userProperty.findFirst({
        where: { userId: user.id },
      });
      if (checkUserProperty && user.businessType === 'Single') {
        throw new BadRequestException('Single business type can only have one property')
      }

      const limitProp = await this.prisma.userProperty.findMany({
        where: { userId: user.id },
      });
      if (limitProp.length >= 20) {
        throw new BadRequestException('Limit reached for multiple properties');
      }
    }

    // Validate propertyTypeId if provided
    if (args.propertyTypeId) {
      try {
        const propertyTypeChild = await this.propertyTypeGrpc
          .getOnePropertyType({ id: args.propertyTypeId })
          .toPromise();

        if (!propertyTypeChild) {
          errors.propertyTypeId = ['Property type not found'];
        } else {
          const categoryCode = await this.propertyTypeGrpc
            .getPropertySubLicense({
              id: propertyTypeChild.parentId,
              code: propertyTypeChild.categoryCode,
            })
            .toPromise();

          if (!categoryCode?.data?.[0]?.categoryCode) {
            errors.propertyTypeId = ['Invalid category code from property type'];
          }
        }
      } catch (error) {
        console.log(error);
        errors.propertyTypeId = ['Property type not found'];
      }
    }

    // Validate postal if provided
    if (args.postalId) {
      try {
        const postal = await this.postalGrpc
          .getPostal({ id: args.postalId }, new Metadata())
          .toPromise();
        if (!postal) {
          errors.postalId = ['Postal not found'];
        }
      } catch (error) {
        errors.postalId = ['Postal not found'];
      }
    }

    // Validate license if provided
    if (args.licenseNumber) {
      try {
        const conventionalLicense = await this.integrationService.validateLicense(
          args.licenseNumber
        );
        if (!conventionalLicense.code) {
          errors.licenseNumber = ['Invalid license key'];
        }
      } catch (error) {
        errors.licenseNumber = ['Invalid license key'];
      }
    }

    // Validate custom fields if provided
    if (args.customFields?.id) {
      try {
        const category = await this.integrationService.DetailCategoryById(
          args.customFields.id
        );
        if (args.customFields.type !== 'lupsum') {
          if (category[`${args.customFields.type}`].length === 0) {
            errors.customFields = ['Invalid custom fields'];
          }
        } else {
          if (Object.keys(category[`${args.customFields.type}`]).length === 0) {
            errors.customFields = ['Invalid custom fields'];
          }
        }
      } catch (error) {
        errors.customFields = ['Failed to validate custom fields'];
      }
    }

    // Validate contact person job positions if provided
    if (args.contactPerson?.length > 0) {
      const jobPositionIds = args.contactPerson.map(contact => contact.jobPositionId);
      const uniqueJobPositionIds = new Set(jobPositionIds);
      
      if (uniqueJobPositionIds.size !== jobPositionIds.length) {
        errors.contactPerson = ['Duplicate job positions are not allowed'];
      } else {
        for (const contact of args.contactPerson) {
          try {
            const jobPosition = await firstValueFrom(
              this.jobPositionGrpcService.detailJobPosition({ id: contact.jobPositionId })
            );
            
            if (!jobPosition) {
              errors.contactPerson = [...(errors.contactPerson || []), `Job position ${contact.jobPositionId} not found`];
            }
          } catch (error) {
            errors.contactPerson = [...(errors.contactPerson || []), `Invalid job position: ${contact.jobPositionId}`];
          }
        }
      }
    }

    if (Object.keys(errors).length > 0) {
      throw new BadRequestException(errors);
    }

    return "ok";
  }
}