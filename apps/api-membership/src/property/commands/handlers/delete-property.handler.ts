import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { DeletePropertyCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import {
  InternalServerErrorException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PropertyGrpcService } from 'apps/api-membership/src/internal-client/property.service';
import { status } from '@grpc/grpc-js';
import { lastValueFrom } from 'rxjs';
import { IntegrationDlpService } from '../../../integration/services';

@CommandHandler(DeletePropertyCommand)
export class DeletePropertyHandler
  implements ICommandHandler<DeletePropertyCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly propertyGrpcService: PropertyGrpcService,
    private readonly integrationService: IntegrationDlpService,
  ) {}

  async execute(command: DeletePropertyCommand) {
    const { propertyId, user } = command;

    try {
      const property = await this.prisma.property.findFirst({
        where: {
          AND: [{ users: { some: { userId: user.id } } }, { id: propertyId }],
        },
      });

      if (!property) {
        throw new NotFoundException('Property not found');
      }

      const license = await this.integrationService.validateTemporary(
        property.requestId,
      );

      if (license.status === 'pending') {
        throw new BadRequestException(
          'Cannot delete property with pending license status',
        );
      }

      if (license.status === 'rejected' && property.status === 'draft') {
        await this.prisma.$transaction(async (tr) => {
          await tr.userProperty.deleteMany({
            where: { propertyId: propertyId, userId: user.id },
          });
          await tr.configuration.deleteMany({
            where: { propertyId: propertyId },
          });
          await tr.property.delete({
            where: { id: propertyId },
          });
        });

        return 'Property removed successfully';
      }

      await this.prisma.$transaction(async (tr) => {
        await tr.property.update({
          where: { id: propertyId },
          data: {
            status: 'inActive',
            updatedAt: new Date(),
          },
        });

        await tr.userProperty.updateMany({
          where: { propertyId: propertyId },
          data: {
            deletedAt: new Date(),
            isDefault: false,
            updatedAt: new Date(),
          },
        });
      });

      if (property.status === 'active') {
        await lastValueFrom(
          this.propertyGrpcService.deleteProperty({ id: propertyId }),
        );
      }

      return 'Property successfully marked as inactive';
    } catch (err) {
      console.error(err.message);
      if (
        err instanceof NotFoundException ||
        err instanceof BadRequestException
      ) {
        throw err;
      }
      throw new InternalServerErrorException('Internal Server Error');
    }
  }
}
