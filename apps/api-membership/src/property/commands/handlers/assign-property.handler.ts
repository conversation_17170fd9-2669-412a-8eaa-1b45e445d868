import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { AssignPropertyCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { PropertyGrpcService } from 'apps/api-membership/src/internal-client/property.service';
import { firstValueFrom } from 'rxjs';
import { Prisma } from '@prisma/client';

@CommandHandler(AssignPropertyCommand)
export class AssignPropertyHandler
  implements ICommandHandler<AssignPropertyCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly propertyService: PropertyGrpcService,
  ) {}

  async execute(command: AssignPropertyCommand) {
    const { args } = command;

    try {
      return await this.prisma.$transaction(
        async (prisma) => {
          // Validate user and property existence
          await this.validateUserAndProperty(
            prisma,
            args.userId,
            args.propertyId,
          );

          // Check for existing property assignment
          await this.checkExistingPropertyAssignment(
            prisma,
            args.userId,
            args.propertyId,
          );

          // Determine if this should be a default property
          const isDefault = await this.determineDefaultPropertyStatus(
            prisma,
            args.propertyId,
          );

          // Create user property locally
          const userProperty = await this.createUserProperty(
            prisma,
            args.userId,
            args.propertyId,
            isDefault,
          );

          // Assign property via gRPC
          await this.assignPropertyExternally(args.propertyId, args.userId);

          return 'Property assigned successfully';
        },
        {
          isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
        },
      );
    } catch (error) {
      this.handleAssignmentError(error);
    }
  }

  private async validateUserAndProperty(
    prisma: Prisma.TransactionClient,
    userId: string,
    propertyId: string,
  ): Promise<void> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      throw new NotFoundException('Property not found');
    }
  }

  private async checkExistingPropertyAssignment(
    prisma: Prisma.TransactionClient,
    userId: string,
    propertyId: string,
  ): Promise<void> {
    const existingUserProperty = await prisma.userProperty.findUnique({
      where: {
        userId_propertyId: {
          userId,
          propertyId,
        },
      },
    });

    if (existingUserProperty) {
      throw new BadRequestException(
        'User is already assigned to this property',
      );
    }
  }

  private async determineDefaultPropertyStatus(
    prisma: Prisma.TransactionClient,
    propertyId: string,
  ): Promise<boolean> {
    const isDefaultProperty = await prisma.userProperty.findFirst({
      where: {
        propertyId,
        isDefault: true,
      },
    });

    return !isDefaultProperty;
  }

  private async createUserProperty(
    prisma: Prisma.TransactionClient,
    userId: string,
    propertyId: string,
    isDefault: boolean,
  ) {
    return await prisma.userProperty.create({
      data: {
        userId,
        propertyId,
        isDefault,
      },
      include: {
        property: true,
        user: true,
      },
    });
  }

  private async assignPropertyExternally(
    propertyId: string,
    userId: string,
  ): Promise<void> {
    try {
      const result = await firstValueFrom(
        this.propertyService.assignProperty({
          propertyId,
          userId,
        }),
      );

      if (result.code !== 200) {
        throw new BadRequestException(
          result.message || 'Failed to assign property',
        );
      }
    } catch (error) {
      throw new BadRequestException(
        error.message || 'External property assignment failed',
      );
    }
  }

  private handleAssignmentError(error: any): never {
    throw new BadRequestException(
      error.message || 'Failed to assign property to user',
    );
  }
}
