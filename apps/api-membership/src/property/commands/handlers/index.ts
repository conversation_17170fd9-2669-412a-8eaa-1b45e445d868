import { ActivatePropertyHandler } from './activate-property.handler';
import { AssignPropertyHandler } from './assign-property.handler';
import { CreatePropertyHandler } from './create-property.handler';
import { DeletePropertyHandler } from './delete-property.handler';
import { SetPropertyFlagHandler } from './set-property-flag.handler';
import { SuspendPropertyHandler } from './suspend-property.handler';
import { UploadPropertyLogoHandler } from './upload-property-logo.handler';
import { ValidatePropertyHandler } from './validate-property.handler';
import { CreateTempLicenseHandler } from './create-temp-license.handler';
import { CreateTempValidHandler } from './create-temp-valid.handler';
import { ValidateLicenseTempHandler } from './validate-license-temp.handler';
import { ExtendTempLicenseHandler } from './extend-temp-license.handler';
import { ActivateLicenseHandler } from './activate-license.handler';
import { UpdateBillingAddressHandler } from './update-billing-address.handler';

export const PropertyCommandHandlers = [
  CreatePropertyHandler,
  ActivatePropertyHandler,
  SuspendPropertyHandler,
  UploadPropertyLogoHandler,
  DeletePropertyHandler,
  AssignPropertyHandler,
  SetPropertyFlagHandler,
  ValidatePropertyHandler,
  CreateTempLicenseHandler,
  CreateTempValidHandler,
  ValidateLicenseTempHandler,
  ExtendTempLicenseHandler,
  ActivateLicenseHandler,
  UpdateBillingAddressHandler,
];
