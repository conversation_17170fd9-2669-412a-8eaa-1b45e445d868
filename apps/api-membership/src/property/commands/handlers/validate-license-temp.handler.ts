import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { ValidLicenseTempCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { IntegrationDlpService } from '../../../integration/services';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Id } from '@app/proto-schema/common-proto/common';
import { lastValueFrom } from 'rxjs';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { RequestIdNotFoundException } from '../../../../../../libs/validator/src/customError/not-found';
import { IntegrationDlpQueueService } from '../../../queue';

@CommandHandler(ValidLicenseTempCommand)
export class ValidateLicenseTempHandler
  implements ICommandHandler<ValidLicenseTempCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly integrationService: IntegrationDlpService,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
    private readonly dlpQueueService: IntegrationDlpQueueService,
  ) {}

  async execute(command: ValidLicenseTempCommand) {
    const { args } = command;

    try {
      const property = await this.prisma.property.findFirst({
        where: { requestId: args.requestId },
        include: { configuration: { select: { options: true } } },
      });

      if (!property) {
        throw new NotFoundException();
      }

      const industry = property.configuration.options['industryPlan'];
      const custom = property.configuration.options['customfields'];

      let license: any = null;
      const jobId: string | null = null;

      try {
        license = await this.integrationService.validateTemporary(
          args.requestId,
        );
        console.log('[ValidateTemp] Direct call successful:');
      } catch (error) {
        console.warn(
          '[ValidateTemp] Direct call failed, using queue with retry:',
          error.message,
        );

        license = await this.validateWithQueueAndWait(
          args.requestId,
          property.id,
        );
      }

      if (!license) {
        throw new BadRequestException("['requestId is not found']");
      }

      const propertyTypeRequest: Id = { id: industry };
      const propertyTypeChild = await lastValueFrom(
        this.propertyTypeGrpc.getOnePropertyType(propertyTypeRequest),
      );

      const propertyType = await lastValueFrom(
        this.propertyTypeGrpc.getOnePropertyType({
          id: propertyTypeChild.parentId,
        }),
      );

      return {
        license,
        PropertyTypeParent: {
          id: propertyType.id,
          name: propertyType.name,
        },
        propertyType: {
          id: propertyTypeChild.id,
          name: propertyTypeChild.name,
        },
        customFields: custom,
      };
    } catch (error) {
      if (error.status === 404) {
        if (error.status === 404) {
          throw new RequestIdNotFoundException(args.requestId);
        }
      }
      console.error(error.message);
      throw new InternalServerErrorException();
    }
  }

  private async validateWithQueueAndWait(
    requestId: string,
    propertyId: string,
    maxWaitTime = 30000,
  ): Promise<any> {
    console.log('[ValidateTemp] Starting queue validation with polling...');

    const jobId = await this.dlpQueueService.validateTemporaryWithRetry(
      requestId,
      {
        propertyId,
        operation: 'validate_temporary',
      },
    );

    console.log(`[ValidateTemp] Job queued: ${jobId}, polling for result...`);

    const startTime = Date.now();
    const pollInterval = 1000;

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const jobStatus = await this.dlpQueueService.getJobStatus(jobId);

        if (!jobStatus) {
          throw new BadRequestException(`Job ${jobId} not found in queue`);
        }

        if (jobStatus.finishedOn && !jobStatus.failedReason) {
          console.log(
            `[ValidateTemp] Job completed successfully after ${jobStatus.attemptsMade} attempts`,
          );

          const result = jobStatus.returnvalue;
          if (result?.success && result?.data) {
            return result.data; // Return the actual license data
          } else {
            throw new BadRequestException(
              'Job completed but no valid data returned',
            );
          }
        }

        // Job failed permanently
        if (jobStatus.failedReason) {
          console.error(
            `[ValidateTemp] Job failed permanently: ${jobStatus.failedReason}`,
          );
          throw new BadRequestException(
            `Validation failed: ${jobStatus.failedReason}`,
          );
        }

        // Job still processing or waiting
        if (jobStatus.processedOn) {
          console.log(
            `[ValidateTemp] Job processing... (attempt ${jobStatus.attemptsMade})`,
          );
        } else {
          console.log(`[ValidateTemp] Job waiting in queue...`);
        }

        // Wait before next poll
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      } catch (error) {
        if (error instanceof BadRequestException) {
          throw error; // Re-throw business logic errors
        }
        console.error(`[ValidateTemp] Error polling job status:`, error);
        // Continue polling for other errors
      }
    }

    // Timeout reached
    console.warn(`[ValidateTemp] Polling timeout reached for job ${jobId}`);
    throw new BadRequestException({
      message:
        'License validation is taking longer than expected. Please check back later.',
      jobId,
      trackingUrl: `/queue-management/job/${jobId}`,
      estimatedTime: 'Please try again in 1-2 minutes',
    });
  }
}
