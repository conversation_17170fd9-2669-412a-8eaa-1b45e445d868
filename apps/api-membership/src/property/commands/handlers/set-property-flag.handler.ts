import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { SetPropertyFlagCommand, SuspendPropertyCommand } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { status } from "@grpc/grpc-js";
import { RpcException } from "@nestjs/microservices";

@CommandHandler(SetPropertyFlagCommand)
export class SetPropertyFlagHandler implements ICommandHandler<SetPropertyFlagCommand> {
  constructor(
    private readonly prisma: PrismaService,
  ) { }

  async execute(command: SetPropertyFlagCommand) {
    const { args } = command
    try {
      await this.prisma.property.update({
        where: {
          id: args.id
        },
        data: {
          flag: args.flag
        }
      });
      return {
        code: status.OK,
        message: 'success set property flag'
      };
    } catch (error) {
      console.log(error)
      throw new RpcException({ code: status.INTERNAL, message: 'Failed to suspend property' });
    }
  }
}