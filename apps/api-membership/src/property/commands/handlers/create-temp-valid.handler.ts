import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CreateTempValidCommand } from '../impl/create-temp-valid.command';
import { PrismaService } from 'nestjs-prisma';
import cidGenerator from '@app/common/helpers/cid-generator.helper';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Metadata } from '@grpc/grpc-js';
import { firstValueFrom, lastValueFrom } from 'rxjs';
import { Id } from '@app/proto-schema/common-proto/common';
import { CustomFieldBody } from '../../dto/create-property.dto';
import { validate } from 'class-validator';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';
import { IntegrationDlpService } from '../../../integration/services';
import { JobPositionGrpcService } from '../../../internal-client/job-position.service';

@CommandHandler(CreateTempValidCommand)
export class CreateTempValidHandler
  implements ICommandHandler<CreateTempValidCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
    private readonly postalGrpc: PostalGrpcService,
    private readonly integrationService: IntegrationDlpService,
    private readonly jobPositionGrpcService: JobPositionGrpcService,
  ) {}

  async execute(command: CreateTempValidCommand) {
    const { user, args } = command;

    const checkUserProperty = await this.prisma.userProperty.findFirst({
      where: {
        userId: user.id,
        property: {
          licenseKey: args.licenseKey,
        },
      },
    });

    const limitProp = await this.prisma.userProperty.findMany({
      where: { userId: user.id },
    });

    const validLicense = await this.prisma.property.findFirst({
      where: { id: checkUserProperty.propertyId, licenseKey: args.licenseKey },
      include: { configuration: { select: { options: true } } },
    });

    if (!checkUserProperty) {
      console.error();
      throw new BadRequestException('Invalid License or property not found');
    }

    const customFields = validLicense.configuration.options['customfields'];
    const licenseType = validLicense.configuration.options['licenseType'];
    const propertySub = validLicense.configuration.options['industryPlan'];

    if (customFields && customFields.id) {
      try {
        const category = await this.integrationService.DetailCategoryById(
          customFields.id,
        );
        if (customFields.type !== 'lupsum') {
          if (category[`${customFields.type}`].length == 0) {
            throw new BadRequestException('invalid custom fields');
          }
        } else {
          if (Object.keys(category[`${customFields.type}`]).length === 0) {
            throw new BadRequestException('invalid custom fields');
          }
        }
      } catch (err) {
        console.error(err);
        throw new InternalServerErrorException('Internal Server Error');
      }
    }

    const meta = new Metadata();
    const postal = await lastValueFrom(
      this.postalGrpc.getPostal({ id: args.postalId }, meta),
    );
    if (!postal) {
      throw new NotFoundException('postal not found');
    }

    const propertyTypeRequest: Id = { id: propertySub };

    const propertyTypeChild = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType(propertyTypeRequest),
    );

    const propertyType = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType({
        id: propertyTypeChild.parentId,
      }),
    );

    const categoryCode = await lastValueFrom(
      this.propertyTypeGrpc.getPropertySubLicense({
        id: propertyTypeChild.parentId,
        code: propertyTypeChild.categoryCode,
      }),
    );

    if (!categoryCode?.data?.[0]?.categoryCode) {
      throw new BadRequestException('Invalid category code from property type');
    }

    try {
      const CodeLmkn = await this.integrationService.DetailCategoryByCode(
        categoryCode.data[0].categoryCode,
      );

      if (!CodeLmkn) {
        throw new BadRequestException(
          `Category with code ${categoryCode.data[0].categoryCode} not found in DLP system`,
        );
      }

      const lastRecord = await this.prisma.property.findMany({
        where: { cid: { not: null } },
        orderBy: { createdAt: 'desc' },
        take: 1,
      });

      let baseCid = 1;
      if (lastRecord.length > 0) {
        const cid = lastRecord[0].cid;
        const numericPart = cid.substring(cid.length - 3);
        if (numericPart === 'NaN') {
          baseCid = Math.floor(Math.random() * 1000);
        } else {
          baseCid = Number(numericPart) + 1;
        }
      }

      let customField: CustomFieldBody | undefined;
      if (customFields) {
        customField = new CustomFieldBody();
        customField.id = CodeLmkn.id;
        customField.type = CodeLmkn.type;
        customField.value = customFields.value;

        const errors = await validate(customField);
        if (errors.length > 0) {
          throw new BadRequestException('Invalid custom fields');
        }
      }

      if (args.contactPerson?.length > 0) {
        const jobPositionIds = args.contactPerson.map(
          (contact) => contact.jobPositionId,
        );
        const uniqueJobPositionIds = new Set(jobPositionIds);

        if (uniqueJobPositionIds.size !== jobPositionIds.length) {
          throw new BadRequestException(
            'Duplicate job positions are not allowed',
          );
        }

        for (const contact of args.contactPerson) {
          try {
            const jobPosition = await firstValueFrom(
              this.jobPositionGrpcService.detailJobPosition({
                id: contact.jobPositionId,
              }),
            );

            if (!jobPosition) {
              throw new NotFoundException(
                `Job position with id ${contact.jobPositionId} not found`,
              );
            }
          } catch (error) {
            throw new BadRequestException(
              `Invalid job position: ${contact.jobPositionId}`,
            );
          }
        }
      }

      try {
        const property = await this.prisma.property.update({
          where: { id: validLicense.id },
          data: {
            cid: cidGenerator(propertyType.codeProperty, baseCid.toString()),
            companyName: args.companyName,
            brandName: args.brandName,
            companyEmail: args.companyEmail,
            companyPhoneNumber: args.companyPhoneNumber,
            npwp: args.npwp,
            address: args.address,
            billingAddress: args.billingAddress,
            status: 'active',
            postalId: postal.id,
            propertyTypeId: propertyType.id,
            licenseKey: validLicense.licenseKey,
            ...(args.contactPerson?.length > 0 && {
              contactPerson: {
                create: args.contactPerson.map((contact) => ({
                  firstName: contact.firstName,
                  lastName: contact.lastName,
                  email: contact.email,
                  phone: contact.phone,
                  jobPositionId: contact.jobPositionId,
                })),
              },
            }),
          },
          include: {
            // users: true,
            contactPerson: true,
          },
        });

        return property;
      } catch (error) {
        console.log(error);
        throw new InternalServerErrorException('Failed to create property');
      }
    } catch (error: any) {
      if (error?.status === 404) {
        throw new BadRequestException(
          `Category with code ${categoryCode.data[0].categoryCode} not found in DLP system`,
        );
      }
      console.error(error.message);
      throw new InternalServerErrorException(
        'Failed to fetch category details from DLP system',
      );
    }
  }
}
