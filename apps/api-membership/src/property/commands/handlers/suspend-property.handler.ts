import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { SuspendPropertyCommand } from "../impl";
import { PrismaService } from "nestjs-prisma";
import { status } from "@grpc/grpc-js";
import { RpcException } from "@nestjs/microservices";

@CommandHandler(SuspendPropertyCommand)
export class SuspendPropertyHandler implements ICommandHandler<SuspendPropertyCommand> {
  constructor(
    private readonly prisma: PrismaService,
  ) { }

  async execute(command: SuspendPropertyCommand) {
    const { propertyId } = command

    try {
      const property = await this.prisma.property.findUnique({
        where: { id: propertyId },
        include: { users: { include: { user: true } } },
      });

      if (!property) {
        throw new RpcException({ code: status.NOT_FOUND, message: 'Property not found' });
      }

      if (property.status === 'suspend') {
        throw new RpcException({ code: status.ALREADY_EXISTS, message: 'Property is already suspended' });
      }

      await this.prisma.$transaction(async (tr) => {
        await tr.property.update({
          where: { id: propertyId },
          data: { status: 'suspend' },
        });

        for (const userProperty of property.users) {
          await tr.user.update({
            where: { id: userProperty.user.id },
            data: { status: 'inActive' },
          });
        }
      });

      // TODO : send notification

      return {
        code: status.OK,
        message: 'suspend property success'
      }
    } catch (error) {
      if (error instanceof RpcException) {
        throw error;
      }
      throw new RpcException({ code: status.INTERNAL, message: 'Failed to suspend property' });
    }
  }
}