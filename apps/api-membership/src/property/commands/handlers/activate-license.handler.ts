import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { ActivateLicenseCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { IntegrationDlpService } from '../../../integration/services';
import { lastValueFrom } from 'rxjs';
import { Metadata } from '@grpc/grpc-js';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';

@CommandHandler(ActivateLicenseCommand)
export class ActivateLicenseHandler
  implements ICommandHandler<ActivateLicenseCommand>
{
  constructor(
    public readonly prisma: PrismaService,
    private readonly integrationService: IntegrationDlpService,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
    private readonly postalGrpc: PostalGrpcService,
  ) {}

  async execute(command: ActivateLicenseCommand) {
    const { args, user } = command;

    let payload = {};
    const newReq = Array(32)
      .fill(0)
      .map(() => String.fromCharCode(65 + Math.floor(Math.random() * 26)))
      .join('');

    const property = await this.prisma.property.findFirst({
      where: { requestId: args.requestId },
      include: { configuration: { select: { options: true } } },
    });

    const categoryCode = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType({
        id: property.configuration.options['industryPlan'],
      }),
    );

    const meta = new Metadata();

    const postal = await lastValueFrom(
      this.postalGrpc.getPostal({ id: property.postalId }, meta),
    );

    payload = {
      customer: {
        companyName: property.companyName,
        companyEmail: property.companyEmail,
        brandName: property.brandName,
        postalCode: postal.code,
        categoryCode: categoryCode.categoryCode,
        unit: Number(property.configuration.options['customfields']['value']),
        companyPhoneNumber: property.companyPhoneNumber,
        licenseCode: args.code,
        requestId: newReq,
      },
    };

    await this.integrationService.insertProperty(payload);

    await this.prisma.property.updateMany({
      where: {
        requestId: args.requestId,
      },
      data: {
        requestId: newReq,
      },
    });

    return this.integrationService.activateLicense(args.code, newReq);
  }
}
