import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from '../../../auth/strategies/types/user.type';
import { updateBillingAddressDto } from '../../dto/update-billing-address.dto';

export class UpdateBillingAddressCommand implements ICommand {
  constructor(
    public readonly user: ICurrentUser,
    public readonly id: string,
    public readonly args: updateBillingAddressDto,
  ) {}
}
