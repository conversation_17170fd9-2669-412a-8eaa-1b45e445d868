import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { PropertyController } from './property.controller';
import { PropertyCommandHandlers } from './commands';
import { PostalModule } from '../postal/postal.module';
import { PropertyTypeModule } from '../property-type/property-type.module';
import { PropertyQueryHandlers } from './queries';
import { PropertyRpcController } from './property.rpc.controller';
import { IntegrationModule } from '../integration/integration.module';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { ClientsModule } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { INTERNAL_PACKAGE, InternalClient } from 'libs/clients';
import { QueueModule } from '../queue';

@Module({
  imports: [
    CqrsModule,
    PostalModule,
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) =>
          InternalClient(configService),
        name: INTERNAL_PACKAGE,
      },
    ]),
    PropertyTypeModule,
    InternalClientModule,
    IntegrationModule,
    QueueModule,
  ],
  controllers: [PropertyController, PropertyRpcController],
  providers: [...PropertyCommandHandlers, ...PropertyQueryHandlers],
})
export class PropertyModule {}
