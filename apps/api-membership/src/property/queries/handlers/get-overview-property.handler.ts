import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetOverviewPropertyQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { GetOverviewPropertyResponse } from '@app/proto-schema/index.membership';
import { RpcException } from '@nestjs/microservices';

@QueryHandler(GetOverviewPropertyQuery)
export class GetOverviewPropertyHandler
  implements IQueryHandler<GetOverviewPropertyQuery> {
  constructor(private prisma: PrismaService) { }

  async execute(
    query: GetOverviewPropertyQuery,
  ): Promise<GetOverviewPropertyResponse> {
    try {
      // Count total user owners (admin users without parent)
      const totalUserOwner = await this.prisma.user.count({
        where: {
          isAdmin: true,
          parentId: null
        }
      });

      // Count total active properties
      const totalProperty = await this.prisma.property.count({
        where: {
          status: 'active'
        }
      });

      // Count properties with PAID and DEMO flags
      const paidProperties = await this.prisma.property.count({
        where: {
          flag: 'PAID'
        }
      });

      const demoProperties = await this.prisma.property.count({
        where: {
          flag: 'DEMO'
        }
      });

      // Calculate percentage of PAID vs DEMO properties
      const totalFlaggedProperties = paidProperties + demoProperties;
      const percentageUsedPromoCode = totalFlaggedProperties > 0
        ? Math.round((demoProperties / paidProperties) * 100)
        : 0;

      return {
        data: {
          totalUserOwner,
          totalProperty,
          percentageUsedPromoCode
        }
      };

    } catch (error) {
      throw new RpcException({
        code: 13,
        message: 'Error fetching property overview: ' + (error?.message || 'Unknown error')
      });
    }
  }
}
