import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetOperatorPropertyQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { NotFoundException } from '@nestjs/common';
import { Id } from '@app/proto-schema/common-proto/common';

@QueryHandler(GetOperatorPropertyQuery)
export class GetOperatorHandler
  implements IQueryHandler<GetOperatorPropertyQuery>
{
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
  ) {}

  async execute(query: GetOperatorPropertyQuery) {
    const { user, id } = query;

    const child = await this.prisma.user.findFirst({
      where: { parentId: user.id },
    });

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        userId: child.id,
        propertyId: id,
      },
      include: {
        user: true,
        property: {
          include: {
            media: true,
          },
        },
      },
    });

    if (!userProperty) {
      throw new NotFoundException();
    }

    // const propertyTypeRequest: Id = {
    //   id: userProperty.property.propertyTypeId,
    // };
    // const propertyType = await this.propertyTypeGrpc
    //   .getOnePropertyType(propertyTypeRequest)
    //   .toPromise();

    // const postalRequest: Id = { id: userProperty.property.postalId };
    // const postal = await this.postalGrpc.(postalRequest).toPromise();

    const propertyWithDetails = {
      ...userProperty.property,
      id: userProperty.property.id.toString(),
      // propertyType: {
      //   ...propertyType,
      //   id: propertyType.id.toString(),
      // },
      media: userProperty.property.media,
      userOperator: userProperty.user,
      // postalId
    };

    return propertyWithDetails;
  }
}
