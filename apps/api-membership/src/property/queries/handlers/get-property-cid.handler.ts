import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertyCidQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetPropertyCidQuery)
export class GetPropertyCidHandler
  implements IQueryHandler<GetPropertyCidQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPropertyCidQuery) {
    const { cid } = query;

    const property = await this.prisma.property.findFirst({ where: { cid } });

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        propertyId: property.id,
      },
      include: {
        property: {
          include: {
            media: true,
            configuration: { select: { options: true } },
          },
        },
        user: {
          include: {
            profile: true,
          },
        },
      },
    });

    if (!userProperty) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'property not found',
      });
    }

    const configurationOptions = userProperty.property.configuration?.options;
    const categoryId = configurationOptions['categoryId'] || null;
    const industryPlan = configurationOptions['industryPlan'] || null;

    return {
      property: {
        id: userProperty.property.id,
        cid: userProperty.property.cid,
        companyName: userProperty.property.companyName,
        brandName: userProperty.property.brandName,
        companyEmail: userProperty.property.companyEmail,
        companyPhoneNumber: userProperty.property.companyPhoneNumber,
        npwp: userProperty.property.npwp,
        address: userProperty.property.address,
        postalId: userProperty.property.postalId,
        createdAt: userProperty.property.createdAt.toISOString(),
        updatedAt: userProperty.property.updatedAt.toISOString(),
        propertyTypeId: userProperty.property.propertyTypeId,
        configuration: {
          industryLicense: categoryId,
          industryPlan: industryPlan,
        },
        media: userProperty.property.media
          ? {
              id: userProperty.property.media.id,
              path: userProperty.property.media.path,
              fileName: userProperty.property.media.fileName,
              name: userProperty.property.media.name,
              caption: userProperty.property.media.caption,
              encoding: userProperty.property.media.encoding,
              mimeType: userProperty.property.media.mimeType,
              size: userProperty.property.media.size,
              dimension: userProperty.property.media.dimension,
            }
          : null,
      },
      user: {
        id: userProperty.user.id,
        email: userProperty.user.email,
        username: userProperty.user.username,
        mobileNumber: userProperty.user.mobileNumber,
        password: userProperty.user.password,
        profile: userProperty.user.profile
          ? {
              id: userProperty.user.profile.id,
              firstName: userProperty.user.profile.firstName,
              lastName: userProperty.user.profile.lastName,
              placeOfBirth: userProperty.user.profile.placeOfBirth,
              gender: userProperty.user.profile.gender,
              dateOfBirth: userProperty.user.profile.dateOfBirth?.toISOString(),
              address: userProperty.user.profile.address,
              createdAt: userProperty.user.profile.createdAt.toISOString(),
              updatedAt: userProperty.user.profile.updatedAt.toISOString(),
            }
          : null,
      },
      userProperty: {
        id: userProperty.id,
        userId: userProperty.userId,
        propertyId: userProperty.propertyId,
        isDefault: userProperty.isDefault,
        createdAt: userProperty.createdAt.toISOString(),
        updatedAt: userProperty.updatedAt.toISOString(),
      },
    };
  }
}
