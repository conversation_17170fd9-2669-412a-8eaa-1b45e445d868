import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GrpcDetailPropertyQuery, GrpcListPropertyQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { UserGrpcService } from 'apps/api-membership/src/internal-client/user.service';
import { firstValueFrom } from 'rxjs';
import { PropertyTypeGrpcService } from 'apps/api-membership/src/property-type/service/property-type.grpc.service';
import { PostalGrpcService } from 'apps/api-membership/src/postal/service/postal.grpc.service';
import { Metadata } from '@grpc/grpc-js';

@QueryHandler(GrpcDetailPropertyQuery)
export class GrpcDetailPropertyHandler implements IQueryHandler<GrpcDetailPropertyQuery> {
    constructor(
        private prisma: PrismaService,
        private userService: UserGrpcService,
        private propertyTypeService: PropertyTypeGrpcService,
        private postalService: PostalGrpcService
    ) { }

    async execute(query: GrpcDetailPropertyQuery) {
        const { id } = query;

        try {
            const property = await this.prisma.property.findUnique({
                where: { id: id.id },
                include: {
                    user: {
                        include: {
                            profile: true
                        }
                    }
                }
            });

            if (!property) {
                throw new RpcException({
                    code: 5,
                    message: 'Property not found'
                });
            }

            const propertyTypeResponse = await firstValueFrom(
                this.propertyTypeService.getPropertyTypeIn({
                    id: [property.propertyTypeId].filter(Boolean)
                })
            );

            const usersResponse = await firstValueFrom(
                this.userService.getUsersByPropertyId({
                    propertyId: [property.id]
                })
            );

            const postalResponse = await firstValueFrom(
                this.postalService.getPostalByIds(
                    { postalIds: [property.postalId] },
                    new Metadata()
                )
            );

            const propertyUsers = usersResponse?.users?.filter(user =>
                user.activationId === property.id
            ) || [];

            const propertyType = propertyTypeResponse?.data?.[0];
            const postal = postalResponse?.data?.[0];

            const owner = property.user ? {
                id: property.user.id || '',
                username: property.user.username || '',
                email: property.user.email || '',
                mobileNumber: property.user.mobileNumber || '',
                businessType: property.user.businessType || '',
                profile: property.user.profile ? {
                    firstName: property.user.profile.firstName || '',
                    lastName: property.user.profile.lastName || '',
                    placeOfBirth: property.user.profile.placeOfBirth || '',
                    dateOfBirth: property.user.profile.dateOfBirth?.toISOString() || '',
                    gender: property.user.profile.gender || 'unknown',
                    address: property.user.profile.address || ''
                } : null,
                isActive: property.user.isActive || false
            } : null;

            return {
                id: property.id || '',
                cid: property.cid || '',
                companyName: property.companyName || '',
                brandName: property.brandName || '',
                companyEmail: property.companyEmail || '',
                companyPhoneNumber: property.companyPhoneNumber || '',
                address: property.address || '',
                province: postal?.province || '',
                city: postal?.city || '',
                district: postal?.district || '',
                urban: postal?.urban || '',
                postalCode: postal?.code || '',
                postalId: property.postalId || '',
                licenseKey: property.licenseKey || '',
                licenseType: property.licenseType || '',
                industry: propertyType?.name || '',
                flag: property.flag || '',
                owner: owner,
                users: propertyUsers.map(user => ({
                    id: user.id || '',
                    username: user.username || '',
                    email: user.email || '',
                    mobileNumber: user.mobileNumber || '',
                    businessType: user.businessType || '',
                    isAdmin: user.isAdmin || false,
                    profile: user.profile ? {
                        firstName: user.profile.firstName || '',
                        lastName: user.profile.lastName || '',
                        placeOfBirth: user.profile.placeOfBirth || '',
                        dateOfBirth: user.profile.dateOfBirth || '',
                        gender: user.profile.gender || '',
                        address: user.profile.address || ''
                    } : null,
                    isActive: user.isActive || false,
                    status: user.status || '',
                    activationId: user.activationId || '',
                    zoneName: user.zoneName || ''
                }))
            };

        } catch (error) {
            throw new RpcException({
                code: 13,
                message: 'Error fetching property details: ' + (error?.message || 'Unknown error')
            });
        }
    }
}