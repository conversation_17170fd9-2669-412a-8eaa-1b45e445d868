import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertyQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { NotFoundException } from '@nestjs/common';
import { PropertyTypeGrpcService } from 'apps/api-membership/src/property-type/service/property-type.grpc.service';
import { PostalGrpcService } from 'apps/api-membership/src/postal/service/postal.grpc.service';
import { Id } from '@app/proto-schema/index.common';

@QueryHandler(GetPropertyQuery)
export class GetPropertyHandler implements IQueryHandler<GetPropertyQuery> {
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private postalGrpc: PostalGrpcService,
  ) {}

  async execute(query: GetPropertyQuery) {
    const { user } = query;

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        userId: user.id,
      },
      include: {
        property: {
          include: {
            media: true,
          },
        },
      },
    });

    if (!userProperty) {
      throw new NotFoundException();
    }

    const propertyTypeRequest: Id = {
      id: userProperty.property.propertyTypeId,
    };
    const propertyType = await this.propertyTypeGrpc
      .getOnePropertyType(propertyTypeRequest)
      .toPromise();

    // const postalRequest: Id = { id: userProperty.property.postalId };
    // const postal = await this.postalGrpc.(postalRequest).toPromise();

    const propertyWithDetails = {
      ...userProperty.property,
      id: userProperty.property.id.toString(),
      propertyType: {
        ...propertyType,
        id: propertyType.id.toString(),
      },
      media: userProperty.property.media,
      // postalId
    };

    return propertyWithDetails;
  }
}
