import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetListOwnerPropertyQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { convertKeyValuePairs, Pagination } from '@app/common';
import { Prisma } from '@prisma/client';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetListOwnerPropertyQuery)
export class GetListOwnerPropertyHandler implements IQueryHandler<GetListOwnerPropertyQuery> {
  constructor(
    private prisma: PrismaService,
  ) {}

  async execute(query: GetListOwnerPropertyQuery) {
    const { args } = query;

    const params = JSON.parse(args.query);
    const page = params?.page || 1;
    const limit = params?.limit || 20;
    const search = params?.search || '';
    const sort = params?.sort || 'desc'

    try {
      const items = await Pa<PERSON>ation<any, Prisma.UserFindManyArgs>(
        this.prisma.user,
        {
          where: {
            AND: [
              {
                OR: [
                  { id: { contains: search, mode: 'insensitive' } },
                  {
                    profile: {
                      firstName: { contains: search, mode: 'insensitive' }
                    }
                  },
                  {
                    profile: {
                      lastName: { contains: search, mode: 'insensitive' }
                    }
                  },
                  {
                    email: { contains: search, mode: 'insensitive' }
                  },
                  {
                    email: { contains: search, mode: 'insensitive' }
                  },
                  {
                    mobileNumber: { contains: search, mode: 'insensitive' }
                  },
                  {
                    businessType: { contains: search, mode: 'insensitive' }
                  }
                ],
              },
              {
                status: 'active'
              }
            ]
          },
          orderBy: { createdAt: 'desc' },
          include: {
            profile: true,
            properties: {
              select: {
                propertyId: true
              }
            }
          },
        },
        {
          limit: limit,
          page: page,
        },
      );

      items.data = items.data.map((item) => {
        return {
          id: item.id,
          firstName: item.profile.firstName,
          lastName: item.profile.lastName,
          gender: item.profile.gender,
          email: item.email,
          dateOfBirth: item.profile.dateOfBirth,
          mobileNumber: item.mobileNumber,
          businessType: item.businessType === 'Single' ? 'Corporate' : 'Enterprise',
          propertyIds: item.properties.map((userProperty) => {
            return { id: userProperty.propertyId }
          })
        }
      });

      return {
        status: {
          code: status.OK,
          message: 'Get list owner property success'
        },
        meta: items.meta,
        data: items.data
      }
    } catch (error) {
      console.log(error);
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Internal Server Error'
      });
    }
  }
}
