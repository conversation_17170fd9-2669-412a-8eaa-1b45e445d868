import { GetOnePropertyHandler } from './get-one-property.handler';
import { GetPropertiesHandler } from './get-properties.handler';
import { GetPropertyUserHandler } from './get-property-user.handler';
import { GetPropertyHandler } from './get-property.handler';
import { GetPropertyCidHandler } from './get-property-cid.handler';
import { GetOperatorHandler } from './get-operator.handler';
import { GetPropertyIdHandler } from './get-property-id.handler';
import { GetPropertyZoneTypeHandler } from './get-property-zone-type.handler';
import { GetPropertyOrderHandler } from './get-property-order.handler';
import { GetOverviewPropertyHandler } from './get-overview-property.handler';
import { GrpcGetListPropertyHandler } from './grpc-list-property.handler';
import { GrpcDetailPropertyHandler } from './grpc-detail-property.handler';
import { GetListOwnerPropertyHandler } from './get-list-owner-property.query';
import { GetOwnerPropertyHandler } from './get-owner-property.query';

export const PropertyQueryHandlers = [
  GetPropertyHandler,
  GetPropertyUserHandler,
  GetPropertiesHandler,
  GetOnePropertyHandler,
  GetPropertyCidHandler,
  GetOperatorHandler,
  GetPropertyIdHandler,
  GetPropertyZoneTypeHandler,
  GetPropertyOrderHandler,
  GetOverviewPropertyHandler,
  GrpcGetListPropertyHandler,
  GrpcDetailPropertyHandler,
  GetListOwnerPropertyHandler,
  GetOwnerPropertyHandler
];
