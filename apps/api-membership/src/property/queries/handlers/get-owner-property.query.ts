import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetOwnerPropertyQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetOwnerPropertyQuery)
export class GetOwnerPropertyHandler implements IQueryHandler<GetOwnerPropertyQuery> {
  constructor(
    private prisma: PrismaService,
  ) {}

  async execute(query: GetOwnerPropertyQuery) {
    const { args } = query;
    const params = JSON.parse(args.query);
    const item = await this.prisma.user.findFirst({
      where: {
        AND: [
          {
            id: params?.id
          },
          {
            status: 'active'
          }
        ]
      },
      include: {
        profile: true,
        properties: {
          select: {
            property: true
          }
        }
      },
    });

    if (!item) {
      throw new RpcException({ code: status.NOT_FOUND, message: 'group not found'});
    }

    return {
      status: {
        code: status.OK,
        message: 'Get list owner property success'
      },
      data: item.properties.map((itm) => {
        return {
          id: itm.property.id,
          cid: itm.property.cid,
          companyName: itm.property.companyName,
          brandName: itm.property.brandName,
        }
      })
    }
  }
}
