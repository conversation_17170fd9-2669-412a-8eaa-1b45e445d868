import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GrpcListPropertyQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { convertKeyValuePairs } from '@app/common';
import { Prisma } from '@prisma/client';
import { UserGrpcService } from 'apps/api-membership/src/internal-client/user.service';
import { firstValueFrom } from 'rxjs';
import { PropertyTypeGrpcService } from 'apps/api-membership/src/property-type/service/property-type.grpc.service';
import { PostalGrpcService } from 'apps/api-membership/src/postal/service/postal.grpc.service';
import { Metadata } from '@grpc/grpc-js';

@QueryHandler(GrpcListPropertyQuery)
export class GrpcGetListPropertyHandler implements IQueryHandler<GrpcListPropertyQuery> {
    constructor(
        private prisma: PrismaService,
        private userService: UserGrpcService,
        private propertyTypeService: PropertyTypeGrpcService,
        private postalService: PostalGrpcService
    ) { }

    async execute(query: GrpcListPropertyQuery) {
        const { args } = query;

        const params = convertKeyValuePairs(args?.filters || []);
        const page = args?.page || 1;
        const limit = args?.limit || 10;
        const skip = (page - 1) * limit;

        try {
            const where: Prisma.PropertyWhereInput = {};

            if (params.flag) {
                where.flag = params.flag;
            }

            if (params.postalId) {
                where.postalId = params.postalId;
            }

            const totalCount = await this.prisma.property.count({
                where
            });

            const properties = await this.prisma.property.findMany({
                where,
                skip,
                take: limit,
                include: {
                    users: {
                        where: {
                            user: {
                                isAdmin: true,
                                parentId: null
                            }
                        },
                        include: {
                            user: {
                                include: {
                                    profile: true
                                },
                            }
                        }
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });

            if (!properties?.length) {
                throw new RpcException({
                    code: 5,
                    message: 'Properties not found'
                });
            }

            const propertyIds = properties.map(property => property.id);
            const postalIds = properties.map(property => property.postalId);

            const propertyTypeIds = properties
                .map(property => property.propertyTypeId)
                .filter(Boolean);

            const propertyTypesResponse = await firstValueFrom(
                this.propertyTypeService.getPropertyTypeIn({
                    id: propertyTypeIds
                })
            );

            const usersResponse = await firstValueFrom(
                this.userService.getUsersByPropertyId({
                    propertyId: propertyIds
                })
            );

            const postalResponse = await firstValueFrom(
                this.postalService.getPostalByIds(
                    { postalIds: postalIds },
                    new Metadata()
                )
            );
            

            const data = properties.map((property) => {
                const propertyUsers = usersResponse?.users?.filter(user =>
                    user.activationId === property.id
                ) || [];

                const propertyType = propertyTypesResponse?.data?.find(
                    type => type.id === property.propertyTypeId
                );

                const postal = postalResponse?.data?.find(
                    postal => postal.id === property.postalId
                );

                const ownerUserProperty = property.users.find(up => up.user?.isAdmin && !up.user?.parentId);
                const ownerUser = ownerUserProperty?.user;

                const owner = ownerUser ? {
                    id: ownerUser.id || '',
                    username: ownerUser.username || '',
                    email: ownerUser.email || '',
                    mobileNumber: ownerUser.mobileNumber || '',
                    businessType: ownerUser.businessType || '',
                    profile: ownerUser.profile ? {
                        firstName: ownerUser.profile.firstName || '',
                        lastName: ownerUser.profile.lastName || '',
                        placeOfBirth: ownerUser.profile.placeOfBirth || '',
                        dateOfBirth: ownerUser.profile.dateOfBirth?.toISOString() || '',
                        gender: ownerUser.profile.gender || 'unknown',
                        address: ownerUser.profile.address || ''
                    } : null,
                    isActive: ownerUser.isActive || false
                } : null;

                return {
                    id: property.id || '',
                    cid: property.cid || '',
                    companyName: property.companyName || '',
                    brandName: property.brandName || '',
                    companyEmail: property.companyEmail || '',
                    companyPhoneNumber: property.companyPhoneNumber || '',
                    address: property.address || '',
                    province: postal?.province || '',
                    city: postal?.city || '',
                    district: postal?.district || '',
                    urban: postal?.urban || '',
                    postalCode: postal?.code || '',
                    postalId: property.postalId || '',
                    licenseKey: property.licenseKey || '',
                    licenseType: property.licenseType || '',
                    industry: propertyType?.name || '',
                    flag: property.flag || '',
                    owner: owner,
                    users: propertyUsers.map(user => ({
                        id: user.id || '',
                        username: user.username || '',
                        email: user.email || '',
                        mobileNumber: user.mobileNumber || '',
                        businessType: user.businessType || '',
                        isAdmin: user.isAdmin || false,
                        profile: user.profile ? {
                            firstName: user.profile.firstName || '',
                            lastName: user.profile.lastName || '',
                            placeOfBirth: user.profile.placeOfBirth || '',
                            dateOfBirth: user.profile.dateOfBirth || '',
                            gender: user.profile.gender || '',
                            address: user.profile.address || ''
                        } : null,
                        isActive: user.isActive || false,
                        status: user.status || '',
                        activationId: user.activationId || '',
                        zoneName: user.zoneName || ''
                    }))
                };
            });

            const lastPage = Math.ceil(totalCount / limit);

            return {
                data,
                meta: {
                    total: totalCount || 0,
                    lastPage: lastPage || 0,
                    currentPage: page,
                    limit: limit,
                    prev: page > 1 ? page - 1 : 0,
                    next: page < lastPage ? page + 1 : 0
                }
            };

        } catch (error) {
            throw new RpcException({
                code: 13,
                message: 'Error fetching properties: ' + (error?.message || 'Unknown error')
            });
        }
    }
}