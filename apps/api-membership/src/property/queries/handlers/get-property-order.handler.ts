import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertyOrderQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PackageService } from '../../../internal-client/services';
import { Pagination } from '@app/common';
import { Prisma } from '@prisma/client';
import { Metadata, status } from '@grpc/grpc-js';

@QueryHandler(GetPropertyOrderQuery)
export class GetPropertyOrderHandler
  implements IQueryHandler<GetPropertyOrderQuery>
{
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private pkgGrpc: PackageService,
  ) {}

  async execute(query: GetPropertyOrderQuery) {
    const { propertyId } = query;

    const items = await Pagination<any, Prisma.OrderFindManyArgs>(
      this.prisma.order,
      {
        orderBy: { createdAt: 'desc' },
        where: {
          AND: [{ propertyId: propertyId }],
        },
        include: {
          details: { include: { orderPackageDetail: true } },
          property: true,
          payment: true,
        },
      },
      // { page: args.page, limit: args.limit },
    );

    const data = await Promise.all(
      items.data.map(async (item) => {
        let propertyTypeName = null;
        let packageObj = null;

        if (item.property) {
          const propertyType = await this.propertyTypeGrpc
            .getOnePropertyType({ id: item.property.propertyTypeId })
            .toPromise()
            .then((response) => response?.name)
            .catch(() => null);

          propertyTypeName = propertyType;

          const payload: any = {
            propertyId: item.property.id,
          };

          const meta = new Metadata();
          try {
            const pkgResponse = await this.pkgGrpc.client
              .listPackage(
                {
                  query: JSON.stringify(payload),
                },
                meta,
              )
              .toPromise();

            const packageData =
              pkgResponse?.data?.length > 0 ? pkgResponse.data[0] : null;

            packageObj = packageData
              ? {
                  id: packageData.id,
                  name: packageData.name,
                  status: packageData.status,
                  itemType: packageData.itemType,
                  itemId: packageData.itemId,
                  isActive: packageData.isActive,
                  activeAt: packageData.activeAt,
                  expiredAt: packageData.expiredAt,
                  contractEndAt: packageData.contractEndAt,
                  trialEndAt: packageData.trialEndAt,
                  isTrial: packageData.isTrial,
                  createdAt: packageData.createdAt,
                  updatedAt: packageData.updatedAt,
                  propertyId: item.property.id,
                }
              : null;
          } catch (error) {
            // Handle package service error gracefully
            packageObj = null;
          }
        }

        return {
          id: item.id,
          name: item.name,
          description: item.description,
          status: item.status,
          tag: item.tag,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          totalPrice: item.totalPrice,
          discount: item.discount,
          voucherId: item.voucherId,
          orderPayment: item.payment
            ? {
                id: item.payment.id,
                by: item.payment.by,
                url: item.payment.url,
                expiredPayment: item.payment.expiredPayment,
                status: item.payment.status,
                paymentRequestId: item.payment.paymentRequestId,
                createdAt: item.payment.createdAt,
                updatedAt: item.payment.updatedAt,
              }
            : null,
          orderDetail:
            item.details.length > 0
              ? item.details.map((det) => {
                  return {
                    id: det.id,
                    name: det.name,
                    duration: det.duration,
                    price: det.price,
                    totalPrice: det.totalPrice,
                    tax: det.tax,
                    discount: det.discount,
                    itemType: det.itemType,
                    itemId: det.itemId,
                    qty: det.qty,
                    sku: det.sku,
                    createdAt: det.createdAt,
                    updatedAt: det.updatedAt,
                    orderPackageDetail: det.orderPackageDetail.map((opd) => {
                      return {
                        id: opd.id,
                        featureId: opd.featureId,
                        qty: opd.qty,
                      };
                    }),
                  };
                })
              : [],
          property: item.property,
          industry: propertyTypeName,
          package: packageObj,
        };
      }),
    );

    return {
      status: {
        code: status.OK,
        message: 'Success Get Orders',
      },
      meta: items.meta,
      data,
    };
  }
}
