import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertyIdQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetPropertyIdQuery)
export class GetPropertyIdHandler implements IQueryHandler<GetPropertyIdQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPropertyIdQuery) {
    const { id } = query;

    return this.prisma.property.findFirst({
      where: { id },
      include: { order: { include: { details: true } }, contactPerson: true },
    });
  }
}
