import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { GetOnePropertyQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { NotFoundException } from '@nestjs/common';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PackageService } from '../../../internal-client/services';
import { Metadata } from '@grpc/grpc-js';
import { PostalGrpcService } from 'apps/api-membership/src/postal/service/postal.grpc.service';
import { JobPositionGrpcService } from 'apps/api-membership/src/internal-client/job-position.service';
import { firstValueFrom, lastValueFrom } from 'rxjs';
import { IntegrationDlpService } from '../../../integration/services';

@QueryHandler(GetOnePropertyQuery)
export class GetOnePropertyHandler
  implements IQueryHandler<GetOnePropertyQuery>
{
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private pkgGrpc: PackageService,
    private postalGrpc: PostalGrpcService,
    private readonly integrationService: IntegrationDlpService,
    private jobPositionGrpc: JobPositionGrpcService,
  ) {}

  async execute(query: GetOnePropertyQuery) {
    const { ids } = query;
    let propertyTypePlan = null;
    let propertyTypeLicense = null;
    let postal = null;
    let propertyType = null;
    let codeLmkn = null;

    const item = await this.prisma.property.findFirst({
      where: { id: ids },
      include: {
        configuration: true,
        contactPerson: true,
      },
    });

    if (!item) {
      throw new NotFoundException('Property not found');
    }

    const contactPersonData = await Promise.all(
      item.contactPerson.map(async (contact) => {
        try {
          if (!contact.jobPositionId) {
            return { ...contact, jobPosition: null };
          }
          const jobPosition = await firstValueFrom(
            this.jobPositionGrpc.detailJobPosition({
              id: contact.jobPositionId,
            }),
          );
          return {
            ...contact,
            jobPosition: {
              id: jobPosition.id,
              name: jobPosition.name,
              description: jobPosition.description,
            },
          };
        } catch (error) {
          console.error('Error fetching job position:', error);
          return { ...contact, jobPosition: null };
        }
      }),
    );

    const lcs = item.configuration.options['licenseType'] || null;
    const customFields = item.configuration.options['customfields'] || null;

    const { propertyTypeId, postalId, configuration, ...propertyData } = item;

    if (propertyTypeId) {
      try {
        propertyType = await lastValueFrom(
          this.propertyTypeGrpc.getOnePropertyType({ id: propertyTypeId }),
        );
      } catch (error) {
        console.error('Error fetching property type:', error);
        propertyType = null;
      }
    } else {
      propertyType = null;
    }

    if (configuration?.options?.['industryPlan']) {
      const industryPlanId = configuration.options['industryPlan'];
      if (industryPlanId) {
        try {
          propertyTypePlan = await lastValueFrom(
            this.propertyTypeGrpc.getOnePropertyType({ id: industryPlanId }),
          );
        } catch (error) {
          console.error('Error fetching industry plan:', error);
          propertyTypePlan = null;
        }
      } else {
        propertyTypePlan = null;
      }
    }

    if (propertyTypePlan) {
      codeLmkn = await this.integrationService.DetailCategoryByCode(
        propertyTypePlan.categoryCode,
      );
    } else {
      codeLmkn = null;
    }

    if (configuration?.options?.['categoryId']) {
      const categoryId = configuration.options['categoryId'];
      if (categoryId) {
        try {
          propertyTypeLicense = await lastValueFrom(
            this.propertyTypeGrpc.getOnePropertyType({ id: categoryId }),
          );
        } catch (error) {
          console.error('Error fetching category:', error);
          propertyTypeLicense = null;
        }
      } else {
        propertyTypeLicense = null;
      }
    }

    if (postalId) {
      const meta = new Metadata();
      try {
        postal = await lastValueFrom(
          this.postalGrpc.getPostal({ id: postalId }, meta),
        );
      } catch (error) {
        console.error('Error fetching postal:', error);
        postal = null;
      }
    } else {
      postal = null;
    }

    const payload: any = {
      propertyId: item.id,
    };

    const meta = new Metadata();
    let pkg = null;
    try {
      pkg = await lastValueFrom(
        this.pkgGrpc.client.listPackage(
          {
            query: JSON.stringify(payload),
          },
          meta,
        ),
      );
    } catch (error) {
      console.error('Error fetching package:', error);
      pkg = null;
    }

    const extendedTemporary =
      configuration?.options?.['extendedTemp'] !== undefined
        ? configuration.options['extendedTemp']
        : false;

    const rangeLabel = codeLmkn?.rangeLabel || null;
    const unitLabel = codeLmkn?.unitLabel || null;

    return {
      ...propertyData,
      contactPerson: contactPersonData,
      customFields: customFields,
      propertyType: propertyType
        ? {
            id: propertyType.id,
            name: propertyType.name,
          }
        : null,
      propertyTypePlan: propertyTypePlan
        ? {
            id: propertyTypePlan.id,
            name: propertyTypePlan.name,
          }
        : null,
      propertyTypeLicense: propertyTypeLicense
        ? {
            id: propertyTypeLicense.id,
            name: propertyTypeLicense.name,
          }
        : null,
      postal: postal
        ? {
            province: {
              name: postal.province,
            },
            city: {
              name: postal.city,
            },
            district: {
              name: postal.district,
            },
            urban: {
              name: postal.urban,
            },
            zip: {
              code: postal.code,
            },
          }
        : null,
      package: pkg ? pkg.data : null,
      licenseType: lcs,
      extendedTemporary: extendedTemporary,
      rangeLabel: rangeLabel,
      unitLabel: unitLabel,
    };
  }
}
