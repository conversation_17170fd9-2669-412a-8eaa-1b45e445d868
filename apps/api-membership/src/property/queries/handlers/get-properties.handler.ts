import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertiessQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { Pagination } from '@app/common';
import { Prisma } from '@prisma/client';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { Metadata } from '@grpc/grpc-js';
import { PackageService } from '../../../internal-client/services';
import { IntegrationDlpService } from '../../../integration/services';
import { lastValueFrom } from 'rxjs';

@QueryHandler(GetPropertiessQuery)
export class GetPropertiesHandler
  implements IQueryHandler<GetPropertiessQuery>
{
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private integrationService: IntegrationDlpService,
    private pkgGrpc: PackageService,
  ) {}

  async execute(query: GetPropertiessQuery) {
    const { user, args } = query;
    const search = args.search || '';
    const page = Number(args?.page || 1);
    const limit = Number(args?.limit || 50);
    const sortType = args?.sortType ? args.sortType : 'desc';

    const baseWhere = {
      userId: user.id,
    };

    let whereClause;
    const propertyFilters: any = {};

    if (args.industry) {
      propertyFilters.propertyTypeId = args.industry;
    }

    const searchConditions = [];

    if (search && search.trim() !== '') {
      searchConditions.push(
        { companyName: { contains: search, mode: 'insensitive' } },
        { brandName: { contains: search, mode: 'insensitive' } },
        { cid: { contains: search, mode: 'insensitive' } },
      );
    }

    if (args.cid && args.cid.trim() !== '') {
      searchConditions.push({
        cid: { contains: args.cid, mode: 'insensitive' },
      });
    }

    if (args.companyName && args.companyName.trim() !== '') {
      searchConditions.push({
        companyName: { contains: args.companyName, mode: 'insensitive' },
      });
    }

    if (args.brandName && args.brandName.trim() !== '') {
      searchConditions.push({
        brandName: { contains: args.brandName, mode: 'insensitive' },
      });
    }

    if (
      searchConditions.length > 0 ||
      Object.keys(propertyFilters).length > 0
    ) {
      const propertyWhere: any = { ...propertyFilters };

      if (searchConditions.length > 0) {
        propertyWhere.OR = searchConditions;
      }

      whereClause = {
        ...baseWhere,
        property: propertyWhere,
      };
    } else {
      whereClause = baseWhere;
    }

    const items = await Pagination<any, Prisma.UserPropertyFindManyArgs>(
      this.prisma.userProperty,
      {
        where: whereClause,
        orderBy: { createdAt: sortType },
        include: {
          property: true,
          user: true,
        },
      },
      {
        limit: limit,
        page: page,
      },
    );

    let propertyTypes: { id: string; name: string }[] = [];

    try {
      propertyTypes = await Promise.all(
        items.data.map(async (item) => {
          const id = item.property.propertyTypeId;
          if (!id) return { id: '', name: '' };

          try {
            const propertyType = await lastValueFrom(
              this.propertyTypeGrpc.getOnePropertyType({ id }),
            );
            return { id: propertyType.id, name: propertyType.name };
          } catch (error) {
            console.error(`Error fetching property type with id ${id}:`, error);
            return { id: '', name: '' };
          }
        }),
      );
    } catch (error) {
      console.error('Error in property types Promise.all:', error);
      propertyTypes = items.data.map(() => ({ id: '', name: '' }));
    }

    let packages: any[] = [];

    try {
      packages = await Promise.all(
        items.data.map(async (itm) => {
          const payload: any = {
            propertyId: itm.property.id,
          };

          const meta = new Metadata();
          const pkg = await lastValueFrom(
            this.pkgGrpc.client.listPackage(
              {
                query: JSON.stringify(payload),
              },
              meta,
            ),
          );

          return pkg.data;
        }),
      );
    } catch (error) {
      console.error('Error fetching packages:', error);
    }

    let result = await Promise.all(
      items.data.map(async (itm, index) => {
        const { propertyTypeId, requestId, ...propertyData } = itm.property;

        let licenseStatusStatus = null;
        let mustVerif = false;
        let propertyForm = false;

        const otherUsers = await this.prisma.userProperty.findMany({
          where: {
            propertyId: itm.property.id,
            userId: { not: user.id },
          },
        });

        if (requestId) {
          try {
            const licenseStatus =
              await this.integrationService.checkLicenseStatus(requestId);
            licenseStatusStatus = licenseStatus?.status || null;
          } catch (error) {
            console.error('Error fetching license status:', error);
          }
        }

        if (licenseStatusStatus === 'pending') {
          mustVerif = true;
        }

        if (itm.property.status === 'draft') {
          propertyForm = true;
        }

        const userOperator = otherUsers.length > 0;
        const packageList = packages[index] || [];
        const planPackage = packageList.find((dt) => dt.itemType === 'PLAN');
        const playerNode = planPackage?.packageFeature?.find(
          (ft) => ft.id === 'f-002',
        );

        return {
          ...propertyData,
          licenseStatus: licenseStatusStatus,
          totalActivation: playerNode ? Number(playerNode.qty) : 0,
          totalUsedActivation: playerNode ? Number(playerNode.quota) : 0,
          propertyType: propertyTypes[index] || { id: '', name: '' },
          package: packages[index] || [],
          userOperator: userOperator || '',
          requestId: requestId || '',
          mustVerifyTemporaryLicense: mustVerif,
          mustCompletePropertyForm: propertyForm,
        };
      }),
    );

    if (
      args.packageStatus ||
      args.licenseStatus ||
      args.packageName ||
      args.packageType
    ) {
      result = result.filter((item) => {
        if (args.licenseStatus && item.licenseStatus !== args.licenseStatus) {
          return false;
        }

        if (args.packageStatus || args.packageName || args.packageType) {
          const packageList = item.package || [];

          if (
            packageList.length === 0 &&
            (args.packageStatus || args.packageName || args.packageType)
          ) {
            return false;
          }

          let packageMatch = false;

          for (const pkg of packageList) {
            let matches = true;

            if (args.packageStatus) {
              const pkgStatus = this.getPackageStatus(pkg);
              if (pkgStatus !== args.packageStatus) {
                matches = false;
              }
            }

            if (args.packageName && matches) {
              if (
                !pkg.name ||
                !pkg.name.toLowerCase().includes(args.packageName.toLowerCase())
              ) {
                matches = false;
              }
            }

            if (args.packageType && matches) {
              if (pkg.itemType !== args.packageType) {
                matches = false;
              }
            }

            if (matches) {
              packageMatch = true;
              break;
            }
          }

          return packageMatch;
        }

        return true;
      });
    }

    return {
      ...items,
      data: result,
    };
  }

  private getPackageStatus(pkg: any): string {
    const now = Date.now();

    if (!pkg.isActive) {
      return 'inactive';
    }

    if (pkg.isTrial) {
      return 'trial';
    }

    if (pkg.expiredAt && pkg.expiredAt < now) {
      return 'expired';
    }

    return 'active';
  }
}
