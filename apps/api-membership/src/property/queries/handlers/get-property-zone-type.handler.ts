import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertyZoneTypeQuery } from '../impl';
import { PropertyGrpcService } from 'apps/api-membership/src/internal-client/property.service';
import { PrismaService } from 'nestjs-prisma';
import { HttpStatus, NotFoundException } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { Id } from '@app/proto-schema/index.common';
import { GetPropertyZoneTypeRequest } from '@app/proto-schema/index.internal';

@QueryHandler(GetPropertyZoneTypeQuery)
export class GetPropertyZoneTypeHandler
  implements IQueryHandler<GetPropertyZoneTypeQuery>
{
  constructor(
    private propertyGrpc: PropertyGrpcService,
    private prisma: PrismaService,
  ) {}

  async execute(query: GetPropertyZoneTypeQuery): Promise<any> {
    const { id, args } = query;

    const request: GetPropertyZoneTypeRequest = {
      id,
      search: args.search,
    };

    const data = await this.prisma.property.findFirst({
      where: { id },
    });

    if (!data) {
      throw new NotFoundException('Property Not Found');
    }

    const propertyZoneType = await firstValueFrom(
      this.propertyGrpc.getPropertyZoneType(request),
    );

    if (!propertyZoneType) {
      throw new NotFoundException('Property Not Found');
    }

    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully Get Property Zone Type',
      data: propertyZoneType.data,
    };
  }
}
