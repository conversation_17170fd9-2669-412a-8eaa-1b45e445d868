import { ContextDto } from '@app/common';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNotIn,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  MaxLength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { IsUniqueCommercialProperty } from 'libs/validator/src/is-unique-commercial-property/is-unique-commercial-property';
import { IsValidEmail } from '../../../../../libs/validator/src/is-valid-email/is-valid-email';

export enum CategoryType {
  RANGES = 'ranges',
  PERCENTAGES = 'percentages',
  LUPSUM = 'lupsum',
  SPLIT = 'split',
  PROGRESIVES = 'progressives',
}

export enum LicenseType {
  DLM = 'DLM',
  CONVENTIONAL = 'CONVENTIONAL',
  TEMPORARY = 'TEMPORARY',
}

export class Percentages {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @ValidateIf((prop) => prop.id != '')
  id: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @IsNotIn([0], { message: 'Value cannot be zero' })
  @ValidateIf((prop) => prop.value != '')
  value: number;
}

export class CustomFieldBody {
  id: string;
  type: CategoryType;

  @ApiProperty()
  @IsNotEmpty()
  @IsNotIn([0], { message: 'Value cannot be zero' })
  @ValidateIf((prop) => prop.value != '')
  value: number | Percentages;
}

export class CreateContactPersonDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  firstName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  lastName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsValidEmail()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsPhoneNumber('ID')
  phone: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  jobPositionId: string;
}

export class CreatePropertyDto extends PartialType(ContextDto) {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  companyName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  @IsUniqueCommercialProperty({ model: 'property', ignore: true })
  brandName: string;

  @ApiProperty()
  @IsEmail()
  @IsValidEmail()
  companyEmail: string;

  @ApiProperty()
  @IsPhoneNumber('ID')
  companyPhoneNumber: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  npwp: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @MaxLength(250)
  address: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  propertyTypeId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @ValidateIf((prop) => prop.typeId != '')
  postalId: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  billingAddress?: string;

  @ApiProperty({ enum: LicenseType })
  @IsEnum(LicenseType)
  @IsNotEmpty()
  licenseType: LicenseType;

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((prop) => prop.licenseType === 'CONVENTIONAL')
  @IsNotEmpty({
    message: 'licenseNumber is required when licenseType is CONVENTIONAL.',
  })
  licenseNumber?: string;

  @ApiPropertyOptional({ type: CustomFieldBody })
  @Type(() => CustomFieldBody)
  @IsOptional()
  @ValidateIf((prop) => prop.licenseType === 'DLM')
  @ValidateNested()
  @IsNotEmpty({ message: 'customFields is required when licenseType is DLM' })
  customFields?: CustomFieldBody;

  @ApiPropertyOptional({ type: [CreateContactPersonDto] })
  @Type(() => CreateContactPersonDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  contactPerson?: CreateContactPersonDto[];
}
