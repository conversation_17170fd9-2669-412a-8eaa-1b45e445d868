import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  Matches,
  MaxLength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateContactPersonDto } from './create-property.dto';
import { IsValidEmail } from '../../../../../libs/validator/src/is-valid-email/is-valid-email';

export class ValidLicenseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  companyName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  brandName: string;

  @ApiProperty()
  @IsEmail()
  @IsValidEmail()
  companyEmail: string;

  @ApiProperty()
  @IsPhoneNumber('ID')
  companyPhoneNumber: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Matches(/^\d+$/, { message: 'npwp must be a numeric string' })
  npwp: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @MaxLength(250)
  address: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  billingAddress?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @ValidateIf((prop) => prop.typeId != '')
  postalId: string;

  @ApiProperty()
  @IsNotEmpty()
  licenseKey: string;

  @ApiPropertyOptional({ type: [CreateContactPersonDto] })
  @Type(() => CreateContactPersonDto)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  contactPerson?: CreateContactPersonDto[];
}
