import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, ValidateNested } from 'class-validator';
import { CustomFieldBody } from './create-property.dto';
import { Type } from 'class-transformer';

export class tempLicenseExtendDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  propertyId?: string;

  @ApiPropertyOptional({ type: CustomFieldBody })
  @Type(() => CustomFieldBody)
  @IsOptional()
  @ValidateNested()
  customFields?: CustomFieldBody;
}
