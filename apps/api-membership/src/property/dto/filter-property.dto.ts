import { BaseFilterDto } from '@app/common';
import { PartialType } from '@nestjs/swagger';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from 'class-validator';

export enum PackageStatusFilter {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
  TRIAL = 'trial',
}

export enum LicenseStatusFilter {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PROCESSED = 'processed',
}

export enum PackageTypeFilter {
  PLAN = 'PLAN',
  ADDON = 'ADDON',
  LICENSE = 'LICENSE',
}

export class FilterPropertyDto extends PartialType(BaseFilterDto) {
  @ApiPropertyOptional({ description: 'Filter by industry/property type ID' })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiPropertyOptional({
    enum: PackageStatusFilter,
    description: 'Filter by package status'
  })
  @IsOptional()
  @IsEnum(PackageStatusFilter)
  packageStatus?: PackageStatusFilter;

  @ApiPropertyOptional({
    enum: LicenseStatusFilter,
    description: 'Filter by license status'
  })
  @IsOptional()
  @IsEnum(LicenseStatusFilter)
  licenseStatus?: LicenseStatusFilter;

  @ApiPropertyOptional({ description: 'Filter by package name' })
  @IsOptional()
  @IsString()
  packageName?: string;

  @ApiPropertyOptional({
    enum: PackageTypeFilter,
    description: 'Filter by package type'
  })
  @IsOptional()
  @IsEnum(PackageTypeFilter)
  packageType?: PackageTypeFilter;

  @ApiPropertyOptional({ description: 'Search by CID' })
  @IsOptional()
  @IsString()
  cid?: string;

  @ApiPropertyOptional({ description: 'Search by company name' })
  @IsOptional()
  @IsString()
  companyName?: string;

  @ApiPropertyOptional({ description: 'Search by brand name' })
  @IsOptional()
  @IsString()
  brandName?: string;
}