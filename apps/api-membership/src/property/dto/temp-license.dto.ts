import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { CustomFieldBody } from './create-property.dto';
import { Type } from 'class-transformer';

export class tempLicenseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  propertyTypeId: string;

  @ApiPropertyOptional({ type: CustomFieldBody })
  @Type(() => CustomFieldBody)
  @IsOptional()
  @ValidateNested()
  customFields?: CustomFieldBody;
}
