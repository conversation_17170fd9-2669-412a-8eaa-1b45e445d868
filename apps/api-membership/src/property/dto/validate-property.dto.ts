import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsBoolean,
  ValidateNested,
  IsEnum,
  IsNotEmpty,
} from 'class-validator';
import { CreateContactPersonDto, CustomFieldBody } from './create-property.dto';
import { LicenseType } from '@app/common';

export class ValidatePropertyDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  propertyTypeId?: string;

  @ApiProperty({ enum: LicenseType })
  @IsEnum(LicenseType)
  @IsNotEmpty()
  licenseType: LicenseType;

  @ApiProperty()
  @IsString()
  @IsOptional()
  postalId?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  licenseNumber?: string;

  @ApiPropertyOptional({ type: CustomFieldBody })
  @ValidateNested()
  @Type(() => CustomFieldBody)
  @IsOptional()
  customFields?: CustomFieldBody;

  @ApiPropertyOptional({ type: [CreateContactPersonDto] })
  @ValidateNested({ each: true })
  @Type(() => CreateContactPersonDto)
  @IsOptional()
  contactPerson?: CreateContactPersonDto[];
}
