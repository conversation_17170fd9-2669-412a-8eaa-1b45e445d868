import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { CreatePropertyDto } from './dto/create-property.dto';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import {
  ActivateLicenseCommand,
  CreatePropertyCommand,
  DeletePropertyCommand,
  ExtendTempLicenseCommand,
  UpdateBillingAddressCommand,
  UploadPropertyLogoCommand,
  ValidatePropertyCommand,
  ValidLicenseTempCommand,
} from './commands';
import {
  GetOnePropertyQuery,
  GetPropertiessQuery,
  GetPropertyZoneTypeQuery,
} from './queries';
import { FilterPropertyDto } from './dto/filter-property.dto';
import { Throttle } from '@nestjs/throttler';
import { UploadPropertyLogoDto } from './dto/upload-property-logo.dto';
import { FilterListZoneTypeDto } from './dto/filter-list-zone-type.dto';
import { ValidatePropertyDto } from './dto/validate-property.dto';
import { tempLicenseDto } from './dto/temp-license.dto';
import { CreateTempLicenseCommand } from './commands/impl/create-temp-license.command';
import { CreateTempValidCommand } from './commands/impl/create-temp-valid.command';
import { ValidLicenseDto } from './dto/valid-license.dto';
import { ValidateLicenseTempDto } from './dto/validate-temp-license.dto';
import { tempLicenseExtendDto } from './dto/temp-license-extend.dto';
import { ActivateLicenseDto } from './dto/activate-license.dto';
import { updateBillingAddressDto } from './dto/update-billing-address.dto';

@ApiTags('Property')
@Controller('property')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class PropertyController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Post('validate')
  validate(
    @Body() validatePropertyDto: ValidatePropertyDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new ValidatePropertyCommand(user, validatePropertyDto),
    );
  }

  @Post()
  @ApiOperation({ summary: 'create property if not exist' })
  create(
    @Body() createPropertyDto: CreatePropertyDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new CreatePropertyCommand(createPropertyDto, user),
    );
  }

  @Post('logo')
  @Throttle({ default: { limit: 10, ttl: 60000 } })
  upload(@User() user: ICurrentUser, @Body() Payload: UploadPropertyLogoDto) {
    return this.commandBus.execute(
      new UploadPropertyLogoCommand(user, Payload),
    );
  }

  @Get(':id/zone-type')
  getPropertyZoneType(
    @Param('id') id: string,
    @Query() args: FilterListZoneTypeDto,
  ) {
    return this.queryBus.execute(new GetPropertyZoneTypeQuery(id, args));
  }

  @Get(':id')
  findOne(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.queryBus.execute(new GetOnePropertyQuery(id));
  }

  @Delete(':id')
  delete(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.commandBus.execute(new DeletePropertyCommand(id, user));
  }

  @Get()
  findAll(@User() user: ICurrentUser, @Query() filter: FilterPropertyDto) {
    return this.queryBus.execute(new GetPropertiessQuery(user, filter));
  }

  @Post('temp-license')
  @ApiOperation({ summary: 'Request Temporary to DLP' })
  temporaryLicense(
    @Body() createTempLicense: tempLicenseDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new CreateTempLicenseCommand(createTempLicense, user),
    );
  }

  @Post('temp-license-extend')
  @ApiOperation({ summary: 'Extend Temporary license from DLP' })
  extendTempLicense(
    @User() user: ICurrentUser,
    @Body() args: tempLicenseExtendDto,
  ) {
    return this.commandBus.execute(new ExtendTempLicenseCommand(user, args));
  }

  @Post('temp-license-validate')
  @ApiOperation({ summary: 'Validate Temporary license from DLP' })
  validateLicenseTemp(
    @Body() validateLicense: ValidateLicenseTempDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new ValidLicenseTempCommand(validateLicense, user),
    );
  }

  @Post('temp-valid')
  @ApiOperation({ summary: 'Create Property (Finish setup property)' })
  validTempLicense(@User() user: ICurrentUser, @Body() args: ValidLicenseDto) {
    return this.commandBus.execute(new CreateTempValidCommand(user, args));
  }

  @Post('activate-license')
  @ApiOperation({ summary: 'Activate license from DLP' })
  activateLicenseTemp(
    @User() user: ICurrentUser,
    @Body() args: ActivateLicenseDto,
  ) {
    return this.commandBus.execute(new ActivateLicenseCommand(user, args));
  }

  @Patch(':id/billing-address')
  @ApiOperation({ summary: 'Update Property Billing Address' })
  updateBillingAdress(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Body() args: updateBillingAddressDto,
  ) {
    return this.commandBus.execute(
      new UpdateBillingAddressCommand(user, id, args),
    );
  }
}
