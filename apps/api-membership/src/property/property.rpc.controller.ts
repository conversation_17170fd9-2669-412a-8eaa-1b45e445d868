import { Empty, Id, Query, QueryMap, Status } from '@app/proto-schema/index.common';
import {
  ListOrderResponse,
  GetOverviewPropertyResponse,
  Property,
  PROPERTY_SERVICE_NAME,
  PropertyCidRequest,
  PropertyServiceController,
  PropertyUserRequest,
  PropertyUserResponse,
  SetPropertyFlagRequest,
  ListPropertyResponse,
  PropertyInfo,
  GetListOwnerPropertyResponse,
  OwnerProperty,
  PropertyWithType,
  GetOwnerPropertyResponse
} from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import {
  GetListOwnerPropertyQuery,
  GetOverviewPropertyQuery,
  GetOwnerPropertyQuery,
  GetPropertyCidQuery,
  GetPropertyIdQuery,
  GetPropertyOrderQuery,
  GetPropertyUserQuery,
  GrpcDetailPropertyQuery,
  GrpcListPropertyQuery,
} from './queries';
import { ActivatePropertyCommand, SetPropertyFlagCommand } from './commands';
import { GrpcMethod } from '@nestjs/microservices';

@Controller()
export class PropertyRpcController implements PropertyServiceController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) { }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'setPropertyFlag')
  setPropertyFlag(
    request: SetPropertyFlagRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(new SetPropertyFlagCommand(request));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getPropertyUser')
  getPropertyUser(
    request: PropertyUserRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<PropertyUserResponse>
    | Observable<PropertyUserResponse>
    | PropertyUserResponse {
    return this.queryBus.execute(new GetPropertyUserQuery(request.propertyId));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'activateProperty')
  activateProperty(
    request: PropertyUserRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(
      new ActivatePropertyCommand(request.propertyId),
    );
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'suspendProperty')
  suspendProperty(
    request: PropertyUserRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(
      new ActivatePropertyCommand(request.propertyId),
    );
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getPropertyByCid')
  getPropertyByCid(
    request: PropertyCidRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<PropertyUserResponse>
    | Observable<PropertyUserResponse>
    | PropertyUserResponse {
    return this.queryBus.execute(new GetPropertyCidQuery(request.cid));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getPropertyById')
  getPropertyById(
    request: Id,
    metadata: Metadata,
    ...rest
  ): Promise<Property> | Observable<Property> | Property {
    return this.queryBus.execute(new GetPropertyIdQuery(request.id));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getPropertyOrder')
  getPropertyOrder(
    request: Id,
    metadata: Metadata,
    ...rest
  ):
    | Promise<ListOrderResponse>
    | Observable<ListOrderResponse>
    | ListOrderResponse {
    return this.queryBus.execute(new GetPropertyOrderQuery(request.id));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getOverviewProperty')
  getOverviewProperty(
    request: Empty,
    metadata: Metadata,
    ...rest
  ):
    | Promise<GetOverviewPropertyResponse>
    | Observable<GetOverviewPropertyResponse>
    | GetOverviewPropertyResponse {
    return this.queryBus.execute(new GetOverviewPropertyQuery());
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'listProperty')
  listProperty(
    request: QueryMap,
    metadata: Metadata,
  ): Promise<ListPropertyResponse> | Observable<ListPropertyResponse> | ListPropertyResponse {
    return this.queryBus.execute(new GrpcListPropertyQuery(request));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'detailProperty')
  detailProperty(
    request: Id,
    metadata: Metadata,
  ): Promise<PropertyInfo> | Observable<PropertyInfo> | PropertyInfo {
    return this.queryBus.execute(new GrpcDetailPropertyQuery(request));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getListOwnerProperty')
  getListOwnerProperty(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetListOwnerPropertyResponse> | Observable<GetListOwnerPropertyResponse> | GetListOwnerPropertyResponse {
    return this.queryBus.execute(new GetListOwnerPropertyQuery(request));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getOwnerProperty')
  getOwnerProperty(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetOwnerPropertyResponse> | Observable<GetOwnerPropertyResponse> | GetOwnerPropertyResponse {
    return this.queryBus.execute(new GetOwnerPropertyQuery(request));
  }
}
