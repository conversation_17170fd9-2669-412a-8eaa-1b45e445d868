import { Inject, OnModuleInit } from '@nestjs/common';
import {
  REFERRAL_SERVICE_NAME,
  ReferralServiceClient,
} from '@app/proto-schema/internal-proto/referral';
import { INTERNAL_PACKAGE } from 'libs/clients';
import { ClientGrpc } from '@nestjs/microservices';

export class ReferralService implements OnModuleInit {
  public client: ReferralServiceClient;

  constructor(@Inject(INTERNAL_PACKAGE) private clientGrpc: ClientGrpc) {}

  onModuleInit() {
    this.client = this.clientGrpc.getService(REFERRAL_SERVICE_NAME);
  }
}
