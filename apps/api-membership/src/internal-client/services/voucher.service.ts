import {
  VOUCHER_SERVICE_NAME,
  VoucherServiceClient,
} from '@app/proto-schema/index.internal';
import { Inject, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { INTERNAL_PACKAGE } from 'libs/clients';

export class VoucherService implements OnModuleInit {
  public client: VoucherServiceClient;

  constructor(@Inject(INTERNAL_PACKAGE) private clientGrpc: ClientGrpc) {}

  onModuleInit() {
    this.client =
      this.clientGrpc.getService<VoucherServiceClient>(VOUCHER_SERVICE_NAME);
  }
}
