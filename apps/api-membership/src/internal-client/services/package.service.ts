import {
  PACKAGE_SERVICE_NAME,
  PackageServiceClient,
} from '@app/proto-schema/index.internal';
import { Inject, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { INTERNAL_PACKAGE } from 'libs/clients';

export class PackageService implements OnModuleInit {
  public client: PackageServiceClient;

  constructor(@Inject(INTERNAL_PACKAGE) private clientGrpc: ClientGrpc) {}

  onModuleInit() {
    this.client =
      this.clientGrpc.getService<PackageServiceClient>(PACKAGE_SERVICE_NAME);
  }
}
