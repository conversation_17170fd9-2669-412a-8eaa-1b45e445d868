import { Inject, OnModuleInit } from '@nestjs/common';
import {
  DEMO_CODE_SERVICE_NAME,
  DemoCodeServiceClient,
} from '@app/proto-schema/internal-proto/demo-code';
import { INTERNAL_PACKAGE } from '../../../../../libs/clients';
import { ClientGrpc } from '@nestjs/microservices';

export class DemoCodeService implements OnModuleInit {
  public client: DemoCodeServiceClient;

  constructor(@Inject(INTERNAL_PACKAGE) private clientGrpc: ClientGrpc) {}

  onModuleInit() {
    this.client = this.clientGrpc.getService(DEMO_CODE_SERVICE_NAME);
  }
}
