import { Module } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';
import { InternalClient } from 'libs/clients/internal.client';
import { PlanGrpcService } from './plan.service';
import { InternalServices } from './services';
import { BundleGrpcService } from './budle.service';
import { OrderPaymentGrpcService } from './order-payment.service';
import { DeviceGrpcService } from './device.service';
import { LicenseGrpcService } from './license.service';
import { PropertyGrpcService } from './property.service';
import { AuthGrpcService } from './auth.service';
import { UserGrpcService } from './user.service';
import { ConfigService } from '@nestjs/config';
import { INTERNAL_PACKAGE } from 'libs/clients';
import { JobPositionGrpcService } from './job-position.service';
import { ConfigurationGrpcService } from '../order/services/configuration.grpc.service';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) =>
          InternalClient(configService),
        name: INTERNAL_PACKAGE,
      },
    ]),
  ],
  providers: [
    PlanGrpcService,
    BundleGrpcService,
    OrderPaymentGrpcService,
    DeviceGrpcService,
    LicenseGrpcService,
    PropertyGrpcService,
    AuthGrpcService,
    UserGrpcService,
    JobPositionGrpcService,
    ConfigurationGrpcService,
    ...InternalServices,
  ],
  exports: [
    PlanGrpcService,
    BundleGrpcService,
    OrderPaymentGrpcService,
    DeviceGrpcService,
    LicenseGrpcService,
    PropertyGrpcService,
    AuthGrpcService,
    UserGrpcService,
    JobPositionGrpcService,
    ConfigurationGrpcService,
    ...InternalServices,
  ],
})
export class InternalClientModule {}
