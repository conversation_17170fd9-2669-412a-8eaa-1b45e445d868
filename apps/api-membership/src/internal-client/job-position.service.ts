import { Empty, Id } from "@app/proto-schema/index.common";
import { JobPosition, ListJobPositionResponse, JOB_POSITION_SERVICE_NAME, JobPositionServiceClient } from "@app/proto-schema/index.internal";
import { Metadata } from "@grpc/grpc-js";
import { Inject, Injectable } from "@nestjs/common";
import { ClientGrpc } from "@nestjs/microservices";
import { Observable } from "rxjs";

@Injectable()
export class JobPositionGrpcService {
  private jobPositionService: JobPositionServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.jobPositionService = this.client.getService<JobPositionServiceClient>(JOB_POSITION_SERVICE_NAME);
  }

  listJobPosition(request: Empty): Observable<ListJobPositionResponse> {
    const meta = new Metadata();    
    return this.jobPositionService.listJobPosition(request, meta);
  }

  detailJobPosition(request: Id): Observable<JobPosition> {
    const meta = new Metadata();
    return this.jobPositionService.detailJobPosition(request, meta);
  }
}