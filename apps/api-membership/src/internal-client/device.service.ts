import {
  Bund<PERSON>,
  <PERSON>undleRequest,
  ListBundleResponse,
  BUNDLE_SERVICE_NAME,
  BundleServiceClient,
  DeviceServiceClient,
  DEVICE_SERVICE_NAME,
  Device,
  ListDeviceResponse,
  GetTotalDeviceRequest,
  GetTotalDeviceResponse,
  GetActivationsByPropertyRequest,
  GetActivationsByPropertyResponse,
} from '@app/proto-schema/index.internal';
import { Id, Query } from '@app/proto-schema/index.common';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';

@Injectable()
export class DeviceGrpcService {
  private deviceService: DeviceServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.deviceService =
      this.client.getService<DeviceServiceClient>(DEVICE_SERVICE_NAME);
  }

  getDevice(request: Query): Observable<Device> {
    const meta = new Metadata();
    return this.deviceService.getDevice(request, meta);
  }

  getDevicesByPropertyIn(request: Query): Observable<ListDeviceResponse> {
    const meta = new Metadata();
    return this.deviceService.getDevicesByPropertyIn(request, meta);
  }

  getTotalDevice(
    request: GetTotalDeviceRequest,
  ): Observable<GetTotalDeviceResponse> {
    const meta = new Metadata();
    const cek = this.deviceService.getTotalDevice(request, meta);
    console.log(cek);
    return cek;
  }

  getActivationsByProperty(request: GetActivationsByPropertyRequest): Observable<GetActivationsByPropertyResponse> {
    const meta = new Metadata();
    return this.deviceService.getActivationsByProperty(request, meta);
  }
}
