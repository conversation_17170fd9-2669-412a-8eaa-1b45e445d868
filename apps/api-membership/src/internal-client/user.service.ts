import {
  UserServiceClient,
  USER_SERVICE_NAME,
  CreateUserRequest,
  UpdateUserRequest,
  UpdateUserPasswordRequest,
  GetUsersByPropertyRequest,
  GetUsersByPropertyResponse,
  GetUserByIdResponse,
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { Status } from '@app/proto-schema/index.common';
import { common } from '@app/proto-schema';

@Injectable()
export class UserGrpcService {
  private userService: UserServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.userService =
      this.client.getService<UserServiceClient>(USER_SERVICE_NAME);
  }

  createUser(request: CreateUserRequest): Observable<Status> {
    const meta = new Metadata();
    return this.userService.createUser(request, meta);
  }

  updateUser(request: UpdateUserRequest): Observable<Status> {
    const meta = new Metadata();
    return this.userService.updateUser(request, meta);
  }

  updateUserPassword(request: UpdateUserPasswordRequest): Observable<Status> {
    const meta = new Metadata();
    return this.userService.updateUserPassword(request, meta);
  }

  deleteUser(id: common.Id): Observable<Status> {
    const meta = new Metadata();
    return this.userService.deleteUser(id, meta);
  }

  getUsersByPropertyId(request: GetUsersByPropertyRequest): Observable<GetUsersByPropertyResponse> {
    const meta = new Metadata();
    return this.userService.getUsersByPropertyId(request, meta);
  }

  getUserById(id: common.Id): Observable<GetUserByIdResponse> {
    const meta = new Metadata();
    return this.userService.getUserById(id, meta);
  }
}
