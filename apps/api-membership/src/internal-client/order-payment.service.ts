import { B<PERSON><PERSON>, BundleRequest, ListBundleResponse, BUNDLE_SERVICE_NAME, BundleServiceClient, OrderPaymentServiceClient, ORDER_PAYMENT_SERVICE_NAME, CheckoutResp } from "@app/proto-schema/index.internal";
import { Id, Status } from "@app/proto-schema/index.common";
import { Metadata } from "@grpc/grpc-js";
import { Inject, Injectable } from "@nestjs/common";
import { ClientGrpc } from "@nestjs/microservices";
import { Observable } from "rxjs";

@Injectable()
export class OrderPaymentGrpcService {
  private orderPaymentService: OrderPaymentServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.orderPaymentService = this.client.getService<OrderPaymentServiceClient>(ORDER_PAYMENT_SERVICE_NAME);
  }

  cancelOrder(paymentId: string, reason: string): Observable<Status> {
    const meta = new Metadata();    
    return this.orderPaymentService.cancelOrder({ paymentId, reason }, meta);
  }

  orderCheckout(request: Id): Observable<CheckoutResp> {
    const meta = new Metadata();    
    return this.orderPaymentService.orderCheckout(request, meta);
  }
}
