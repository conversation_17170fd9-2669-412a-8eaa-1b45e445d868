import { License, LicenseRequest, ListLicenseResponse, LICENSE_SERVICE_NAME, LicenseServiceClient, SetLicenseRequest } from "@app/proto-schema/index.internal";
import { Id, Status } from "@app/proto-schema/index.common";
import { Metadata } from "@grpc/grpc-js";
import { Inject, Injectable } from "@nestjs/common";
import { ClientGrpc } from "@nestjs/microservices";
import { Observable } from "rxjs";

@Injectable()
export class LicenseGrpcService {
  private licenseService: LicenseServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.licenseService = this.client.getService<LicenseServiceClient>(LICENSE_SERVICE_NAME);
  }

  listLicense(request: LicenseRequest): Observable<ListLicenseResponse> {
    const meta = new Metadata();    
    return this.licenseService.listLicense(request, meta);
  }

  getLicense(request: Id): Observable<License> {
    const meta = new Metadata();
    return this.licenseService.getLicense(request, meta);
  }

  licenseByPropertyTypeId(request: LicenseRequest): Observable<ListLicenseResponse> {
    const meta = new Metadata();
    return this.licenseService.licenseByPropertyType(request, meta);
  }

  setPerformingLicense(request: SetLicenseRequest): Observable<Status> {
    const meta = new Metadata();
    return this.licenseService.setPerformingLicense(request, meta);
  }
}
