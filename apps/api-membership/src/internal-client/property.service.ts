import {
  AssignPropertyRequest,
  PropertyServiceClient,
  PROPERTY_SERVICE_NAME,
  GetPropertyBillResponse,
  GetPropertyZoneTypeResponse,
  GetPropertyZoneTypeRequest,
  GetIsActivateAllUsedRequest,
  GetIsActivateAllUsedResponse,
  GetPropertyInRequest,
  GetPropertyTypeInResponse,
  PropertySummaryResponse,
} from '@app/proto-schema/index.internal';
import { Id, Status } from '@app/proto-schema/index.common';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';

@Injectable()
export class PropertyGrpcService {
  private propertyService: PropertyServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.propertyService = this.client.getService<PropertyServiceClient>(
      PROPERTY_SERVICE_NAME,
    );
  }

  deleteProperty(request: Id): Observable<Status> {
    const meta = new Metadata();
    return this.propertyService.deleteProperty(request, meta);
  }

  assignProperty(request: AssignPropertyRequest): Observable<Status> {
    const meta = new Metadata();
    return this.propertyService.assignProperty(request, meta);
  }

  getPropertyBill(request: Id): Observable<GetPropertyBillResponse> {
    const meta = new Metadata();
    return this.propertyService.getPropertyBill(request, meta);
  }

  getPropertyZoneType(
    request: GetPropertyZoneTypeRequest,
  ): Observable<GetPropertyZoneTypeResponse> {
    const meta = new Metadata();
    return this.propertyService.getPropertyZoneType(request, meta);
  }

  getIsActivateAllUsed(
    request: GetIsActivateAllUsedRequest,
  ): Observable<GetIsActivateAllUsedResponse> {
    const meta = new Metadata();
    return this.propertyService.getIsActivateAllUsed(request, meta);
  }

  getPropertyIn(
    request: GetPropertyInRequest,
  ): Observable<PropertySummaryResponse> {
    const meta = new Metadata();
    return this.propertyService.getPropertyIn(request, meta);
  }
}
