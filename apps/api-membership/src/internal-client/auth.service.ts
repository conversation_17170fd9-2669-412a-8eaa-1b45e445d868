import { AuthServiceClient, AUTH_SERVICE_NAME, GenerateUserTokenReq, GenerateUserTokenResp, GenerateUserTokenByDeviceIDReq } from "@app/proto-schema/index.internal";
import { Metadata } from "@grpc/grpc-js";
import { Inject, Injectable } from "@nestjs/common";
import { ClientGrpc } from "@nestjs/microservices";
import { Observable } from "rxjs";

@Injectable()
export class AuthGrpcService {
  private authService: AuthServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.authService = this.client.getService<AuthServiceClient>(AUTH_SERVICE_NAME);
  }

  generateUserToken(request: GenerateUserTokenReq): Observable<GenerateUserTokenResp> {
    const meta = new Metadata();    
    return this.authService.generateUserToken(request, meta);
  }

  generateUserTokenByDeviceId(request: GenerateUserTokenByDeviceIDReq): Observable<GenerateUserTokenResp> {
    const meta = new Metadata();    
    return this.authService.generateUserTokenByDeviceId(request, meta);
  }

}
