import { forwardRef, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { RedisModule, RedisModuleOptions } from '@nestjs-modules/ioredis';
import { DlpQueueService } from './services/dlp-queue.service';
import { IntegrationDlpQueueService } from './services/integration-dlp-queue.service';
import { DlpJobProcessor } from './processors/dlp-job.processor';
import { QueueManagementController } from './controllers/queue-management.controller';
import {
  DLP_QUEUE_NAME,
  getDlpQueueConfig,
  getRedisConfig,
  QUEUE_KEY_PREFIX,
} from './config/queue.config';
import { IntegrationModule } from '../integration/integration.module';

@Module({
  imports: [
    ConfigModule,
    HttpModule,
    forwardRef(() => IntegrationModule),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (
        config: ConfigService,
      ): Promise<RedisModuleOptions> => ({
        type: 'single',
        url: config.get<string>('REDIS'),
      }),
      inject: [ConfigService],
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        connection: getRedisConfig(configService),
        defaultJobOptions: getDlpQueueConfig(configService),
        prefix: QUEUE_KEY_PREFIX,
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: DLP_QUEUE_NAME,
    }),
  ],
  controllers: [QueueManagementController],
  providers: [DlpQueueService, IntegrationDlpQueueService, DlpJobProcessor],
  exports: [DlpQueueService, IntegrationDlpQueueService],
})
export class QueueModule {}
