import { Injectable, Logger } from '@nestjs/common';
import { DlpQueueService } from './dlp-queue.service';

@Injectable()
export class IntegrationDlpQueueService {
  private readonly logger = new Logger(IntegrationDlpQueueService.name);

  constructor(private readonly dlpQueueService: DlpQueueService) {}

  async validateLicenseWithRetry(
    code: string,
    metadata?: any,
  ): Promise<string> {
    this.logger.log(`Queuing license validation for code: ${code}`);
    return this.dlpQueueService.validateLicense(code, metadata);
  }

  async activateLicenseWithRetry(
    code: string,
    requestId: string,
    metadata?: any,
  ): Promise<string> {
    this.logger.log(
      `Queuing license activation for code: ${code}, requestId: ${requestId}`,
    );
    return this.dlpQueueService.activateLicense(code, requestId, metadata);
  }

  async requestTemporaryWithRetry(
    payload: any,
    metadata?: any,
  ): Promise<string> {
    this.logger.log('Queuing temporary license request');
    return this.dlpQueueService.requestTemporary(payload, metadata);
  }

  async requestDlmWithRetry(payload: any, metadata?: any): Promise<string> {
    this.logger.log('Queuing DLM license request');
    return this.dlpQueueService.requestDlm(payload, metadata);
  }

  async requestConventionalWithRetry(
    payload: any,
    metadata?: any,
  ): Promise<string> {
    this.logger.log('Queuing conventional license request');
    return this.dlpQueueService.requestConventional(payload, metadata);
  }

  async validateTemporaryWithRetry(
    requestId: string,
    metadata?: any,
  ): Promise<string> {
    this.logger.log(
      `Queuing temporary license validation for requestId: ${requestId}`,
    );
    return this.dlpQueueService.validateTemporary(requestId, metadata);
  }

  async checkLicenseStatusWithRetry(
    requestIds: string,
    metadata?: any,
  ): Promise<string> {
    this.logger.log(
      `Queuing license status check for requestIds: ${requestIds}`,
    );
    return this.dlpQueueService.checkLicenseStatus(requestIds, metadata);
  }

  async validateTmpLicenseWithRetry(
    code: string,
    metadata?: any,
  ): Promise<string> {
    this.logger.log(`Queuing tmp license validation for code: ${code}`);
    return this.dlpQueueService.validateTmpLicense(code, metadata);
  }

  async insertPropertyWithRetry(payload: any, metadata?: any): Promise<string> {
    this.logger.log('Queuing property insertion');
    return this.dlpQueueService.insertProperty(payload, metadata);
  }

  async calculateLicenseWithRetry(
    payload: any,
    metadata?: any,
  ): Promise<string> {
    this.logger.log('Queuing license calculation');
    return this.dlpQueueService.calculateLicense(payload, metadata);
  }

  async getJobStatus(jobId: string) {
    return this.dlpQueueService.getJobStatus(jobId);
  }

  async getQueueStats() {
    return this.dlpQueueService.getQueueStats();
  }
}
