import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { DlpJobData, DlpOperation } from '../interfaces/dlp-job.interface';
import {
  DLP_JOB_PRIORITIES,
  DLP_OPERATION_DELAYS,
  DLP_QUEUE_NAME,
} from '../config/queue.config';

@Injectable()
export class DlpQueueService {
  private readonly logger = new Logger(DlpQueueService.name);

  constructor(
    @InjectQueue(DLP_QUEUE_NAME) private readonly dlpQueue: Queue<DlpJobData>,
  ) {}

  async addDlpJob(
    operation: DlpOperation,
    payload: any,
    options?: {
      priority?: number;
      delay?: number;
      metadata?: any;
    },
  ): Promise<string> {
    const jobData: DlpJobData = {
      operation,
      payload,
      metadata: {
        ...options?.metadata,
        originalTimestamp: Date.now(),
        retryCount: 0,
      },
    };

    const jobOptions = {
      priority: options?.priority || DLP_JOB_PRIORITIES.NORMAL,
      delay: options?.delay || DLP_OPERATION_DELAYS[operation] || 0,
      removeOnComplete: 100,
      removeOnFail: 50,
      attempts: 5,
      backoff: {
        type: 'exponential' as const,
        delay: 1000,
      },
    };

    try {
      const job = await this.dlpQueue.add(
        `dlp-${operation}`,
        jobData,
        jobOptions,
      );

      this.logger.log(`DLP job added to queue: ${operation}`, {
        jobId: job.id,
        operation,
        priority: jobOptions.priority,
        delay: jobOptions.delay,
      });

      return job.id!;
    } catch (error) {
      this.logger.error(`Failed to add DLP job to queue: ${operation}`, {
        operation,
        error: error.message,
      });
      throw error;
    }
  }

  // Convenience methods for specific operations
  async validateLicense(code: string, metadata?: any): Promise<string> {
    return this.addDlpJob(
      DlpOperation.VALIDATE_LICENSE,
      { code },
      { priority: DLP_JOB_PRIORITIES.HIGH, metadata },
    );
  }

  async activateLicense(
    code: string,
    requestId: string,
    metadata?: any,
  ): Promise<string> {
    return this.addDlpJob(
      DlpOperation.ACTIVATE_LICENSE,
      { code, requestId },
      { priority: DLP_JOB_PRIORITIES.HIGH, metadata },
    );
  }

  async requestTemporary(payload: any, metadata?: any): Promise<string> {
    return this.addDlpJob(DlpOperation.REQUEST_TEMPORARY, payload, {
      priority: DLP_JOB_PRIORITIES.NORMAL,
      metadata,
    });
  }

  async requestDlm(payload: any, metadata?: any): Promise<string> {
    return this.addDlpJob(DlpOperation.REQUEST_DLM, payload, {
      priority: DLP_JOB_PRIORITIES.NORMAL,
      metadata,
    });
  }

  async requestConventional(payload: any, metadata?: any): Promise<string> {
    return this.addDlpJob(DlpOperation.REQUEST_CONVENTIONAL, payload, {
      priority: DLP_JOB_PRIORITIES.NORMAL,
      metadata,
    });
  }

  async validateTemporary(requestId: string, metadata?: any): Promise<string> {
    return this.addDlpJob(
      DlpOperation.VALIDATE_TEMPORARY,
      { requestId },
      { priority: DLP_JOB_PRIORITIES.NORMAL, metadata },
    );
  }

  async checkLicenseStatus(
    requestIds: string,
    metadata?: any,
  ): Promise<string> {
    return this.addDlpJob(
      DlpOperation.CHECK_LICENSE_STATUS,
      { requestIds },
      { priority: DLP_JOB_PRIORITIES.LOW, metadata },
    );
  }

  async validateTmpLicense(code: string, metadata?: any): Promise<string> {
    return this.addDlpJob(
      DlpOperation.VALIDATE_TMP_LICENSE,
      { code },
      { priority: DLP_JOB_PRIORITIES.NORMAL, metadata },
    );
  }

  async insertProperty(payload: any, metadata?: any): Promise<string> {
    return this.addDlpJob(DlpOperation.INSERT_PROPERTY, payload, {
      priority: DLP_JOB_PRIORITIES.NORMAL,
      metadata,
    });
  }

  async calculateLicense(payload: any, metadata?: any): Promise<string> {
    return this.addDlpJob(DlpOperation.CALCULATE_LICENSE, payload, {
      priority: DLP_JOB_PRIORITIES.HIGH,
      metadata,
    });
  }

  async listCategory(metadata?: any): Promise<string> {
    return this.addDlpJob(
      DlpOperation.LIST_CATEGORY,
      {},
      { priority: DLP_JOB_PRIORITIES.LOW, metadata },
    );
  }

  async detailCategoryByCode(code: string, metadata?: any): Promise<string> {
    return this.addDlpJob(
      DlpOperation.DETAIL_CATEGORY_BY_CODE,
      { code },
      { priority: DLP_JOB_PRIORITIES.NORMAL, metadata },
    );
  }

  async detailCategoryById(id: string, metadata?: any): Promise<string> {
    return this.addDlpJob(
      DlpOperation.DETAIL_CATEGORY_BY_ID,
      { id },
      { priority: DLP_JOB_PRIORITIES.NORMAL, metadata },
    );
  }

  // Queue management methods
  async getJobStatus(jobId: string) {
    try {
      const job = await this.dlpQueue.getJob(jobId);
      if (!job) {
        return null;
      }

      return {
        id: job.id,
        name: job.name,
        data: job.data,
        progress: job.progress,
        attemptsMade: job.attemptsMade,
        finishedOn: job.finishedOn,
        processedOn: job.processedOn,
        failedReason: job.failedReason,
        returnvalue: job.returnvalue,
      };
    } catch (error) {
      this.logger.error(`Failed to get job status: ${jobId}`, {
        jobId,
        error: error.message,
      });
      throw error;
    }
  }

  async getQueueStats() {
    try {
      const waiting = await this.dlpQueue.getWaiting();
      const active = await this.dlpQueue.getActive();
      const completed = await this.dlpQueue.getCompleted();
      const failed = await this.dlpQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
      };
    } catch (error) {
      this.logger.error('Failed to get queue stats', {
        error: error.message,
      });
      throw error;
    }
  }

  async pauseQueue(): Promise<void> {
    await this.dlpQueue.pause();
    this.logger.log('DLP queue paused');
  }

  async resumeQueue(): Promise<void> {
    await this.dlpQueue.resume();
    this.logger.log('DLP queue resumed');
  }

  async cleanQueue(grace: number = 0): Promise<void> {
    await this.dlpQueue.clean(grace, 100, 'completed');
    await this.dlpQueue.clean(grace, 50, 'failed');
    this.logger.log('DLP queue cleaned');
  }
}
