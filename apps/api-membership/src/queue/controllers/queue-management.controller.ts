import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { DlpQueueService } from '../services/dlp-queue.service';
import { JwtGuard } from '../../auth/guards/jwt.guard';
import { DlpOperation } from '../interfaces/dlp-job.interface';
import { ApiBearerAuth } from '@nestjs/swagger';

@Controller('queue-management')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class QueueManagementController {
  constructor(private readonly dlpQueueService: DlpQueueService) {}

  @Get('stats')
  async getQueueStats() {
    return this.dlpQueueService.getQueueStats();
  }

  @Get('job/:jobId')
  async getJobStatus(@Param('jobId') jobId: string) {
    const status = await this.dlpQueueService.getJobStatus(jobId);
    if (!status) {
      return { message: 'Job not found' };
    }
    return status;
  }

  @Post('pause')
  async pauseQueue() {
    await this.dlpQueueService.pauseQueue();
    return { message: 'Queue paused successfully' };
  }

  @Post('resume')
  async resumeQueue() {
    await this.dlpQueueService.resumeQueue();
    return { message: 'Queue resumed successfully' };
  }

  @Post('clean')
  async cleanQueue(@Query('grace') grace?: string) {
    const graceMs = grace ? parseInt(grace) : 0;
    await this.dlpQueueService.cleanQueue(graceMs);
    return { message: 'Queue cleaned successfully' };
  }

  @Post('add-job')
  async addJob(
    @Body()
    jobData: {
      operation: DlpOperation;
      payload: any;
      priority?: number;
      delay?: number;
      metadata?: any;
    },
  ) {
    const jobId = await this.dlpQueueService.addDlpJob(
      jobData.operation,
      jobData.payload,
      {
        priority: jobData.priority,
        delay: jobData.delay,
        metadata: jobData.metadata,
      },
    );

    return {
      message: 'Job added successfully',
      jobId,
    };
  }

  @Post('test-operations')
  async testOperations() {
    const results = [];

    // Test license validation
    try {
      const validateJobId =
        await this.dlpQueueService.validateLicense('TEST123');
      results.push({
        operation: 'validate_license',
        jobId: validateJobId,
        status: 'queued',
      });
    } catch (error) {
      results.push({
        operation: 'validate_license',
        error: error.message,
        status: 'failed',
      });
    }

    // Test category listing
    try {
      const listJobId = await this.dlpQueueService.listCategory();
      results.push({
        operation: 'list_category',
        jobId: listJobId,
        status: 'queued',
      });
    } catch (error) {
      results.push({
        operation: 'list_category',
        error: error.message,
        status: 'failed',
      });
    }

    // Test license calculation
    try {
      const calculateJobId = await this.dlpQueueService.calculateLicense({
        categoryId: 'TEST_CAT',
        amount: 1000000,
      });
      results.push({
        operation: 'calculate_license',
        jobId: calculateJobId,
        status: 'queued',
      });
    } catch (error) {
      results.push({
        operation: 'calculate_license',
        error: error.message,
        status: 'failed',
      });
    }

    return {
      message: 'Test operations initiated',
      results,
    };
  }
}
