import { Processor, WorkerHost } from '@nestjs/bullmq';
import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import {
  DlpJobData,
  DlpJobResult,
  DlpOperation,
} from '../interfaces/dlp-job.interface';
import { IntegrationDlpService } from '../../integration/services/integration-dlp.service';
import { DLP_QUEUE_NAME } from '../config/queue.config';

@Injectable()
@Processor(DLP_QUEUE_NAME)
export class DlpJobProcessor extends WorkerHost {
  private readonly logger = new Logger(DlpJobProcessor.name);

  constructor(
    @Inject(forwardRef(() => IntegrationDlpService))
    private readonly dlpService: IntegrationDlpService,
  ) {
    super();
  }

  async process(job: Job<DlpJobData>): Promise<DlpJobResult> {
    const startTime = Date.now();
    const { operation, payload, metadata } = job.data;
    const retryCount = job.attemptsMade;

    this.logger.log(`Processing DLP job: ${operation}`, {
      jobId: job.id,
      operation,
      retryCount,
      metadata,
    });

    try {
      let result: any;

      switch (operation) {
        case DlpOperation.VALIDATE_LICENSE:
          result = await this.dlpService.validateLicense(payload.code);
          break;

        case DlpOperation.ACTIVATE_LICENSE:
          result = await this.dlpService.activateLicense(
            payload.code,
            payload.requestId,
          );
          break;

        case DlpOperation.REQUEST_TEMPORARY:
          result = await this.dlpService.requestTemporary(payload);
          break;

        case DlpOperation.REQUEST_DLM:
          result = await this.dlpService.requestDlm(payload);
          break;

        case DlpOperation.REQUEST_CONVENTIONAL:
          result = await this.dlpService.requestConventional(payload);
          break;

        case DlpOperation.VALIDATE_TEMPORARY:
          result = await this.dlpService.validateTemporary(payload.requestId);
          break;

        case DlpOperation.CHECK_LICENSE_STATUS:
          result = await this.dlpService.checkLicenseStatus(payload.requestIds);
          break;

        case DlpOperation.VALIDATE_TMP_LICENSE:
          result = await this.dlpService.validateTmpLicense(payload.code);
          break;

        case DlpOperation.INSERT_PROPERTY:
          result = await this.dlpService.insertProperty(payload);
          break;

        case DlpOperation.CALCULATE_LICENSE:
          result = await this.dlpService.calculateLicense(payload);
          break;

        case DlpOperation.LIST_CATEGORY:
          result = await this.dlpService.listCategory();
          break;

        case DlpOperation.DETAIL_CATEGORY_BY_CODE:
          result = await this.dlpService.DetailCategoryByCode(payload.code);
          break;

        case DlpOperation.DETAIL_CATEGORY_BY_ID:
          result = await this.dlpService.DetailCategoryById(payload.id);
          break;

        default:
          throw new Error(`Unknown DLP operation: ${operation}`);
      }

      const duration = Date.now() - startTime;

      this.logger.log(`DLP job completed successfully: ${operation}`, {
        jobId: job.id,
        operation,
        duration,
        retryCount,
      });

      return {
        success: true,
        data: result,
        metadata: {
          operation,
          executedAt: Date.now(),
          duration,
          retryCount,
        },
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      const isRetryable = this.isRetryableError(error);

      this.logger.error(`DLP job failed: ${operation}`, {
        jobId: job.id,
        operation,
        error: error.message,
        status: error?.response?.status,
        retryCount,
        isRetryable,
        duration,
      });

      if (!isRetryable || retryCount >= 4) {
        this.logger.error(`DLP job permanently failed: ${operation}`, {
          jobId: job.id,
          operation,
          finalRetryCount: retryCount,
        });
      }

      const jobResult: DlpJobResult = {
        success: false,
        error: {
          message: error.message,
          status: error?.response?.status,
          code: error?.code,
          retryable: isRetryable,
        },
        metadata: {
          operation,
          executedAt: Date.now(),
          duration,
          retryCount,
        },
      };

      if (isRetryable && retryCount < 4) {
        throw error;
      }

      return jobResult;
    }
  }

  private isRetryableError(error: any): boolean {
    if (
      error.code === 'ECONNREFUSED' ||
      error.code === 'ENOTFOUND' ||
      error.code === 'ETIMEDOUT'
    ) {
      return true;
    }

    const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
    if (
      error?.response?.status &&
      retryableStatusCodes.includes(error.response.status)
    ) {
      return true;
    }

    if (error?.response?.status === 401) {
      return true;
    }

    if (error?.response?.status >= 400 && error?.response?.status < 500) {
      return false;
    }

    return true;
  }
}
