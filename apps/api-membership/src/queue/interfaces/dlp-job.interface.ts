export interface DlpJobData {
  operation: DlpOperation;
  payload: any;
  metadata?: {
    userId?: string;
    orderId?: string;
    requestId?: string;
    originalTimestamp: number;
    retryCount?: number;
  };
}

export enum DlpOperation {
  VALIDATE_LICENSE = 'validate_license',
  ACTIVATE_LICENSE = 'activate_license',
  REQUEST_TEMPORARY = 'request_temporary',
  REQUEST_DLM = 'request_dlm',
  REQUEST_CONVENTIONAL = 'request_conventional',
  VALIDATE_TEMPORARY = 'validate_temporary',
  CHECK_LICENSE_STATUS = 'check_license_status',
  VALIDATE_TMP_LICENSE = 'validate_tmp_license',
  INSERT_PROPERTY = 'insert_property',
  CALCULATE_LICENSE = 'calculate_license',
  LIST_CATEGORY = 'list_category',
  DETAIL_CATEGORY_BY_CODE = 'detail_category_by_code',
  DETAIL_CATEGORY_BY_ID = 'detail_category_by_id',
}

export interface DlpJobResult {
  success: boolean;
  data?: any;
  error?: {
    message: string;
    status?: number;
    code?: string;
    retryable: boolean;
  };
  metadata: {
    operation: DlpOperation;
    executedAt: number;
    duration: number;
    retryCount: number;
  };
}

export interface DlpQueueOptions {
  attempts: number;
  backoff: {
    type: 'exponential';
    delay: number;
  };
  removeOnComplete: number;
  removeOnFail: number;
}
