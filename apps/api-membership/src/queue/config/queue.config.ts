import { ConfigService } from '@nestjs/config';
import { DlpQueueOptions } from '../interfaces/dlp-job.interface';

export const DLP_QUEUE_NAME = 'dlp-integration-queue';
export const QUEUE_KEY_PREFIX = '{dlp-queue}:';

export const getDlpQueueConfig = (
  configService: ConfigService,
): DlpQueueOptions => ({
  attempts: 5,
  backoff: {
    type: 'exponential',
    delay: 1000,
  },
  removeOnComplete: 100,
  removeOnFail: 50,
});

export const getRedisConfig = (configService: ConfigService) => ({
  host: configService.get<string>('REDIS_HOST') || '127.0.0.1',
  port: configService.get<number>('REDIS_PORT') || 6379,
  password: configService.get<string>('REDIS_PASS'),
  db: 0,
});

export const DLP_JOB_PRIORITIES = {
  HIGH: 10,
  NORMAL: 5,
  LOW: 1,
} as const;

// Delay configurations by mili
export const DLP_OPERATION_DELAYS = {
  VALIDATE_LICENSE: 0,
  ACTIVATE_LICENSE: 0,
  REQUEST_TEMPORARY: 1000,
  REQUEST_DLM: 2000,
  REQUEST_CONVENTIONAL: 2000,
  VALIDATE_TEMPORARY: 500,
  CHECK_LICENSE_STATUS: 5000,
  VALIDATE_TMP_LICENSE: 0,
  INSERT_PROPERTY: 1000,
  CALCULATE_LICENSE: 0,
  LIST_CATEGORY: 0,
  DETAIL_CATEGORY_BY_CODE: 0,
  DETAIL_CATEGORY_BY_ID: 0,
} as const;
