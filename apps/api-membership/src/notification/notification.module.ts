import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { NotificationController } from './notification.controller';
import { NotificationQueryHandlers } from './queries/handlers';
import { NotificationCommandHandlers } from './commands/handlers';
import { NotificationEventHandlers } from './events';
import { EventModule } from '../event/event.module';

@Module({
  imports: [CqrsModule, EventModule],
  controllers: [NotificationController],
  providers: [
    ...NotificationQueryHandlers,
    ...NotificationCommandHandlers,
    ...NotificationEventHandlers
  ],
})
export class NotificationModule {}
