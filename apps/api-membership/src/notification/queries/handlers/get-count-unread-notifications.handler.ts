import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetCountUnreadNotificationsQuery, GetNotificationsQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetCountUnreadNotificationsQuery)
export class GetCountUnreadNotificationsHandler implements IQueryHandler<GetCountUnreadNotificationsQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetCountUnreadNotificationsQuery) {
    const { user } = query;
    const count = await this.prisma.notification.count({
      where: {
        AND: [
          {
            isRead: false
          },
          {
            toId: user.id
          }
        ]
      }
    })

    return {
      count
    };
  }
}
