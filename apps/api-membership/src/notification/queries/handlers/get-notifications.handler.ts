import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetNotificationsQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetNotificationsQuery)
export class GetNotificationsHandler implements IQueryHandler<GetNotificationsQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetNotificationsQuery) {
    const { user } = query;
    const items = await this.prisma.notification.findMany(
      {
        where: {
          toId: user.id 
        },
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          title: true,
          url: true,
          message: true,
          type: true,
          tags: true,
          isRead: true,
          createdAt: true,
          updatedAt: true
        }
      },
    )

    return items;
  }
}
