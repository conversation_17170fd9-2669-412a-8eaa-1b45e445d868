import { Controller } from '@nestjs/common';
import { EventPublisher } from '@nestjs/cqrs';
import { EventPattern } from '@nestjs/microservices';
import { NotificationModel } from './models/notification.model';
import { PrismaService } from 'nestjs-prisma';

@Controller()
export class NotificationEventController {
  constructor(
    private readonly publisher: EventPublisher,
    private readonly prismaService: PrismaService
  ) {}

  @EventPattern('WEB-NOTIFICATION')
  async handleRemotePlayerEvent(data: Record<string, unknown>) {
    const userProperty = await this.prismaService.userProperty.findFirst({
      where: {
        propertyId: data.to as string
      },
      include: {
        user: true,
        property: true
      }
    });

    if (userProperty) {
      const notificationModel = this.publisher.mergeClassContext(NotificationModel);
      const hookModel = new notificationModel();
      hookModel.sendWebNotification({
        to: userProperty.userId,
        event: 'notification',
        data: {
          message: `device was activated`,
          type: 'device_active',
          data: data.value
        }
      });
    }
    return;
  }
}