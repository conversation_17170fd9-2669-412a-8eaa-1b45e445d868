import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { ReadOneNotificationCommand } from '../impl';
import { InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(ReadOneNotificationCommand)
export class ReadOneNotificationHandler
  implements ICommandHandler<ReadOneNotificationCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: ReadOneNotificationCommand) {
    const { user, id } = command;
    
    try {
      await this.prisma.notification.update({
        where: {
          id: id,
          toId: user.id
        },
        data: {
          isRead: true,
        },
      });

      return 'successfully read one notification';
    } catch (e) {
      throw new InternalServerErrorException();
    }
  }
}
