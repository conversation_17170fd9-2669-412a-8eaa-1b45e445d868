import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>andler } from '@nestjs/cqrs';
import { ReadAllNotificationCommand, TestNotificationCommand } from '../impl';
import { InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { WebGateway } from 'apps/api-membership/src/event/web.gateway';

@CommandHandler(TestNotificationCommand)
export class TestNotificationHandler
  implements ICommandHandler<TestNotificationCommand>
{
  constructor(
    private readonly prismaService: PrismaService,
    private readonly webGateway: WebGateway
  ) {}

  async execute(command: TestNotificationCommand) {
    const { user, args } = command;
    try {
      this.webGateway.server.to(user.id).emit(args.eventName, args.data);

      await this.prismaService.notification.create({
        data: {
          fromId: user.id,
          toId: user.id,
          title: args.eventName,
          message: args.data,
          type: args.eventName,
          tags: [args.eventName]
        }
      })

      return 'successfully send notification';
    } catch (e) {
      console.log(e)
      throw new InternalServerErrorException();
    }
  }
}
