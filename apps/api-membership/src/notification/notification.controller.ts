import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { ReadAllNotificationCommand, ReadOneNotificationCommand, TestNotificationCommand } from './commands/impl';
import { GetCountUnreadNotificationsQuery, GetNotificationsQuery } from './queries/impl';
import { Throttle } from '@nestjs/throttler';
import { TestNotificationDto } from './dto/test-notification.dto';

@Controller('notification')
@ApiTags('Notification')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class NotificationController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Post('read-all')
  @Throttle({ default: { limit: 50, ttl: 60000 } })
  readAllNotification(@User() user: ICurrentUser) {
    return this.commandBus.execute(
      new ReadAllNotificationCommand(user),
    );
  }

  @Patch(':id/read')
  @Throttle({ default: { limit: 50, ttl: 60000 } })
  readOneNotification(
    @User() user: ICurrentUser,
    @Param('id') id: string
  ) {
    return this.commandBus.execute(
      new ReadOneNotificationCommand(user, id),
    );
  }

  @Get()
  findAll(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetNotificationsQuery(user));
  }

  @Get('count-unread')
  countUnread(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetCountUnreadNotificationsQuery(user));
  }

  @Post('test')
  @Throttle({ default: { limit: 50, ttl: 60000 } })
  testNotification(
    @User() user: ICurrentUser,
    @Body() payload: TestNotificationDto
  ) {
    return this.commandBus.execute(
      new TestNotificationCommand(user, payload),
    );
  }
}
