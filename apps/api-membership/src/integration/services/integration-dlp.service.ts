import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { ConfigService } from '@nestjs/config';
import { response } from 'express';

interface TokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

@Injectable()
export class IntegrationDlpService {
  private readonly tokenKey = 'dlp-integration-token';
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRedis() private readonly redis: Redis,
  ) {
    this.baseUrl = this.configService.get<string>('DLP_HOST');
  }

  private async makeRequest<T>(
    method: 'get' | 'post',
    url: string,
    options: { payload?: any } = {},
  ) {
    const token = await this.getToken();
    const config = {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    };

    let response;
    try {
      if (method === 'get') {
        response = await firstValueFrom(
          this.httpService.get<T>(`${this.baseUrl}${url}`, config),
        );
      } else {
        response = await firstValueFrom(
          this.httpService.post<T>(
            `${this.baseUrl}${url}`,
            options.payload,
            config,
          ),
        );
      }
      return response.data;
    } catch (error: any) {
      if (error?.response?.status === 401) {
        console.log('[DLP] Token expired, refreshing and retrying request');
        const newToken = await this.getToken(token);
        config.headers.Authorization = `Bearer ${newToken}`;

        if (method === 'get') {
          response = await firstValueFrom(
            this.httpService.get<T>(`${this.baseUrl}${url}`, config),
          );
        } else {
          response = await firstValueFrom(
            this.httpService.post<T>(
              `${this.baseUrl}${url}`,
              options.payload,
              config,
            ),
          );
        }
        return response.data;
      }
      if (error?.response?.status === 404) {
        console.error(`[DLP] Resource not found at ${url}`);
        return null;
      }
      throw error;
    }
  }

  async listCategory() {
    console.log('[DLP] Fetching category list');
    return this.makeRequest('get', '/category/list');
  }

  async validateLicense(code: string) {
    console.log(`[DLP] Fetching validate license for code: ${code}`);
    return this.makeRequest('post', `/license/validate`, { payload: { code } });
  }

  async DetailCategoryByCode(code: string) {
    console.log(`[DLP] Fetching category details for code: ${code}`);
    return this.makeRequest('get', `/category/code/${code}`);
  }

  async DetailCategoryById(id: string) {
    console.log(`[DLP] Fetching category details for ID: ${id}`);
    return this.makeRequest('get', `/category/${id}`);
  }

  async activateLicense(code: string, requestId: string) {
    console.log(`[DLP] Fetching validate license for code: ${code}`);
    return this.makeRequest('post', `/license/dlm-activate`, {
      payload: { code, requestId },
    });
  }

  async requestTemporary(payload: any) {
    try {
      const response = await this.makeRequest('post', `/temporary-license`, {
        payload,
      });
      return response.data;
    } catch (error: any) {
      console.error('failed requesting temporary license', {
        status: error?.response?.status,
        data: error?.response?.data,
        message: error?.message,
      });

      if (error?.response?.status === 400) {
        const errorMessage =
          error?.response?.data?.message || 'Invalid request';

        throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
      }
      throw error;
    }
  }

  async requestDlm(payload: any) {
    try {
      const response = await this.makeRequest(
        'post',
        `/license/dlm-request-submission`,
        {
          payload,
        },
      );
      return response.data;
    } catch (error: any) {
      console.error('failed requesting dlm license', {
        status: error?.response?.status,
        data: error?.response?.data,
      });
    }
  }

  async requestConventional(payload: any) {
    try {
      const response = await this.makeRequest(
        'post',
        `/license/conventional-request-submission`,
        {
          payload,
        },
      );
      return response.data;
    } catch (error: any) {
      console.error('failed requesting conventional license', {
        status: error?.response?.status,
        data: error?.response?.data,
      });
    }
  }

  async validateTemporary(requestId: string) {
    console.log(`request ID: ${requestId}`);
    return this.makeRequest('post', `/temporary-license/validate`, {
      payload: { requestId },
    });
  }

  async checkLicenseStatus(requestIds: string) {
    console.log(`request ID: ${requestIds}`);
    return this.makeRequest('post', `/license/check-requestiIds`, {
      payload: { requestIds },
    });
  }

  async validateTmpLicense(code: string) {
    return this.makeRequest('post', `/license/check`, {
      payload: { code },
    });
  }

  async insertProperty(payload: any) {
    try {
      const response = await this.makeRequest(
        'post',
        `/license/insert-business-unit`,
        {
          payload,
        },
      );
      return response.data;
    } catch (error: any) {
      console.error('failed insert business unit', {
        status: error?.response?.status,
        data: error?.response?.data,
      });
    }
  }

  async calculateLicense(payload: any) {
    console.log('[DLP] Calculating license', { payload });
    try {
      const response = await this.makeRequest('post', '/category/calculate', {
        payload,
      });
      return response;
    } catch (error: any) {
      console.error('[DLP] Calculate license error:', {
        status: error?.response?.status,
        data: error?.response?.data,
        message: error?.message,
      });

      if (error?.response?.status === 400) {
        const errorMessage =
          error?.response?.data?.message || 'Invalid calculation request';
        if (errorMessage.includes('Amount is not within any defined ranges')) {
          throw new HttpException(
            'The specified amount is not within the allowed ranges for this category',
            HttpStatus.BAD_REQUEST,
          );
        }
        throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
      }
      throw error;
    }
  }

  private async getToken(expiredToken?: string): Promise<string> {
    try {
      console.log(
        '[DLP] Getting token',
        expiredToken ? '(refresh)' : '(check cache)',
      );
      let existingToken: string | null = null;

      try {
        existingToken = await this.redis.get(this.tokenKey);
        console.log(
          '[DLP] Redis token status:',
          existingToken ? 'found' : 'not found',
        );
      } catch (redisError) {
        console.error('[DLP] Redis error:', redisError);
      }

      if (!existingToken) {
        console.log('[DLP] No existing token, requesting new one');
        return await this.requestNewToken();
      }

      const parsedToken = JSON.parse(existingToken) as TokenResponse;

      if (expiredToken) {
        console.log('[DLP] Token expired, refreshing');
        return await this.refreshToken(parsedToken.refresh_token);
      }

      console.log('[DLP] Using existing token');
      return parsedToken.access_token;
    } catch (error) {
      console.error('[DLP] Token retrieval error:', error);
      throw new HttpException('Authentication failed', HttpStatus.UNAUTHORIZED);
    }
  }

  private async requestNewToken(): Promise<string> {
    try {
      const tokenUrl = `${this.baseUrl}/auth/token`;
      const payload = {
        client_id: this.configService.get<string>('DLP_CLIENT_ID'),
        client_secret: this.configService.get<string>('DLP_CLIENT_SECRET'),
      };

      console.log('[DLP] Requesting new token from:', tokenUrl);

      const response = await firstValueFrom(
        this.httpService.post<TokenResponse>(tokenUrl, payload),
      );

      if (!response?.data?.access_token) {
        console.error('[DLP] Invalid token response:', response?.data);
        throw new Error('Invalid token response format');
      }

      try {
        const tokenData = JSON.stringify(response.data);
        await this.redis.set(this.tokenKey, tokenData);
        await this.redis.expire(this.tokenKey, response.data.expires_in - 300);
        console.log('[DLP] Successfully stored new token in Redis');
      } catch (redisError) {
        console.error('[DLP] Redis storage error:', redisError);
      }

      return response.data.access_token;
    } catch (error: any) {
      console.error('[DLP] New token request error:', {
        status: error?.response?.status,
        data: error?.response?.data,
        message: error?.message,
      });

      if (error?.response?.status === 404) {
        throw new HttpException(
          'DLP authentication endpoint not found',
          HttpStatus.NOT_FOUND,
        );
      }

      throw new HttpException(
        'Authentication failed: ' +
          (error?.response?.data?.message || error?.message),
        error?.response?.status || HttpStatus.UNAUTHORIZED,
      );
    }
  }

  private async refreshToken(refreshToken: string): Promise<string> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<TokenResponse>(
          `${this.baseUrl}/auth/refresh`,
          {},
          {
            headers: {
              Authorization: `Bearer ${refreshToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (!response?.data?.access_token) {
        console.log(
          '[DLP] Invalid refresh token response, requesting new token',
        );
        return await this.requestNewToken();
      }

      try {
        await this.redis.set(this.tokenKey, JSON.stringify(response.data));
        await this.redis.expire(this.tokenKey, response.data.expires_in - 300);
        console.log('[DLP] Successfully stored refreshed token in Redis');
      } catch (redisError) {
        console.error('[DLP] Redis storage error:', redisError);
      }

      return response.data.access_token;
    } catch (error: any) {
      console.error('[DLP] Token refresh failed:', {
        status: error?.response?.status,
        data: error?.response?.data,
        message: error?.message,
      });
      return await this.requestNewToken();
    }
  }
}
