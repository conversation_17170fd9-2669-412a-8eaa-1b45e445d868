import { Module } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';
import { InternalClient } from 'libs/clients/internal.client';
import { IntegrationServices } from './services';
import { HttpModule } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { INTERNAL_PACKAGE } from 'libs/clients';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) =>
          InternalClient(configService),
        name: INTERNAL_PACKAGE,
      },
    ]),
    HttpModule,
  ],
  providers: [...IntegrationServices],
  exports: [...IntegrationServices],
})
export class IntegrationModule {}
