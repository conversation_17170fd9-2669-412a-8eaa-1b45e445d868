import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { MailerService } from '@nestjs-modules/mailer';
import { SendEmailNotificationEvent } from '../impl';

@EventsHandler(SendEmailNotificationEvent)
export class SendEmailNotificationHandler
  implements IEventHandler<SendEmailNotificationEvent>
{
  constructor(
    private readonly mailerService: MailerService,
  ) {}

  async handle(event: SendEmailNotificationEvent) {
    const { email, reason } = event;
    try {

      //await this.mailerService.sendMail({
      //  to: email,
      //  subject: 'Public Performing License',
      //  template: 'license-approval',
      //  context: {
      //    reason,
      //  },
      //});
      await this.mailerService.sendMail({
        to: email,
        subject: 'Public Performing License',
        text: reason,
      });
      return 'License email notification sended';
    } catch (error) {
      console.error('Error in SendEmailNotificationHandler:', error);
    }
  }
}
