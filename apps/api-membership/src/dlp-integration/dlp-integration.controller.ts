import { Body, Controller, Post, UseGuards } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { DlpIntegrationGuard } from "../auth/guards/integration.guard";
import { LicenseSubmissionDto } from "./dto/license-submission.dto";
import { CommandBus } from "@nestjs/cqrs";
import { LicenseSubmissionCommand } from "./commands";

@Controller('dlp-integration')
@ApiTags('DLP integration')
@UseGuards(DlpIntegrationGuard)
export class DlpIntegrationController {
  constructor(
    private commandBus: CommandBus
  ) {}

  @Post('license-submission')
  async nextQueue(
    @Body() payload: LicenseSubmissionDto,
  ) {
    return this.commandBus.execute(
      new LicenseSubmissionCommand(payload),
    );
  }
}
