import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { LicenseSubmissionCommand } from '../impl';
import {
  LicenseStatus,
  LicenseSubmissionStatus,
  LicenseType,
} from '@app/common';
import { DlpIntegrationModel } from '../../models/dlp-integration.model';
import {
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { WebGateway } from 'apps/api-membership/src/event/web.gateway';
import { PackageService } from 'apps/api-membership/src/internal-client/services';
import { Metadata } from '@grpc/grpc-js';
import { lastValueFrom } from 'rxjs';

@CommandHandler(LicenseSubmissionCommand)
export class LicenseSubmissionHandler
  implements ICommandHandler<LicenseSubmissionCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher,
    private readonly webGateway: WebGateway,
    private packageService: PackageService,
  ) {}

  async execute(command: LicenseSubmissionCommand) {
    const { args } = command;

    const property = await this.prisma.property.findFirst({
      where: {
        requestId: args.requestId,
      },
      include: {
        users: {
          where: {
            user: {
              AND: [
                {
                  isAdmin: true,
                },
                {
                  parentId: null,
                },
              ],
            },
          },
          include: {
            user: true,
          },
        },
      },
    });

    if (!property) {
      throw new NotFoundException('property not found');
    }

    const dlpIntegrationModel =
      this.publisher.mergeClassContext(DlpIntegrationModel);
    const dlpIntegration = new dlpIntegrationModel();

    switch (args.licenseType) {
      case LicenseType.CONVENTIONAL:
        try {
          if (args.status === LicenseSubmissionStatus.APPROVED) {
            const prop = await this.prisma.property.findFirst({
              where: {
                id: property.id,
              },
              include: {
                configuration: true,
              },
            });

            await this.prisma.property.update({
              where: {
                id: property.id,
              },
              data: {
                licenseType: args.licenseType,
              },
            });

            if (
              args.status === LicenseSubmissionStatus.APPROVED &&
              args.licenseKey &&
              args.expiredAt &&
              args.licenseStatus === LicenseStatus.REGISTERED
            ) {
              const meta = new Metadata();
              await lastValueFrom(
                this.packageService.client.setPackageLicenseTemporaryExpiration(
                  {
                    expiredAt: new Date(
                      new Date(args.expiredAt).setFullYear(
                        new Date(args.expiredAt).getFullYear() + 1,
                      ),
                    ).getTime(),
                    propertyId: prop.id,
                    licenseType: args.licenseType,
                  },
                  meta,
                ),
              );
            }
          }

          dlpIntegration.sendEmail(
            property.users[0].user.email,
            args.status != LicenseSubmissionStatus.REJECTED
              ? `Your license has been ${args.status}`
              : `Your license has been rejected, reason : ${args.reason}`,
          );
        } catch (err) {
          console.log(err.message);
          throw new InternalServerErrorException('internal server error');
        }
        break;
      case LicenseType.DLM:
        try {
          if (args.status === LicenseSubmissionStatus.APPROVED) {
            const prop = await this.prisma.property.findFirst({
              where: {
                id: property.id,
              },
              include: {
                configuration: true,
              },
            });

            await this.prisma.property.update({
              where: {
                id: property.id,
              },
              data: {
                requestId: args.requestId,
                licenseType: args.licenseType,
              },
            });

            if (
              args.status === LicenseSubmissionStatus.APPROVED &&
              args.licenseKey &&
              args.expiredAt &&
              args.licenseStatus === LicenseStatus.REGISTERED
            ) {
              const existingConfig = await this.prisma.configuration.findUnique(
                {
                  where: {
                    propertyId: property.id,
                  },
                },
              );

              const currentOptions =
                typeof existingConfig?.options === 'object' &&
                existingConfig?.options !== null
                  ? (existingConfig.options as Record<string, any>)
                  : {};

              await this.prisma.configuration.update({
                where: {
                  propertyId: property.id,
                },
                data: {
                  options: {
                    ...currentOptions,
                    licenseNumber: args?.licenseKey,
                  },
                },
              });

              await this.prisma.property.update({
                where: {
                  id: property.id,
                },
                data: {
                  licenseKey: args?.licenseKey,
                },
              });
              const meta = new Metadata();
              await lastValueFrom(
                this.packageService.client.setPackageLicenseTemporaryExpiration(
                  {
                    expiredAt: new Date(
                      new Date(args.expiredAt).setFullYear(
                        new Date(args.expiredAt).getFullYear() + 1,
                      ),
                    ).getTime(),
                    propertyId: prop.id,
                    licenseType: args.licenseType,
                  },
                  meta,
                ),
              );
            }
          }

          dlpIntegration.sendEmail(
            property.users[0].user.email,
            args.status != LicenseSubmissionStatus.REJECTED
              ? `Your license has been ${args.status}`
              : `Your license has been rejected, reason : ${args.reason}`,
          );
        } catch (err) {
          console.log(err);
          throw new InternalServerErrorException('internal server error');
        }
        break;
      case LicenseType.TEMPORARY:
        try {
          if (args.status === LicenseSubmissionStatus.APPROVED) {
            const prop = await this.prisma.property.findFirst({
              where: {
                id: property.id,
              },
              include: {
                configuration: true,
              },
            });

            await this.prisma.property.update({
              where: {
                id: property.id,
              },
              data: {
                licenseKey: args?.licenseKey,
                requestId: args.requestId,
                licenseType: args.licenseType,
              },
            });

            if (
              args.status === LicenseSubmissionStatus.APPROVED &&
              args.licenseKey &&
              args.expiredAt &&
              args.licenseStatus === LicenseStatus.REGISTERED
            ) {
              const existingConfig = await this.prisma.configuration.findUnique(
                {
                  where: {
                    propertyId: property.id,
                  },
                },
              );

              const currentOptions =
                typeof existingConfig?.options === 'object' &&
                existingConfig?.options !== null
                  ? (existingConfig.options as Record<string, any>)
                  : {};

              await this.prisma.configuration.update({
                where: {
                  propertyId: property.id,
                },
                data: {
                  options: {
                    ...currentOptions,
                    licenseNumber: args?.licenseKey,
                  },
                },
              });

              const meta = new Metadata();

              await lastValueFrom(
                this.packageService.client.setPackageLicenseTemporaryExpiration(
                  {
                    expiredAt: new Date(
                      new Date(args.expiredAt).setFullYear(
                        new Date(args.expiredAt).getFullYear() + 1,
                      ),
                    ).getTime(),
                    propertyId: prop.id,
                    licenseType: args.licenseType,
                  },
                  meta,
                ),
              );
            }
          }

          dlpIntegration.sendEmail(
            property.users[0].user.email,
            args.status != LicenseSubmissionStatus.REJECTED
              ? `Your license has been ${args.status}`
              : `Your license has been rejected, reason : ${args.reason}`,
          );
        } catch (err) {
          console.log(err.message);
          throw new InternalServerErrorException('internal server error');
        }
        break;
    }

    this.webGateway.server.to(property.users[0].user.id).emit('notification', {
      type: 'license',
      data: {
        licenseKey: args.licenseKey,
        requestId: args.requestId,
        title: `Public Performing License`,
        message:
          args.status != LicenseSubmissionStatus.REJECTED
            ? `Your license has been ${args.status}`
            : `Your license has been rejected, reason : ${args.reason}`,
      },
    });
    return {
      status: 'success',
      message: 'success send license submission',
    };
  }
}
