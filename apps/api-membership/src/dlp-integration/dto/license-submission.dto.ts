import {
  LicenseStatus,
  LicenseSubmissionStatus,
  LicenseType,
} from '@app/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export class LicenseSubmissionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  requestId: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  licenseKey: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  expiredAt: string;

  @ApiProperty({ enum: LicenseSubmissionStatus })
  @IsEnum(LicenseSubmissionStatus)
  @IsNotEmpty()
  status: LicenseSubmissionStatus;

  @ApiPropertyOptional({ enum: LicenseStatus })
  @IsEnum(LicenseStatus)
  @IsOptional()
  licenseStatus?: LicenseStatus;

  @ApiProperty({ enum: LicenseType })
  @IsEnum(LicenseType)
  @IsNotEmpty()
  licenseType: LicenseType;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  reason: string;
}
