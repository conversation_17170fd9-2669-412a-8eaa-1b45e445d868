import { Module } from '@nestjs/common';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { CqrsModule } from '@nestjs/cqrs';
import { DlpIntegrationController } from './dlp-integration.controller';
import { DlpIntegrationEventHandlers } from './events';
import { LicenseSubmissionCommandHandlers } from './commands';
import { EventModule } from '../event/event.module';

@Module({
  imports: [
    CqrsModule,
    InternalClientModule,
    EventModule
  ],
  controllers: [DlpIntegrationController],
  providers: [
    ...DlpIntegrationEventHandlers,
    ...LicenseSubmissionCommandHandlers
  ]
})
export class DlpIntegrationModule {}
