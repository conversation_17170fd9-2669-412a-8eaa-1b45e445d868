import { <PERSON>du<PERSON> } from "@nestjs/common";
import { CqrsModule } from "@nestjs/cqrs";
import { ValidateController } from "./validate.controller";
import { ValidationQueryHandlers } from "./commands";
import { PlanModule } from "../plan/plan.module";
import { InternalClientModule } from "../internal-client/internal-client.module";
import { IntegrationModule } from "../integration/integration.module";
import { LicenseModule } from "../license/license.module";
import { AddonModule } from "../add-on/add-on.module";

@Module({
  imports: [
    CqrsModule,
    AddonModule,
    PlanModule,
    InternalClientModule,
    IntegrationModule,
    LicenseModule,
  ],
  controllers: [ValidateController],
  providers: [
    ...ValidationQueryHandlers,
  ]
})

export class ValidateModule { }
