import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { BadRequestException } from '@nestjs/common';
import { ValidateRegistrationQuery } from '../impl';
import { dayjs } from '@app/common';

@CommandHandler(ValidateRegistrationQuery)
export class ValidateRegisterHandler
  implements ICommandHandler<ValidateRegistrationQuery>
{
  constructor() {}

  async execute(command: ValidateRegistrationQuery): Promise<any> {
    const { args } = command;

    if (args.password !== args.passwordConfirmation) {
      throw new BadRequestException("password doesn't match!");
    }

    const dob = dayjs(args.dateOfBirth, 'YYYY-MM-DD', true);
    if (!dob.isValid()) {
      throw new BadRequestException('Invalid date format. Use YYYY-MM-DD');
    }

    const minAgeDate = dayjs().subtract(17, 'year');
    const maxAgeDate = dayjs().subtract(150, 'year');

    if (dob.isAfter(minAgeDate)) {
      throw new BadRequestException('You must be at least 17 years old');
    }

    if (dob.isBefore(maxAgeDate)) {
      throw new BadRequestException(
        'Date of birth cannot be more than 150 years ago',
      );
    }

    return;
  }
}
