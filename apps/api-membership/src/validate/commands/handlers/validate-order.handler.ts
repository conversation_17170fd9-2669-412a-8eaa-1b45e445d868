import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { status } from '@grpc/grpc-js';
import { IntegrationDlpService } from 'apps/api-membership/src/integration/services';
import { PlanGrpcService } from 'apps/api-membership/src/internal-client/plan.service';
import { LicenseGrpcService } from 'apps/api-membership/src/internal-client/license.service';
import { AddonGrpcService } from '../../../add-on/service/add-on.grpc.service';
import { ValidateOrderCommand } from '../impl';
import { OrderType } from '../../dto/validate-order.dto';

@CommandHandler(ValidateOrderCommand)
export class ValidateOrderHandler
  implements ICommandHandler<ValidateOrderCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly planGrpcService: PlanGrpcService,
    private readonly addOnGrpcService: AddonGrpcService,
    private readonly licenseGrpcService: LicenseGrpcService,
    private readonly integrationService: IntegrationDlpService,
  ) {}

  async execute(command: ValidateOrderCommand) {
    const { user, args } = command;

    const totalPrice: number = 0;
    const payload = {};
    let withLicense = false;
    const orderItems = [];
    const now = new Date();

    const onlyLicense = args.items.every(
      (item) => item.type === OrderType.LICENSE,
    );
    // if (onlyLicense) {
    //   throw new BadRequestException(
    //     'Order cannot be created if it only contains LICENSE',
    //   );
    // }

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        AND: [
          {
            user: {
              id: user.id,
            },
          },
          {
            property: {
              id: args.propertyId,
            },
          },
        ],
      },
      include: {
        property: {
          include: {
            configuration: true,
          },
        },
      },
    });
    if (!userProperty && userProperty?.property) {
      throw new BadRequestException('order cannot be processed');
    }

    if (!userProperty.property.id) {
      throw new BadRequestException('property not registered');
    }

    const hasPlan = args.items.some((item) => item.type === OrderType.PLAN);
    const hasLicense = args.items.some(
      (item) => item.type === OrderType.LICENSE,
    );
    const licenseType =
      userProperty?.property.configuration.options['licenseType'];
    
    //if (licenseType === 'CONVENTIONAL') {
    //  if (!hasPlan) {
    //    throw new BadRequestException(
    //      'Order must include at least one PLAN when licenseType is CONVENTIONAL',
    //    );
    //  }
    //} else if (licenseType === 'DLM') {
    //  if (!hasLicense || !hasPlan) {
    //    throw new BadRequestException(
    //      'Order must include at least one LICENSE and one PLAN when licenseType is DLM',
    //    );
    //  }
    //}

    const existingOrder = await this.prisma.order.findFirst({
      where: {
        AND: [
          {
            property: {
              id: userProperty.property.id,
            },
          },
          {
            status: 'pending',
          },
          {
            details: {
              some: {
                AND: [
                  {
                    itemId: {
                      in: args.items.map((itm) => itm.id),
                    },
                  },
                ],
              },
            },
          },
        ],
      },
    });

    if (existingOrder) {
      throw new BadRequestException(
        'Cannot create a new order, please paid your order first',
      );
    }

    if (licenseType === 'DLM') {
      if (userProperty?.property.configuration.options['customfields']['id']) {
        try {
          const category = await this.integrationService.DetailCategoryById(
            userProperty?.property.configuration.options['customfields']['id'],
          );
          if (
            category[
              `${userProperty?.property.configuration.options['customfields']['type']}`
            ].length === 0
          ) {
            throw new BadRequestException('invalid custom fields');
          }
          withLicense = true;
        } catch (err) {
          throw new InternalServerErrorException('Internal Server Error');
        }
      }
    }

    for (const item of args.items) {
      switch (item.type) {
        case OrderType.PLAN:
          try {
            await this.planGrpcService.detailPlan({ id: item.id }).toPromise();
          } catch (err) {
            if (err.code === status.NOT_FOUND) {
              throw new NotFoundException('plan not found');
            } else {
              console.error('failed order', err.message);
              throw new InternalServerErrorException('failed order');
            }
          }
          break;
        // case OrderType.LICENSE:
        //   if (withLicense) {
        //     try {
        //       if (
        //         userProperty?.property.configuration.options['customfields'][
        //           'type'
        //         ] === 'ranges' ||
        //         userProperty?.property.configuration.options['customfields'][
        //           'type'
        //         ] === 'lupsum' ||
        //         userProperty?.property.configuration.options['customfields'][
        //           'type'
        //         ] === 'split' ||
        //         userProperty?.property.configuration.options['customfields'][
        //           'type'
        //         ] === 'progressives'
        //       ) {
        //         payload = {
        //           id: userProperty?.property.configuration.options[
        //             'customfields'
        //           ]['id'],
        //           amount: Number(
        //             userProperty?.property.configuration.options[
        //               'customfields'
        //             ]['value'],
        //           ),
        //         };
        //       } else {
        //         throw new BadRequestException('unrecognized custom fields');
        //       }
        //
        //       const id =
        //         userProperty?.property.configuration.options['categoryId'];
        //
        //       await this.licenseGrpcService.getLicense({ id }).toPromise();
        //     } catch (err) {
        //       if (err.code === status.NOT_FOUND) {
        //         throw new NotFoundException('license not found');
        //       } else {
        //         throw new InternalServerErrorException('failed order');
        //       }
        //     }
        //   }
        //   break;
        case OrderType.ADDON:
          try {
            await this.addOnGrpcService
              .detailAddon({ id: item.id })
              .toPromise();
          } catch (err) {
            if (err.code === status.NOT_FOUND) {
              throw new NotFoundException('plan not found');
            } else {
              throw new InternalServerErrorException('failed order');
            }
          }
          break;
        default:
          break;
      }
    }
    return;
  }
}
