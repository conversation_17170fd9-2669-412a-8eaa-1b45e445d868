import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { ValidateMobileNumberCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(ValidateMobileNumberCommand)
export class ValidateMobileNumberHandler
  implements ICommandHandler<ValidateMobileNumberCommand>
{
  constructor(public readonly prisma: PrismaService) {}

  async execute(command: ValidateMobileNumberCommand) {
    const { user, args } = command;

    return 'success';
  }
}
