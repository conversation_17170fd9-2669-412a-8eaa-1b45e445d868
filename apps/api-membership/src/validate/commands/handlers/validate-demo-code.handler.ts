import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { ValidateDemoCodeCommand } from '../impl';
import { DemoCodeService } from '../../../internal-client/services';
import { Metadata, status } from '@grpc/grpc-js';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';

@CommandHandler(ValidateDemoCodeCommand)
export class ValidateDemoCodeHandler
  implements ICommandHandler<ValidateDemoCodeCommand>
{
  constructor(private readonly demoCodeService: DemoCodeService) {}

  async execute(command: ValidateDemoCodeCommand) {
    const { args } = command;

    console.log(args);
    try {
      const meta = new Metadata();
      const response = await this.demoCodeService.client
        .validateDemoCode(
          {
            code: args.code,
            propertyId: args.propertyId,
          },
          meta,
        )
        .toPromise();

      return { code: [response.message] };
    } catch (err) {
      let userFriendlyMessage: string;

      if (err.code === status.NOT_FOUND) {
        userFriendlyMessage = 'Demo code not found';
        throw new NotFoundException({ code: [userFriendlyMessage] });
      } else if (err.code === status.INVALID_ARGUMENT) {
        userFriendlyMessage = 'Invalid demo code provided';
        throw new BadRequestException({ code: [userFriendlyMessage] });
      } else {
        userFriendlyMessage = 'An unexpected error occurred';
        throw new InternalServerErrorException({ code: [userFriendlyMessage] });
      }
    }
  }
}
