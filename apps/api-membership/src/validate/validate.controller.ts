import {
  Controller,
  Post,
  HttpCode,
  HttpStatus,
  Body,
  UseGuards,
  HttpException,
  BadRequestException,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ValidateDemoCodeCommand,
  ValidateLicenseCommand,
  ValidateMobileNumberCommand,
  ValidateOrderCommand,
  ValidateRegistrationQuery,
} from './commands';
import { ValidateRegisterDto } from './dto/validate-registration.dto';
import { Throttle } from '@nestjs/throttler';
import { ValidateOrderDto } from './dto/validate-order.dto';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { ValidateMobileNumberDto } from './dto/validate-mobile-number.dto';
import { ValidateLicenseConvDto } from './dto/validate-license-conv.dto';
import { IntegrationDlpService } from '../integration/services';
import { ValidateDemoCodeDto } from './dto/validate-demo-code.dto';
import { ApiBearerAuth } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ValidateTmpLicenseDto } from './dto/validate-temp-license.dto';

@Controller('validate')
export class ValidateController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly integrationDlpService: IntegrationDlpService,
  ) {}

  @Throttle({ default: { limit: 3, ttl: 60000 } })
  @Post('registration')
  @HttpCode(HttpStatus.NO_CONTENT)
  async validateRegistration(@Body() args: ValidateRegisterDto): Promise<void> {
    await this.commandBus.execute(new ValidateRegistrationQuery(args));
  }

  @Throttle({ default: { limit: 3, ttl: 60000 } })
  @Post('order')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  async validateOrder(
    @Body() args: ValidateOrderDto,
    @User() user: ICurrentUser,
  ): Promise<void> {
    await this.commandBus.execute(new ValidateOrderCommand(user, args));
  }

  @Post('mobile-number')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  async validateMobileNumber(
    @Body() args: ValidateMobileNumberDto,
    @User() user: ICurrentUser,
  ) {
    await this.commandBus.execute(new ValidateMobileNumberCommand(user, args));
  }

  @Post('license-conventional')
  @HttpCode(HttpStatus.NO_CONTENT)
  async validateLicense(@Body() args: ValidateLicenseConvDto) {
    console.log('sini');
    const response = await this.integrationDlpService.validateLicense(args.key);

    if (!response) {
      throw new BadRequestException({ key: ['key not found'] });
    }

    if (response.status === 'expired') {
      throw new BadRequestException({ key: ['License is expired'] });
    }

    return;
  }

  @Post('temp-license')
  @HttpCode(HttpStatus.NO_CONTENT)
  async validateTmpLicense(@Body() args: ValidateTmpLicenseDto) {
    return await this.integrationDlpService.validateTmpLicense(args.licenseKey);
  }

  @Post('check-license')
  @HttpCode(HttpStatus.NO_CONTENT)
  async checkLicense(@Body() args: ValidateTmpLicenseDto) {
    return await this.integrationDlpService.validateTmpLicense(args.licenseKey);
  }

  @Post('demo-code')
  @HttpCode(HttpStatus.NO_CONTENT)
  async validateDemoCode(@Body() args: ValidateDemoCodeDto) {
    return await this.commandBus.execute(new ValidateDemoCodeCommand(args));
  }
}
