import { Modu<PERSON> } from "@nestjs/common";
import { ClientsModule } from "@nestjs/microservices";
import { PropertyTypeController } from "./property-type.controller";
import { PropertyTypeGrpcService } from "./service/property-type.grpc.service";
import { InternalClient } from "libs/clients/internal.client";
import { ConfigService } from "@nestjs/config";
import { INTERNAL_PACKAGE } from "libs/clients";

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => InternalClient(configService),
        name: INTERNAL_PACKAGE
      }
    ])
  ],
  controllers: [PropertyTypeController],
  providers: [PropertyTypeGrpcService],
  exports: [PropertyTypeGrpcService]
})

export class PropertyTypeModule { }