import { Id, Query } from '@app/proto-schema/index.common';
import {
  GetPropertySubLicenseRequest,
  GetPropertySubRequest,
  GetPropertySubResponse,
  GetPropertyTypeInRequest,
  GetPropertyTypeInResponse,
  ListPropertyTypesResponse,
  PROPERTY_TYPE_SERVICE_NAME,
  PropertyType,
  PropertyTypeServiceClient,
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';

@Injectable()
export class PropertyTypeGrpcService {
  private propertyTypeService: PropertyTypeServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.propertyTypeService =
      this.client.getService<PropertyTypeServiceClient>(
        PROPERTY_TYPE_SERVICE_NAME,
      );
  }

  listPropertyType(request: Query): Observable<ListPropertyTypesResponse> {
    const meta = new Metadata();
    return this.propertyTypeService.listPropertyTypes(request, meta);
  }

  getOnePropertyType(request: Id): Observable<PropertyType> {
    const meta = new Metadata();
    return this.propertyTypeService.getPropertyType(request, meta);
  }

  getPropertySub(request: GetPropertySubRequest): Observable<GetPropertySubResponse> {
    const meta = new Metadata();
    return this.propertyTypeService.getPropertySub(request, meta);
  }

  getPropertySubLicense(
    request: GetPropertySubLicenseRequest,
  ): Observable<GetPropertySubResponse> {
    const meta = new Metadata();
    return this.propertyTypeService.getPropertySubLicense(request, meta);
  }

  getPropertyTypeIn(
    request: GetPropertyTypeInRequest,
  ): Observable<GetPropertyTypeInResponse> {
    const meta = new Metadata();
    return this.propertyTypeService.getPropertyTypeIn(request, meta);
  }
}
