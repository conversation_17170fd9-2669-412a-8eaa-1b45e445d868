import {
  Controller,
  Get,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiB<PERSON>erAuth, ApiParam, ApiTags } from '@nestjs/swagger';
import { PropertyTypeGrpcService } from './service/property-type.grpc.service';
import { Query as CommonQuery, Id } from '@app/proto-schema/index.common';
import {
  GetPropertySubLicenseRequest,
  GetPropertySubRequest,
  GetPropertySubResponse,
  ListPropertyTypesResponse,
  PropertyType,
} from '@app/proto-schema/index.internal';
import { status } from '@grpc/grpc-js';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { PrismaService } from 'nestjs-prisma';
import { JwtGuard } from '../auth/guards/jwt.guard';

@ApiTags('Property Type')
@Controller('property-type')
export class PropertyTypeController {
  constructor(
    private propertyTypeGrpcService: PropertyTypeGrpcService,
    private prisma: PrismaService,
  ) {}

  @Get()
  async listPropertyType(
    @Query() query: CommonQuery,
  ): Promise<ListPropertyTypesResponse> {
    return await this.propertyTypeGrpcService
      .listPropertyType(query)
      .toPromise();
  }

  @Get('sub-license/:id/:code')
  @ApiParam({ name: 'id', description: 'parentId' })
  @ApiParam({ name: 'code', description: 'Code category' })
  async getOneSubLicense(
    @Param('id') id: string,
    @Param('code') code: string,
  ): Promise<GetPropertySubResponse> {
    const request: GetPropertySubLicenseRequest = { id, code };
    return await this.propertyTypeGrpcService
      .getPropertySubLicense(request)
      .toPromise();
  }

  @Get(':id')
  @ApiParam({ name: 'id', description: 'Property type ID' })
  async getOnePropertyType(@Param('id') id: string): Promise<PropertyType> {
    const requestId: Id = { id };
    try {
      return await this.propertyTypeGrpcService
        .getOnePropertyType(requestId)
        .toPromise();
    } catch (err) {
      if ((err.code = status.NOT_FOUND)) {
        throw new NotFoundException();
      }
    }
  }

  @Get(':id/childs')
  @ApiParam({ name: 'id', description: 'parentId' })
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  async getOnePropertySub(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ): Promise<GetPropertySubResponse> {
    try {
      // Get user's business type from database
      const currentUser = await this.prisma.user.findUnique({
        where: { id: user.id },
        select: { businessType: true },
      });

      const request: GetPropertySubRequest = {
        id,
        businessType: currentUser?.businessType,
      };

      return await this.propertyTypeGrpcService
        .getPropertySub(request)
        .toPromise();
    } catch (err) {
      if ((err.code = status.NOT_FOUND)) {
        throw new NotFoundException();
      } else throw new InternalServerErrorException();
    }
  }
}
