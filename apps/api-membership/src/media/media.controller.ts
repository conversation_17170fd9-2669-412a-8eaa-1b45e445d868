import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { DeleteMediaCommand, UploadMediaCommand } from './commands';
import { GetFileQuery, GetMediaQuery, GetMediasQuery } from './queries';
import { from, map } from 'rxjs';
import { UploadMediaDto } from './dto/upload.media.dto';
import { FilterMediaDto } from './dto/filter-media.dto';

@Controller('media')
@ApiTags('Media')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class MediaController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  upload(
    @Body() uploadMediaDto: UploadMediaDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 100 }),
          new FileTypeValidator({ fileType: '.(png|jpeg|jpg|gif)' }),
        ],
      }),
    )
    file: Express.Multer.File,
  ) {
    const fileStream$ = from([file.buffer]).pipe(
      map((buffer) => ({ chunk: buffer })),
    );

    return this.commandBus.execute(
      new UploadMediaCommand(fileStream$, {
        fileName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
        encoding: file.encoding,
      }),
    );
  }

  @Post('npwp')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  uploadNPWP(
    @Body() uploadMediaDto: UploadMediaDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 500 }),
          new FileTypeValidator({ fileType: '.(png|jpg|pdf)' }),
        ],
      }),
    )
    file: Express.Multer.File,
  ) {
    const fileStream$ = from([file.buffer]).pipe(
      map((buffer) => ({ chunk: buffer })),
    );
    return this.commandBus.execute(
      new UploadMediaCommand(fileStream$, {
        fileName: file.originalname,
        mimeType: file.mimetype,
        size: file.size,
        encoding: file.encoding,
      }),
    );
  }

  @Get()
  findAll(@Query() filter: FilterMediaDto) {
    return this.queryBus.execute(new GetMediasQuery(filter));
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.queryBus.execute(new GetMediaQuery(id));
  }

  @Get(':id/file')
  getFile(@Param('id') id: string) {
    return this.queryBus.execute(new GetFileQuery(id));
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.commandBus.execute(new DeleteMediaCommand(id));
  }
}
