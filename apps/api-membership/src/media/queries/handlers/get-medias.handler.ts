import { Pagination } from '@app/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { Media, Prisma } from '@prisma/client';
import { GetMediasQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetMediasQuery)
export class GetMediasHandler implements IQueryHandler<GetMediasQuery> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly config: ConfigService,
  ) {}

  async execute(query: GetMediasQuery) {
    const { args } = query;
    const search = args?.search || '';
    const items = await Pagination<Media, Prisma.MediaFindManyArgs>(
      this.prisma.media,
      { where: { name: { contains: search, mode: 'insensitive' } } },
      { page: args.page, limit: args.limit },
    );

    const data = items.data.map((item) => {
      const uri = `${this.config.get<string>('APP_ADMIN_SERVER')}/${item.fileName}`;
      return Object.assign(item, { url: uri });
    });
    return Object.assign(items, { data: data });
  }
}
