import { NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { I<PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { GetMediaQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetMediaQuery)
export class GetMediaHandler implements IQueryHandler<GetMediaQuery> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly config: ConfigService,
  ) {}

  async execute(query: GetMediaQuery) {
    const { id } = query;
    const item = await this.prisma.media.findFirst({ where: { id: id } });
    if (!item) {
      throw new NotFoundException();
    }

    const reMap = {
      id: item.id,
      name: item.name,
      caption: item.caption,
      url: `${this.config.get<string>('APP_ADMIN_SERVER')}/${item.fileName}`,
    };
    return reMap;
  }
}
