import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { join } from 'path';
import { cwd } from 'process';
import { GetFileQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetFileQuery)
export class GetFileHandler implements IQueryHandler<GetFileQuery> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetFileQuery) {
    const { id } = query;
    const item = await this.prisma.media.findFirst({ where: { id: id } });
    if (!item) {
      throw new NotFoundException();
    }
    const reMap = {
      id: item.id,
      name: item.name,
      caption: item.caption,
      url: join(cwd(), item.fileName),
    };
    return reMap;
  }
}
