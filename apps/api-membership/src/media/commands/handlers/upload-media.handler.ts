import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UploadMediaCommand } from '../impl';
import { Readable } from 'stream';
import { ConfigService } from '@nestjs/config';
import { Client } from 'minio';
import * as dayjs from 'dayjs';
import { extname } from 'path';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(UploadMediaCommand)
export class UploadMediaHandler implements ICommandHandler<UploadMediaCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(command: UploadMediaCommand) {
    const { args, metaMedia } = command;
    let fileName = undefined;
    if (args) {
      const uniqueSuffix = `${dayjs().unix()}${Math.round(
        Math.random() * 1e9,
      )}`;
      fileName = `${uniqueSuffix}${extname(metaMedia['fileName'])}`;
    }

    const chunks: Buffer[] = [];

    let fileLocation = '';
    if (this.configService.get('STORAGE_PROVIDER') === 'minio') {
      let useHttps = '';
      if (this.configService.get('MINIO_USE_SSL') === 'true') {
        useHttps = 'https';
      } else {
        useHttps = 'http';
      }

      fileLocation = `${this.configService.get(`${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`)}/${this.configService.get<string>('FOLDER_NAME')}`;
    }

    return new Promise((resolve, reject) => {
      args.subscribe({
        next: (chunk) => {
          chunks.push(Buffer.from(chunk.chunk));
        },
        complete: async () => {
          const buffer = Buffer.concat(chunks);
          const readableStream = new Readable({
            read() {
              this.push(buffer);
              this.push(null);
            },
          });

          try {
            const minioClient = new Client({
              endPoint: this.configService.get('MINIO_ENDPOINT'),
              port: parseInt(
                this.configService.get('MINIO_PORT') || '9000',
                10,
              ),
              useSSL: this.configService.get('MINIO_USE_SSL') === 'true',
              accessKey: this.configService.get('MINIO_ACCESS_KEY'),
              secretKey: this.configService.get('MINIO_SECRET_KEY'),
            });

            await minioClient.putObject(
              this.configService.get(
                `${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`,
              ),
              `${this.configService.get<string>('FOLDER_NAME')}/${fileName}`,
              readableStream,
              readableStream.readableLength,
              { 'Content-Type': metaMedia['mimetype'] }
            );

            const media = await this.prisma.media.create({
              data: {
                fileName: fileName,
                caption: metaMedia['fileName'],
                path: fileLocation,
                encoding: metaMedia['encoding'],
                mimeType: metaMedia['mimeType'],
                name: metaMedia['fileName'],
                size: metaMedia['size'],
              },
            });

            resolve({
              id: media.id,
              url: media
                ? `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${media.path}/${media.fileName}`
                : null,
            });
          } catch (err) {
            reject(err);
          }
        },
        error: (err) => {
          reject(err);
        },
      });
    });
  }
}
