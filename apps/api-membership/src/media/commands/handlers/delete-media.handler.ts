import { NotFoundException } from '@nestjs/common';
import { Command<PERSON>andler, ICommandHandler } from '@nestjs/cqrs';
import { existsSync, unlinkSync } from 'fs';
import { join } from 'path';
import { cwd } from 'process';
import { DeleteMediaCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(DeleteMediaCommand)
export class DeleteMediaHandler implements ICommandHandler<DeleteMediaCommand> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeleteMediaCommand) {
    const { id } = command;
    const item = await this.prisma.media.findFirst({ where: { id: id } });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      await this.prisma.media.delete({ where: { id: item.id } });
      const rootPath = join(cwd(), 'storage', item.name);
      if (existsSync(rootPath)) {
        unlinkSync(rootPath);
      }

      return 'successfully deleted media';
    } catch (error) {
      return 'failed deleted media';
    }
  }
}
