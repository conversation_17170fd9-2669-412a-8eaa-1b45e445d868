import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { ReferralController } from './referral.controller';
import { ReferralCommandHandlers } from './commands';

@Module({
  imports: [CqrsModule, InternalClientModule],
  controllers: [ReferralController],
  providers: [...ReferralCommandHandlers],
})
export class ReferralModule {}
