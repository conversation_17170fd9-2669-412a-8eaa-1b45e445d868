import { Body, Controller, Post } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { ReferralService } from '../internal-client/services';
import { ApiOperation } from '@nestjs/swagger';
import { UseReferralDto } from './dto/use-referral.dto';
import { UseReferralCommand } from './commands';

@Controller('referral')
export class ReferralController {
  constructor(
    private commandBus: CommandBus,
    private referralService: ReferralService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Use Referral Code' })
  async useReferral(@Body() args: UseReferralDto) {
    return await this.commandBus.execute(new UseReferralCommand(args));
  }
}
