import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UseReferralCommand } from '../impl';
import { ReferralService } from '../../../internal-client/services';
import { PrismaService } from 'nestjs-prisma';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Metadata, status } from '@grpc/grpc-js';

@CommandHandler(UseReferralCommand)
export class UseReferralHandler implements ICommandHandler<UseReferralCommand> {
  constructor(
    private referralService: ReferralService,
    private prisma: PrismaService,
  ) {}

  async execute(command: UseReferralCommand) {
    const { args } = command;

    const order = await this.prisma.order.findFirst({
      where: {
        id: args.orderId,
      },
      include: {
        details: true,
      },
    });

    if (order.status != 'pending') {
      throw new BadRequestException(`can't use referral`);
    }

    try {
      const meta = new Metadata();
      const refResult = await this.referralService.client
        .useReferral(
          {
            code: args.code,
            orderId: args.orderId,
          },
          meta,
        )
        .toPromise();

      await this.prisma.$transaction(async (tr) => {
        await tr.orderDetail.update({
          where: {
            id: refResult.itemId,
          },
          data: {
            referralId: refResult.itemId,
            discount: refResult.discountPrice,
            totalPrice: refResult.totalPriceAfter,
          },
        });
        await tr.order.update({
          where: { id: args.orderId },
          data: {
            referralCode: args.code,
            discount: refResult.discountPrice,
            totalPrice: Number(order.totalPrice) - refResult.discountPrice,
          },
        });
      });
      return {
        status: 'Success',
        message: 'Success redeem referral code',
        data: refResult,
      };
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException(err.message);
      } else if (err.code === status.INVALID_ARGUMENT) {
        throw new BadRequestException(err.message);
      } else {
        throw new InternalServerErrorException(err.message);
      }
    }
  }
}
