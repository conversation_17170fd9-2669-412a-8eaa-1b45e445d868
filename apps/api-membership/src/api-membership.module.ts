import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PrismaModule } from 'nestjs-prisma';
import { ValidatorModule } from './validator/validator.module';
import { AuthModule } from './auth/auth.module';
import { PlanModule } from './plan/plan.module';
import { PropertyTypeModule } from './property-type/property-type.module';
import { PostalModule } from './postal/postal.module';
import { PropertyModule } from './property/property.module';
import { OrderModule } from './order/order.module';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { join } from 'path';
import { MediaModule } from './media/media.module';
import { OrderPaymentModule } from './order-payment/order-payment.module';
import { EventModule } from './event/event.module';
import { InternalClientModule } from './internal-client/internal-client.module';
import { VoucherModule } from './voucher/voucher.module';
import { DeviceModule } from './device/device.module';
import { AddonModule } from './add-on/add-on.module';
import { AvatarModule } from './avatar/avatar.module';
import { NotificationModule } from './notification/notification.module';
import { RoleModule } from './role/role.module';
import { UserModule } from './user/user.module';
import { IntegrationModule } from './integration/integration.module';
import { LicenseModule } from './license/license.module';
import { RedisModule, RedisModuleOptions } from '@nestjs-modules/ioredis';
import { PackageModule } from './package/package.module';
import { ScheduleModule } from '@nestjs/schedule';
import { PaymentHistoryModule } from './payment-history/payment-history.module';
import { HealthModule } from './health/health.module';
import { ReferralModule } from './referral/referral.module';
import { ValidateModule } from './validate/validate.module';
import { JobPositionModule } from './job-position/job-position.module';
import { PartnershipModule } from './partnership/partnership.module';
import { DemoCodeModule } from './demo-code/demo-code.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { AppModule } from './app/app.module';
import { ListModule } from './list/list.module';
import { DlpIntegrationModule } from './dlp-integration/dlp-integration.module';
import { MonitorModule } from './monitor/monitor.module';
import { QueueModule } from './queue/queue.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    MailerModule.forRootAsync({
      useFactory: async (config: ConfigService) => ({
        transport: {
          host: config.get<string>('MAIL_HOST'),
          port: config.get<string>('MAIL_PORT'), 
          secure: config.get<string>('MAIL_SECURE'),
          auth: {
            user: config.get<string>('MAIL_USER'),
            pass: config.get<string>('MAIL_PASSWORD'),
          },
        },
        defaults: {
          from: `"Velodiva" <${config.get<string>('MAIL_FROM')}>`,
        },
        template: {
          dir: join(__dirname, './mail/templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
      inject: [ConfigService],
    }),
    ConfigModule.forRoot({ isGlobal: true }),
    PrismaModule.forRoot({
      isGlobal: true,
      prismaServiceOptions: {
        prismaOptions: {
          log: [
            {
              emit: 'event',
              level: 'query',
            },
          ],
        },
      },
    }),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (
        config: ConfigService,
      ): Promise<RedisModuleOptions> => ({
        type: 'single',
        url: config.get<string>('REDIS'),
      }),
      inject: [ConfigService],
    }),
    // UserModule,
    AuthModule,
    AppModule,
    OrderModule,
    PlanModule,
    // BundleModule,
    PostalModule,
    PropertyModule,
    PropertyTypeModule,
    PartnershipModule,
    ValidatorModule,
    MediaModule,
    OrderPaymentModule,
    EventModule,
    InternalClientModule,
    VoucherModule,
    ReferralModule,
    DemoCodeModule,
    DeviceModule,
    AddonModule,
    AvatarModule,
    NotificationModule,
    RoleModule,
    UserModule,
    IntegrationModule,
    LicenseModule,
    PackageModule,
    PaymentHistoryModule,
    HealthModule,
    ValidateModule,
    JobPositionModule,
    DashboardModule,
    ListModule,
    DlpIntegrationModule,
    MonitorModule,
    QueueModule,
  ],
})
export class ApiMembershipModule {}
