import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { join } from "path";
import { cwd } from "process";
import { PostalController } from "./postal.controller";
import { PostalGrpcService } from "./service/postal.grpc.service";
import { InternalClient } from "libs/clients/internal.client";
import { INTERNAL_PACKAGE } from "libs/clients";

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => InternalClient(configService),
        name: INTERNAL_PACKAGE
      }
    ])
  ],
  controllers: [PostalController],
  providers: [PostalGrpcService],
  exports: [PostalGrpcService]
})

export class PostalModule { }