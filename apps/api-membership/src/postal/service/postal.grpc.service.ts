import { Id } from '@app/proto-schema/index.common';
import {
  CityRequest,
  DistrictRequest,
  GetPostalByIdsRequest,
  GetPostalByIdsResponse,
  ListCityResponse,
  ListDistrictResponse,
  ListProvinceResponse,
  ListUrbanResponse,
  Postal,
  POSTAL_SERVICE_NAME,
  PostalServiceClient,
  ProvinceRequest,
  UrbanRequest,
  ZipCodeRequest,
  ZipCodeResponse,
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';

@Injectable()
export class PostalGrpcService {
  private postalService: PostalServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.postalService =
      this.client.getService<PostalServiceClient>(POSTAL_SERVICE_NAME);
  }

  getProvince(
    request: ProvinceRequest,
    meta: Metadata,
  ): Observable<ListProvinceResponse> {
    return this.postalService.getProvince(request, meta);
  }

  getCity(request: CityRequest, meta: Metadata): Observable<ListCityResponse> {
    return this.postalService.getCity(request, meta);
  }

  getDistrict(
    request: DistrictRequest,
    meta: Metadata,
  ): Observable<ListDistrictResponse> {
    return this.postalService.getDistrict(request, meta);
  }

  getUrban(
    request: UrbanRequest,
    meta: Metadata,
  ): Observable<ListUrbanResponse> {
    return this.postalService.getUrban(request, meta);
  }

  getZipCode(
    request: ZipCodeRequest,
    meta: Metadata,
  ): Observable<ZipCodeResponse> {
    return this.postalService.getZipCode(request, meta);
  }

  getPostal(request: Id, meta: Metadata): Observable<Postal> {
    return this.postalService.getPostal(request, meta);
  }

  getPostalByIds(
    request: GetPostalByIdsRequest,
    meta: Metadata,
  ): Observable<GetPostalByIdsResponse> {
    return this.postalService.getPostalByIds(request, meta);
  }
}
