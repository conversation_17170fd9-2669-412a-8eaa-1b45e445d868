import { Controller, Get, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { PostalGrpcService } from "./service/postal.grpc.service";
import { FilterProvinceDto } from "./dto/filter-province.dto";
import { CityRequest, DistrictRequest, ListCityResponse, ListDistrictResponse, ListPostalResponse, ListUrbanResponse, ZipCodeResponse } from "@app/proto-schema/index.internal";
import { Metadata } from "@grpc/grpc-js";
import { FilterCityDto } from "./dto/filter-city.dto";
import { FilterDistrictDto } from "./dto/filter-district.dto";
import { FilterUrbanDto } from "./dto/filter-urban.dto";
import { FilterZipCodeDto } from "./dto/filter-zipcode.dto";

@ApiTags('Postal')
@Controller('postal')
export class PostalController {
  constructor(private postalGrpc: PostalGrpcService) { }

  @Get('province')
  async getProvince(
    @Query() filter: FilterProvinceDto
  ): Promise<ListPostalResponse> {
    const request = {
      search: {
        query: filter.search,
      },
    };
    const meta = new Metadata();
    return await this.postalGrpc.getProvince(request, meta).toPromise()
  }

  @Get('city')
  async getCity(
    @Query() filter: FilterCityDto
  ): Promise<ListCityResponse> {
    const meta = new Metadata();
    const res = await this.postalGrpc.getCity(filter, meta).toPromise();
    return {
      data: res.data
    }
  }

  @Get('district')
  async getDistrict(
    @Query() filter: FilterDistrictDto
  ): Promise<ListDistrictResponse> {
    const meta = new Metadata();
    return await this.postalGrpc.getDistrict(filter, meta).toPromise()
  }

  @Get('urban')
  async getUrban(
    @Query() filter: FilterUrbanDto
  ): Promise<ListUrbanResponse> {
    const meta = new Metadata();
    return await this.postalGrpc.getUrban(filter, meta).toPromise()
  }

  @Get('zipCode')
  async getZipCode(
    @Query() filter: FilterZipCodeDto
  ): Promise<ZipCodeResponse> {
    const meta = new Metadata();
    return await this.postalGrpc.getZipCode(filter, meta).toPromise()
  }
}