import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { HealthHandlers } from './queries';
import { HealthController } from './health.controller';
import { TerminusModule } from '@nestjs/terminus';
import Redis from 'ioredis';
import { Client } from 'minio';

@Module({
  imports: [
    CqrsModule,
    TerminusModule,
  ],
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: () => new Redis(process.env.REDIS || 'redis://localhost:6379'),
    },
    {
      provide: 'MINIO_CLIENT',
      useFactory: () => new Client({
        endPoint: process.env.MINIO_ENDPOINT || 'localhost',
        port: Number(process.env.MINIO_PORT) || 9000,
        useSSL: Boolean(process.env.MINIO_USE_SSL),
        accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
        secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
      }),
    },
    ...HealthHandlers,
  ],
  controllers: [
    HealthController
  ]
})
export class HealthModule {}
