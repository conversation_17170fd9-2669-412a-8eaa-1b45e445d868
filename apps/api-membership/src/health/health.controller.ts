import {
  Controller,
  Get,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { HealthCheckQuery } from './queries';
import { HealthPingQuery } from './queries/impl/health-ping.query';

@Controller('health')
@ApiTags('Health')
@ApiBearerAuth()
export class HealthController {
  constructor(
    private queryBus: QueryBus,
  ) {}

  @Get()
  @ApiOperation({ summary: 'health check' })
  getCurrentPlay() {
    return this.queryBus.execute(new HealthCheckQuery());
  }

  @Get('ping')
  @ApiOperation({ summary: 'helth ping' })
  getAll() {
    return this.queryBus.execute(new HealthPingQuery());
  }

}
