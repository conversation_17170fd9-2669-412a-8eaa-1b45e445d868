import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { HealthCheckService, HttpHealthIndicator, PrismaHealthIndicator } from '@nestjs/terminus';
import { Inject } from '@nestjs/common';
import Redis from 'ioredis';
import { HealthPingQuery } from '../impl/health-ping.query';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(HealthPingQuery)
export class HealthPingHandler implements IQueryHandler<HealthPingQuery> {
  constructor(
    private prisma: PrismaService,
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private db: PrismaHealthIndicator,
    @Inject('REDIS_CLIENT') private redisClient: Redis,
  ) {}

  async execute(query: HealthPingQuery) {
    const config = await this.prisma.setting.findFirst({
      where: {
        type: 'iseller'
      }
    });
    const res = await this.health.check([
      () => this.http.pingCheck('inet', 'https://www.google.com'),
      //() => this.http.pingCheck('sb', this.configService.get<string>('GMI_BASE_URL')),
      //() => this.http.pingCheck('spc', this.configService.get<string>('RMS_HOST')),
      () => this.db.pingCheck('db', this.prisma),
      async () => {
        try {
          await new Promise((resolve, reject) => {
            this.redisClient.ping((err, result) => {
              if (err || result !== 'PONG') {
                reject(new Error('Redis is down'));
              } else {
                resolve('up');
              }
            });
          });
          return { cache: { status: 'up' } };
        } catch (err) {
          return { cache: { status: 'down' } };
        }
      },
    ]);

    return res.status;
  }
}