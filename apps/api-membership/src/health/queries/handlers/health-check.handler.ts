import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { HealthCheckQuery } from '../impl';
import { HealthCheckService, HttpHealthIndicator, MemoryHealthIndicator, PrismaHealthIndicator } from '@nestjs/terminus';
import { Inject } from '@nestjs/common';
import Redis from 'ioredis';
import { Client } from 'minio';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(HealthCheckQuery)
export class HealthCheckHandler implements IQueryHandler<HealthCheckQuery> {
  constructor(
    private prisma: PrismaService,
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private db: PrismaHealthIndicator,
    private memory: MemoryHealthIndicator,
    private readonly configService: ConfigService,
    @Inject('REDIS_CLIENT') private redisClient: Redis,
    @Inject('MINIO_CLIENT') private minioClient: Client,
  ) {}

  async execute(query: HealthCheckQuery) {
    const config = await this.prisma.setting.findFirst({
      where: {
        type: 'iseller'
      }
    });
    return this.health.check([
      () => this.http.pingCheck('inet', 'https://www.google.com'),
      //() => this.http.pingCheck('sb', this.configService.get<string>('GMI_BASE_URL')),
      //() => this.http.pingCheck('spc', this.configService.get<string>('RMS_HOST')),
      //() => this.http.pingCheck('pg', config.option['paymentUrl']),
      () => this.db.pingCheck('db', this.prisma),
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024),
      async () => {
        try {
          await new Promise((resolve, reject) => {
            this.redisClient.ping((err, result) => {
              if (err || result !== 'PONG') {
                reject(new Error('Redis is down'));
              } else {
                resolve('up');
              }
            });
          });
          return { cache: { status: 'up' } };
        } catch (err) {
          return { cache: { status: 'down' } };
        }
      },
      async () => {
        try {
          await this.minioClient.listBuckets();
          return { storage: { status: 'up' } };
        } catch (err) {
          return { storage: { status: 'down' } };
        }
      },
    ]);
  }
}