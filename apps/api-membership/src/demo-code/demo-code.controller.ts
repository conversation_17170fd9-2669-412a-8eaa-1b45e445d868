import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { ApiOperation } from '@nestjs/swagger';
import { UseDemoCodeDto } from './dto/use-demo-code.dto';
import { UseDemoCodeCommand } from './commands';
import { DemoCodeService } from '../internal-client/services';
import { ListDemoCodePropertiesDto } from './dto/list-demo-code-properties.dto';
import { ListDemoCodePropertiesRequest } from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';

@Controller('demo-code')
export class DemoCodeController {
  constructor(
    private commandBus: CommandBus,
    private demoCodeService: DemoCodeService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'use DemoCode' })
  async useDemoCode(@Body() args: UseDemoCodeDto) {
    return await this.commandBus.execute(new UseDemoCodeCommand(args));
  }

  @Get('demo-code-properties')
  @ApiOperation({ summary: 'Demo Code Properties' })
  async demoCodeProperties(@Query() args: ListDemoCodePropertiesDto) {
    try {
      const meta = new Metadata();

      const request: ListDemoCodePropertiesRequest = {
        demoCodeId: args.demoCodeId,
        query: {
          query: args.search,
          params: {
            page: (args.page as unknown as string) || '1',
            limit: (args.limit as unknown as string) || '10',
          },
        },
      };

      const response = await this.demoCodeService.client
        .listDemoCodeProperties(request, meta)
        .toPromise();

      return {
        data: response.data.map(({ code, properties }) => ({
          code,
          properties: properties.map(
            ({ createdAt, updatedAt, configuration, contactPerson, ...rest }) =>
              rest,
          ),
        })),
        meta: response.meta,
      };
    } catch (err) {
      console.log(err);
    }
  }
}
