import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { DemoCodeCommandHandlers } from './commands';
import { Demo<PERSON>odeController } from './demo-code.controller';

@Module({
  imports: [CqrsModule, InternalClientModule],
  controllers: [DemoCodeController],
  providers: [...DemoCodeCommandHandlers],
})
export class DemoCodeModule {}
