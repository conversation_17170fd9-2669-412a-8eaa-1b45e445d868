import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { UseDemoCodeCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Metadata, status } from '@grpc/grpc-js';
import { RpcException } from '@nestjs/microservices';
import { DemoCodeService } from '../../../internal-client/services';
import { Status } from '@grpc/grpc-js/build/src/constants';

@CommandHandler(UseDemoCodeCommand)
export class UseDemoCodeHandler implements ICommandHandler<UseDemoCodeCommand> {
  constructor(
    private demoCodeService: DemoCodeService,
    private prisma: PrismaService,
  ) {}

  async execute(command: UseDemoCodeCommand) {
    const { args } = command;

    try {
      const meta = new Metadata();
      return await this.demoCodeService.client
        .useDemoCode(
          {
            code: args.code,
            propertyId: args.propertyId,
          },
          meta,
        )
        .toPromise();
    } catch (err) {
      console.log(err);
      if (err.code === Status.NOT_FOUND) {
        throw new NotFoundException(err.message);
      } else if (err.code === Status.INVALID_ARGUMENT) {
        throw new BadRequestException(err.message);
      } else {
        throw new InternalServerErrorException(err.message);
      }
    }
  }
}
