import { Module } from '@nestjs/common';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { DeviceController } from './device.controller';
import { CqrsModule } from '@nestjs/cqrs';
import { <PERSON>ceQueryHandler } from './queries';

@Module({
  imports: [
    CqrsModule,
    InternalClientModule
  ],
  controllers: [DeviceController],
  providers: [
    ...DeviceQueryHandler
  ]
})
export class DeviceModule {}
