import {
  BadRequestException,
  Controller,
  Get,
  InternalServerErrorException,
  Ip,
  NotFoundException,
  Param,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { FilterDeviceDto } from './dto/filter-device.dto';
import { DeviceGrpcService } from '../internal-client/device.service';
import { PrismaService } from 'nestjs-prisma';
import { status } from '@grpc/grpc-js';
import { LoggedSessionCreate } from '../auth/types';
import { QueryBus } from '@nestjs/cqrs';
import { SsoLoginByDeviceIdQuery } from './queries';
import { lastValueFrom } from 'rxjs';

@ApiTags('Device')
@Controller('device')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class DeviceController {
  constructor(
    private deviceService: DeviceGrpcService,
    private readonly prismaService: PrismaService,
    private queryBus: QueryBus,
  ) {}

  @Get()
  async findAll(@User() user: ICurrentUser, @Query() filter: FilterDeviceDto) {
    const userProperties = await this.prismaService.userProperty.findMany({
      where: { userId: user.id },
      select: { propertyId: true },
    });

    if (userProperties.length === 0) {
      return {
        data: [],
        meta: {
          total: 0,
          lastPage: 0,
          currentPage: 1,
          limit: filter.limit,
          prev: null,
          next: null,
        },
      };
    }

    const propertyIds = userProperties.map((p) => p.propertyId);
    const filterProperties = filter?.propertyIds
      ? filter.propertyIds.split(',').map(String)
      : [];

    const properties =
      filterProperties.length > 0
        ? propertyIds.filter((item) => filterProperties.includes(item))
        : propertyIds;

    // First, get all devices from gRPC without filtering
    const query = {
      limit: 1000, // Get more data to filter locally
      page: 1,
      search: filter?.search, // Keep general search at gRPC level
      sort: filter?.sort,
      sortType: filter?.sortType,
      propertyIds: properties,
    };

    const resp = await lastValueFrom(
      this.deviceService.getDevicesByPropertyIn({
        query: JSON.stringify(query),
      }),
    );

    let filteredDevices = resp.data || [];

    // Apply post-processing filters
    if (filter.businessUnit && filter.businessUnit.trim() !== '') {
      filteredDevices = filteredDevices.filter((device) =>
        device.user?.businessType?.toLowerCase().includes(filter.businessUnit.toLowerCase())
      );
    }

    if (filter.type) {
      filteredDevices = filteredDevices.filter((device) =>
        device.type?.toLowerCase() === filter.type.toLowerCase()
      );
    }

    if (filter.status) {
      const isActiveFilter = filter.status === 'active';
      filteredDevices = filteredDevices.filter((device) =>
        device.isActive === isActiveFilter
      );
    }

    // Apply search filters
    if (filter.deviceName && filter.deviceName.trim() !== '') {
      const searchTerm = filter.deviceName.toLowerCase();
      filteredDevices = filteredDevices.filter((device) =>
        device.name?.toLowerCase().includes(searchTerm)
      );
    }

    if (filter.deviceId && filter.deviceId.trim() !== '') {
      const searchTerm = filter.deviceId.toLowerCase();
      filteredDevices = filteredDevices.filter((device) =>
        device.id?.toLowerCase().includes(searchTerm)
      );
    }

    if (filter.currentlyPlaying && filter.currentlyPlaying.trim() !== '') {
      const searchTerm = filter.currentlyPlaying.toLowerCase();
      filteredDevices = filteredDevices.filter((device) =>
        device.currentPlay?.title?.toLowerCase().includes(searchTerm)
      );
    }

    if (filter.zone && filter.zone.trim() !== '') {
      const searchTerm = filter.zone.toLowerCase();
      filteredDevices = filteredDevices.filter((device) =>
        device.zone?.toLowerCase().includes(searchTerm)
      );
    }

    if (filter.user && filter.user.trim() !== '') {
      const searchTerm = filter.user.toLowerCase();
      filteredDevices = filteredDevices.filter((device) =>
        device.user?.email?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply pagination to filtered results
    const page = Number(filter?.page || 1);
    const limit = Number(filter?.limit || 50);
    const skip = (page - 1) * limit;

    const total = filteredDevices.length;
    const paginatedDevices = filteredDevices.slice(skip, skip + limit);

    return {
      data: paginatedDevices,
      meta: {
        total,
        lastPage: Math.ceil(total / limit),
        currentPage: page,
        limit,
        prev: page > 1 ? page - 1 : null,
        next: page < Math.ceil(total / limit) ? page + 1 : null,
      },
    };
  }

  // @Get('/total-device')
  // async totalDevice(
  //   @User() user: ICurrentUser,
  //   @Query() filter: TotalDeviceDto,
  // ) {
  //   try {
  //     const properties = await this.prismaService.userProperty.findMany({
  //       where: { userId: user.id },
  //       select: { propertyId: true },
  //     });
  //     if (!properties) {
  //       return { total: 0 };
  //     }

  //     const propertyIds = properties.map((p) => p.propertyId);
  //     const request: GetTotalDeviceRequest = {
  //       Ids: propertyIds,
  //       isActive: filter.isActive,
  //       isOnline: filter.isOnline,
  //     };

  //     return await this.deviceService.getTotalDevice(request).toPromise();
  //   } catch (err) {
  //     console.log(err);
  //     if (err.code == status.NOT_FOUND) {
  //       throw new NotFoundException('device not found');
  //     } else {
  //       throw new InternalServerErrorException('Internal Server Error');
  //     }
  //   }
  // }

  // Option 1: Using query parameter
  @Get('activations')
  async getActivations(@Query('propertyId') propertyId: string) {
    if (!propertyId) {
      throw new BadRequestException('Property ID is required');
    }

    try {
      const { data = [] } = await this.deviceService
        .getActivationsByProperty({
          propertyId,
        })
        .toPromise();

      return data;
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException('Activations not found');
      }
      throw new InternalServerErrorException('Failed to fetch activations');
    }
  }

  @Get(':id')
  async findOne(@User() user: ICurrentUser, @Param('id') id: string) {
    try {
      return await this.deviceService
        .getDevice({
          query: JSON.stringify({ propertyId: user.propertyId, id: id }),
        })
        .toPromise();
    } catch (err) {
      if (err.code == status.NOT_FOUND) {
        throw new NotFoundException('device not found');
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }

  @Get(':id/sso')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  loginSSO(
    @User() user: ICurrentUser,
    @Param('id') deviceId: string,
    @Ip() ip: string,
    @Req() req: Request,
  ) {
    const loggedSession: LoggedSessionCreate = {
      ip: ip,
      media: req.headers['user-agent'],
    };
    return this.queryBus.execute(
      new SsoLoginByDeviceIdQuery(user, deviceId, loggedSession),
    );
  }
}
