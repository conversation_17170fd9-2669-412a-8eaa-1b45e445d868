import { BaseFilterDto } from '@app/common';
import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsOptional, IsString, ValidateIf, IsEnum, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';

export enum DeviceStatusFilter {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum DeviceTypeFilter {
  PLAYER = 'player',
  DISPLAY = 'display',
  CONTROLLER = 'controller',
  SPEAKER = 'speaker',
}

export class FilterDeviceDto extends PartialType(BaseFilterDto) {
  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((prop) => prop.search != '')
  @IsString()
  propertyIds?: string;

  @ApiPropertyOptional({ description: 'Filter by business unit/type' })
  @IsOptional()
  @IsString()
  businessUnit?: string;

  @ApiPropertyOptional({
    enum: DeviceTypeFilter,
    description: 'Filter by device type'
  })
  @IsOptional()
  @IsEnum(DeviceTypeFilter)
  type?: DeviceTypeFilter;

  @ApiPropertyOptional({
    enum: DeviceStatusFilter,
    description: 'Filter by device status'
  })
  @IsOptional()
  @IsEnum(DeviceStatusFilter)
  status?: DeviceStatusFilter;

  @ApiPropertyOptional({ description: 'Search by device name' })
  @IsOptional()
  @IsString()
  deviceName?: string;

  @ApiPropertyOptional({ description: 'Search by device ID' })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiPropertyOptional({ description: 'Search by currently playing content' })
  @IsOptional()
  @IsString()
  currentlyPlaying?: string;

  @ApiPropertyOptional({ description: 'Search by zone name' })
  @IsOptional()
  @IsString()
  zone?: string;

  @ApiPropertyOptional({ description: 'Search by user email' })
  @IsOptional()
  @IsString()
  user?: string;
}
