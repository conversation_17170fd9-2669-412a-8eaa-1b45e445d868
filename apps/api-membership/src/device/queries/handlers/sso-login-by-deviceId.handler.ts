import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import {
  InternalServerErrorException,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import { AuthGrpcService } from 'apps/api-membership/src/internal-client/auth.service';
import { status } from '@grpc/grpc-js';
import { SsoLoginByDeviceIdQuery } from '../impl';

@QueryHandler(SsoLoginByDeviceIdQuery)
export class SsoLoginByDeviceIdHandler implements IQueryHandler<SsoLoginByDeviceIdQuery> {
  constructor(
    private prisma: PrismaService,
    private authGrpcService: AuthGrpcService,
  ) { }

  async execute(command: SsoLoginByDeviceIdQuery) {
    const { user, deviceId, session } = command;

    const currentUser = await this.prisma.user.findUnique({
      where: { id: user.id },
    });

    try {
      const res = await this.authGrpcService
        .generateUserTokenByDeviceId({
          deviceId: deviceId,
          pass: currentUser.password,
          ip: session.ip,
          agent: session.media,
        })
        .toPromise();

      return { access_token: res.accessToken, refresh_token: res.refreshToken };
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException(err.message || 'Resource not found');
      }
      if (err.code === status.PERMISSION_DENIED) {
        throw new UnauthorizedException();
      }
      throw new InternalServerErrorException('Internal Server Error');
    }
  }
}
