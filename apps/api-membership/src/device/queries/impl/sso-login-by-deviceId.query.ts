import { IQuery } from "@nestjs/cqrs";
import { ICurrentUser } from "apps/api-membership/src/auth/strategies/types/user.type";
import { LoggedSessionCreate } from "apps/api-membership/src/auth/types";


export class SsoLoginByDeviceIdQuery implements IQuery {
  constructor(
    public readonly user: ICurrentUser,
    public readonly deviceId: string,
    public readonly session: LoggedSessionCreate
  ) {

  }
}