import { Modu<PERSON> } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { OrderPaymentCommandHandlers } from './commands';
import { OrderPaymentController } from './order-payment.controller';
import { OrderPaymentQueryHandlers } from './queries';
import { HttpModule } from '@nestjs/axios';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { OrderPaymentSchedulerService } from './services/order-payment.scheduler.service';

@Module({
  imports: [CqrsModule, HttpModule, InternalClientModule],
  controllers: [OrderPaymentController],
  providers: [...OrderPaymentQueryHandlers, ...OrderPaymentCommandHandlers, OrderPaymentSchedulerService],
})
export class OrderPaymentModule {}
