import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import {
  CreateOrderPaymentCommand,
  PaymentGatewayCommand,
  PaymentStatusCommand,
} from './commands';
import { CreateOrderPaymentDto } from './dto/create-order-payment.dto';
import { FilterOrderPaymentDto } from './dto/filter-order-payment.dto';
import { PaymentGatewayDto } from './dto/payment-gateway.dto';
import { GetOrderPaymentQuery, GetOrdersPaymentQuery } from './queries';
import { PaymentStatusDto } from './dto/payment-status.dto';
import { Throttle } from '@nestjs/throttler';

@ApiTags('Order Payment')
@Controller('order-payment')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class OrderPaymentController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Post()
  @Throttle({ default: { limit: 15, ttl: 60000 } })
  create(@Body() createOrderPaymentDto: CreateOrderPaymentDto) {
    return this.commandBus.execute(
      new CreateOrderPaymentCommand(createOrderPaymentDto),
    );
  }

  @Post('payment-gateway')
  @Throttle({ default: { limit: 15, ttl: 60000 } })
  @ApiBody({ schema: { type: 'object', properties: { orderId: { type: 'string' } } } })
  async getLink(@Body() { orderId, voucherCode }: { orderId: string, voucherCode: string }) {
    return this.commandBus.execute(
      new PaymentGatewayCommand(orderId),
    );
  }

  @Get()
  findAll(@User() user: ICurrentUser, @Query() filter: FilterOrderPaymentDto) {
    return this.queryBus.execute(new GetOrdersPaymentQuery(user, filter));
  }

  @Get(':id')
  findOne(@Param('id') id: string, @User() user: ICurrentUser) {
    return this.queryBus.execute(new GetOrderPaymentQuery(id, user));
  }

  @Post('payment-status')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  getStatusPayment(@Body() payment_request_id: PaymentStatusDto) {
    return this.commandBus.execute(
      new PaymentStatusCommand(payment_request_id),
    );
  }
}
