import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetOrderPaymentQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetOrderPaymentQuery)
export class GetOrderPaymentHandler
  implements IQueryHandler<GetOrderPaymentQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetOrderPaymentQuery) {
    const { id, user } = query;

    const userProperty = await this.prisma.userProperty.findFirst({
      where: { 
        userId: user.id,
        propertyId: user.propertyId
      },
      include: {
        property: true
      }
    });

    if (!userProperty) {
      throw new NotFoundException('Property not found');
    }

    const order = await this.prisma.order.findFirst({
      where: { 
        propertyId: userProperty.propertyId
      }
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    const orderPayment = await this.prisma.orderPayment.findFirst({
      where: { 
        AND: [
          { id: id },
          { orderId: order.id }
        ]
      }
    });

    if (!orderPayment) {
      throw new NotFoundException('Order payment not found');
    }

    return orderPayment;
  }
}