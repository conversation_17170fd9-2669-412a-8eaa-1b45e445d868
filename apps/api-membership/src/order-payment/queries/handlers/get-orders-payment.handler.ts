import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { GetOrdersPaymentQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetOrdersPaymentQuery)
export class GetOrdersPaymentHandler
  implements IQueryHandler<GetOrdersPaymentQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetOrdersPaymentQuery) {
    const { user, args } = query;

    return await this.prisma.orderPayment.findMany({
      //lwhere: { order: { property: { id: user.propertyId } } },
    });
  }
}
