import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'nestjs-prisma';
import { Prisma } from '@prisma/client';

@Injectable()
export class OrderPaymentSchedulerService {
  private readonly logger = new Logger(OrderPaymentSchedulerService.name);

  constructor(private readonly prisma: PrismaService) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async handlerExpiredPayment() {
    try {
      const currentTimestamp = BigInt(Date.now());

      const expiredPayments = await this.prisma.orderPayment.findMany({
        where: {
          expiredPayment: {
            lt: currentTimestamp,
          },
          isPaid: false,
          order: {
            status: {
              equals : "pending"
            }
          }
        },
        include: {
          order: true,
        },
      });

      for (const payment of expiredPayments) {
        await this.processExpiredPayment(payment);
      }

      this.logger.log(`Processed ${expiredPayments.length} expired payments`);
    } catch (error) {
      this.logger.error('Error processing expired payments', error);
    }
  }

  private async processExpiredPayment(
    payment: Prisma.OrderPaymentGetPayload<{ include: { order: true } }>,
  ) {
    try {
      await this.prisma.order.update({
        where: { id: payment.orderId },
        data: {
          status: 'cancelled',
          cancelAt: new Date(),
          cancelReason: 'Payment expired',
        },
      });

      this.logger.log(
        `Cancelled order ${payment.orderId} due to expired payment`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing expired payment for order ${payment.orderId}`,
        error,
      );
    }
  }
}
