import { <PERSON>Bus, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { lastValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { PaymentStatusCommand } from '../impl';
import { ITokenIseller } from '../../types';

@CommandHandler(PaymentStatusCommand)
export class PaymentStatusHandler implements ICommandHandler<PaymentStatusCommand> {
  constructor(
    private commandBus: CommandBus,
    private httpService: HttpService,
  ) {}

  async execute(command: PaymentStatusCommand) {
    const { payment_request_id } = command;

    // TODO : call rpc token isseler
    //const token: ITokenIseller = await this.commandBus.execute(
    //  new TokenIsellerCommand(),
    //);

    //const requeststatus = await lastValueFrom(
    //  this.httpService.post(
    //    `${token.resource_url}/api/v3/InquiryRequestPayment`,
    //    payment_request_id,
    //    {
    //      headers: {
    //        Authorization: `Bearer ${token.access_token}`,
    //      },
    //    },
    //  ),
    //);
    //return requeststatus.data;
    return;
  }
}
