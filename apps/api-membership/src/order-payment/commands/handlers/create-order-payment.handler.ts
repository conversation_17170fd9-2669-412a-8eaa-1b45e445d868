import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CreateOrderPaymentCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(CreateOrderPaymentCommand)
export class CreateOrderPaymentHandler
  implements ICommandHandler<CreateOrderPaymentCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: CreateOrderPaymentCommand) {
    const { args } = command;
    const item = await this.prisma.orderPayment.create({
      data: {
        by: args.by,
        url: args.url,
        expiredPayment: args.expiredPayment,
        isPaid: false,
        orderId: args.orderId,
      },
    });

    await this.prisma.order.update({
      where: { id: args.orderId },
      data: { status: 'pending' },
    });

    return item;
  }
}
