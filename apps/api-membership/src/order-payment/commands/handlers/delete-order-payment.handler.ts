import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { DeleteOrderPaymentCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@CommandHandler(DeleteOrderPaymentCommand)
export class DeleteOrderPaymentHandler
  implements ICommandHandler<DeleteOrderPaymentCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeleteOrderPaymentCommand) {
    const { id } = command;
    return this.prisma.orderPayment.delete({ where: { id: id } });
  }
}
