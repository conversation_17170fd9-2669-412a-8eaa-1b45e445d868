import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { PaymentGatewayCommand } from '../impl';
import {
  BadGatewayException,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { OrderPaymentGrpcService } from 'apps/api-membership/src/internal-client/order-payment.service';
import { invoiceGenerator } from '@app/common';

@CommandHandler(PaymentGatewayCommand)
export class PaymentGatewayHandler
  implements ICommandHandler<PaymentGatewayCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly orderPayment: OrderPaymentGrpcService,
  ) {}

  async execute(command: PaymentGatewayCommand) {
    const { orderId } = command;

    const order = await this.prisma.order.findFirst({
      where: { id: orderId },
      include: {
        property: {
          include: {
            users: {
              include: {
                user: {
                  include: {
                    profile: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!order) {
      throw new NotFoundException('order not found!');
    }

    if (order.status === 'cancelled') {
      throw new BadRequestException('order was canceled');
    }

    const paymentItem = await this.prisma.orderPayment.findFirst({
      where: {
        AND: [{ orderId: orderId }, { isPaid: false }, { paidAt: null }, { url: { not: ''}}],
      },
    });

    if (paymentItem) {
      if (paymentItem.isPaid) {
        throw new BadRequestException('This order already paid');
      }

      if (paymentItem.expiredPayment < Date.now()) {
        throw new BadRequestException('Order was expired');
      }

      return {
        checkoutUrl: paymentItem.url,
        paymentRequestId: paymentItem.paymentRequestId,
        status: order.status,
      };
    }

    try {
      const orderPayment = await this.orderPayment
        .orderCheckout({ id: order.id })
        .toPromise();
      return orderPayment;
    } catch (err) {
      console.log(err);
      if (err.code === 6 || err.code === 1) {
        throw new BadRequestException(err.message);
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }
}
