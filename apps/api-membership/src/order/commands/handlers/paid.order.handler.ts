import { <PERSON><PERSON><PERSON><PERSON>, ICommandH<PERSON>ler, QueryBus } from '@nestjs/cqrs';
import { PaidOrderCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { Metadata, status } from '@grpc/grpc-js';
import { WebGateway } from 'apps/api-membership/src/event/web.gateway';
import { OrderType } from '../../dto/create-order.dto';
import { invoiceGenerator } from '@app/common';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigurationGrpcService } from '../../services/configuration.grpc.service';
import { OrderInvoiceQuery } from '../../queries';
import { ICurrentUser } from '../../../auth/strategies/types/user.type';
import { IntegrationDlpService } from '../../../integration/services';
import { lastValueFrom } from 'rxjs';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';

interface currentUser {
  id: string;
  email: string;
  businessType: string;
}

@CommandHandler(PaidOrderCommand)
export class PaidOrderHandler implements ICommandHandler<PaidOrderCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly webGateway: WebGateway,
    private readonly mailerService: MailerService,
    private readonly configurationGrpcService: ConfigurationGrpcService,
    private readonly queryBus: QueryBus,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
    private readonly integrationService: IntegrationDlpService,
    private readonly postalGrpc: PostalGrpcService,
  ) {}

  async execute(command: PaidOrderCommand) {
    const { args } = command;

    let payload = {};
    const paidAtDate = new Date(args.paidAt);
    const paidAtUtc = new Date(paidAtDate.getTime() + 7 * 60 * 60 * 1000);
    const orderPayment = await this.prisma.orderPayment.findFirst({
      where: {
        AND: [
          {
            paymentRequestId: args.paymentRequestId,
          },
          {
            isPaid: false,
          },
          {
            paidAt: null,
          },
          {
            order: {
              id: args.orderId,
            },
          },
        ],
      },
      include: {
        order: {
          include: {
            property: true,
            details: true,
          },
        },
      },
    });
    if (!orderPayment) {
      throw new RpcException({
        code: status.CANCELLED,
        message: 'fault payment request id',
      });
    }

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        AND: [
          {
            user: {
              id: orderPayment.order.userId,
            },
          },
          {
            property: {
              id: orderPayment.order.propertyId,
            },
          },
        ],
      },
      include: {
        property: {
          include: {
            configuration: true,
          },
        },
        user: {
          include: {
            profile: true,
          },
        },
      },
    });

    const newReq = Array(32)
      .fill(0)
      .map(() => String.fromCharCode(65 + Math.floor(Math.random() * 26)))
      .join('');

    const categoryCode = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType({
        id: userProperty.property.configuration.options['industryPlan'],
      }),
    );

    const propertyType = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType({
        id: userProperty.property.propertyTypeId,
      }),
    );

    const meta = new Metadata();

    const postal = await lastValueFrom(
      this.postalGrpc.getPostal({ id: userProperty.property.postalId }, meta),
    );

    payload = {
      customer: {
        companyName: userProperty.property.companyName,
        companyEmail: userProperty.property.companyEmail,
        brandName: userProperty.property.brandName,
        postalCode: postal.code,
        categoryCode: categoryCode.categoryCode,
        unit: Number(
          userProperty.property.configuration.options['customfields']['value'],
        ),
        companyPhoneNumber: userProperty.property.companyPhoneNumber,
        licenseCode: userProperty.property.licenseKey,
        requestId: newReq,
      },
    };

    if (
      userProperty.property.configuration.options['licenseType'] === 'TEMPORARY'
    ) {
      await this.prisma.property.update({
        where: {
          id: userProperty.property.id,
        },
        data: {
          requestId: newReq,
        },
      });

      await this.integrationService.insertProperty(payload);
    }

    if (
      userProperty.property.configuration.options['licenseType'] ===
      'CONVENTIONAL'
    ) {
      const payload = {
        user: {
          email: userProperty.user.email,
          phoneNumber: userProperty.user.mobileNumber,
          firstName: userProperty.user.profile.firstName,
          lastName: userProperty.user.profile.lastName,
          dateOfBirth:
            userProperty.user.profile.dateOfBirth instanceof Date
              ? userProperty.user.profile.dateOfBirth
                  .toISOString()
                  .split('T')[0]
              : '',
          gender: userProperty.user.profile.gender,
          address: userProperty.user.profile.address || '',
          businessType: userProperty.user.businessType,

          placeOfBirth: userProperty.user.profile.placeOfBirth,
        },
        customer: {
          companyName: userProperty.property.companyName,
          companyEmail: userProperty.property.companyEmail,
          brandName: userProperty.property.brandName,
          postalCode: userProperty.property.postalId,
          categoryCode: categoryCode.categoryCode,
          unit: Number(
            userProperty.property.configuration.options['customfields'][
              'value'
            ],
          ),
          companyPhoneNumber: userProperty.property.companyPhoneNumber,
          industry: propertyType.name,
        },
        licenseCode: userProperty.property.licenseKey,
        requestId: newReq,
      };

      await this.integrationService.requestConventional(payload);

      await this.prisma.property.update({
        where: {
          id: userProperty.property.id,
        },
        data: {
          requestId: newReq,
        },
      });
    }

    try {
      const result = await this.prisma.$transaction(async (tr) => {
        const updatedOrderPayment = await tr.orderPayment.update({
          where: { id: orderPayment.id },
          data: {
            by: args.paymentMethod,
            isPaid: true,
            paymentId: args.paymentId,
            paidAt: paidAtUtc.toString(),
            order: {
              update: {
                status: 'completed',
              },
            },
          },
        });

        if (updatedOrderPayment) {
          await tr.orderPayment.updateMany({
            where: {
              AND: [
                {
                  id: {
                    not: orderPayment.order.id,
                  },
                },
                {
                  status: 'pending',
                },
                {
                  order: {
                    property: {
                      id: orderPayment.order.property.id,
                    },
                  },
                },
              ],
            },
            data: {
              status: 'cancelled',
            },
          });
        }

        const paymentHistory = await tr.paymentHistory.create({
          data: {
            orderId: orderPayment.orderId,
            propertyId: orderPayment.order.propertyId,
            description: `Payment - ${orderPayment.order.name}`,
            amount: orderPayment.order.totalPrice,
          },
        });

        await tr.notification.create({
          data: {
            fromId: orderPayment.order.userId,
            toId: orderPayment.order.userId,
            title: `Order ${orderPayment.order.name}`,
            message: `You completely order ${orderPayment.order.name}`,
            type: 'payment',
            tags: ['payment-complete'],
          },
        });

        this.webGateway.server
          .to(orderPayment.order.userId)
          .emit('notification', {
            type: 'payment',
            data: {
              orderId: orderPayment.order.id,
              title: `Order ${orderPayment.order.name}`,
              message: `You completely order ${orderPayment.order.name}`,
            },
          });
        return { updatedOrderPayment, paymentHistory };
      });

      for (const detail of orderPayment.order.details) {
        if (detail.itemType === OrderType.PLAN) {
          const pkgSerial = await invoiceGenerator(
            this.prisma,
            'orderDetail',
            {
              where: {
                itemType: OrderType.PLAN,
              },
            },
            'VEL',
          );
          await this.prisma.orderDetail.update({
            where: {
              id: detail.id,
            },
            data: {
              serial: pkgSerial,
            },
          });
        } else if (detail.itemType === OrderType.LICENSE) {
          const licSerial = await invoiceGenerator(
            this.prisma,
            'orderDetail',
            {
              where: {
                itemType: OrderType.LICENSE,
              },
            },
            'LIC',
          );
          await this.prisma.orderDetail.update({
            where: {
              id: detail.id,
            },
            data: {
              serial: licSerial,
            },
          });
        }
      }

      const userEmail = await this.prisma.user.findFirst({
        where: { id: orderPayment.order.userId },
      });

      const currentUser: currentUser = {
        id: userEmail.id,
        email: userEmail.email,
        businessType: userEmail.businessType,
      };

      await this.queryBus.execute(
        new OrderInvoiceQuery(
          currentUser as ICurrentUser,
          orderPayment.orderId,
        ),
      );

      return {
        code: status.OK,
        message: 'successfully paid order',
        paymentHistoryId: result.paymentHistory.id,
      };
    } catch (error) {
      throw new RpcException({
        code: status.INTERNAL,
        message: 'fail paid order',
        details: error.message,
      });
    }
  }
}
