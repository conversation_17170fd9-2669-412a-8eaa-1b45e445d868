import { <PERSON>celOrder<PERSON>andler } from './cancel-order.handler';
import { CreateOrderHandler } from './create-order.handler';
import { DeleteOrderHandler } from './delete-order.handler';
import { PaidOrder<PERSON>andler } from './paid.order.handler';
import { SendInvoiceEmailHandler } from './send-invoice-email.handler';
import { UpdateWaitingPaymentHandler } from './update-waiting-payment.handler';
import { TrialOrderHandler } from './trial-order.handler';

export const OrderCommandHandlers = [
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rder<PERSON><PERSON><PERSON>,
  DeleteOrder<PERSON>andler,
  SendInvoiceEmailHandler,
  UpdateWaitingPaymentHandler,
  TrialOrderHandler,
];
