import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { SendInvoiceEmailCommand } from '../impl/send-invoice-email.command';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';
import {
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Metadata } from '@grpc/grpc-js';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigurationGrpcService } from '../../services/configuration.grpc.service';

import puppeteer from 'puppeteer';
import * as moment from 'moment';
import { PackageService } from '../../../internal-client/services';
import { lastValueFrom } from 'rxjs';
import { toDateHuman } from '@app/common';

@CommandHandler(SendInvoiceEmailCommand)
export class SendInvoiceEmailHandler
  implements ICommandHandler<SendInvoiceEmailCommand>
{
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private postalGrpc: PostalGrpcService,
    private pkgGrpc: PackageService,
    private readonly mailerService: MailerService,
    private readonly configGrpc: ConfigurationGrpcService,
  ) {}

  async execute(
    command: SendInvoiceEmailCommand,
  ): Promise<{ message: string }> {
    const { orderId, user } = command;

    try {
      const item = await this.prisma.order.findFirst({
        where: {
          AND: [
            {
              property: {
                users: {
                  some: {
                    user: {
                      id: user.id,
                    },
                  },
                },
              },
            },
            { id: orderId },
          ],
        },
        include: {
          details: {
            include: {
              orderPackageDetail: true,
            },
            orderBy: { createdAt: 'desc' },
          },
          payment: true,
          property: true,
        },
      });

      if (!item) {
        throw new NotFoundException('Order not found');
      }

      const meta = new Metadata();
      const packageDetail = item.details.find(
        (detail) => detail.itemType === 'PLAN',
      );

      if (!packageDetail) {
        throw new NotFoundException('Package details not found');
      }

      // Get configuration for BCC emails
      let bccEmails = [];
      try {
        const bccEmailsResponse = await this.configGrpc.getBccEmails();
        bccEmails = Array.isArray(bccEmailsResponse?.emails)
          ? bccEmailsResponse.emails
          : [];
      } catch (error) {
        console.error('Error fetching BCC emails:', error);
      }

      // Generate PDF for email attachment
      const pdfBuffer = await this.generateInvoicePDF(
        item,
        packageDetail,
        meta,
      );

      if (!pdfBuffer || pdfBuffer.length === 0) {
        throw new Error('PDF generation failed: Empty buffer');
      }

      const year = new Date().getFullYear();

      await this.mailerService.sendMail({
        bcc: bccEmails,
        to: user.email,
        subject: `Order Confirmed - ${item.invoice}`,
        template: 'order-confirmed',
        context: {
          year: year,
          orderNumber: item.invoice,
          customerName: item.property.companyName,
        },
        attachments: [
          { filename: `Order-${item.invoice}.pdf`, content: pdfBuffer },
        ],
      });

      console.log('[Invoice email sent successfully]');

      return { message: 'Invoice email sent successfully' };
    } catch (error) {
      console.error('Error sending invoice email:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to send invoice email');
    }
  }

  private async generateInvoicePDF(
    item: any,
    packageDetail: any,
    meta: Metadata,
  ): Promise<Buffer> {
    let browser;
    let page;

    try {
      const propertyType = await lastValueFrom(
        this.propertyTypeGrpc.getOnePropertyType({
          id: item.property.propertyTypeId,
        }),
      );

      const postal = await lastValueFrom(
        this.postalGrpc.getPostal({ id: item.property.postalId }, meta),
      );

      const pkg = await lastValueFrom(
        this.pkgGrpc.client.getOnePackage(
          { id: packageDetail.itemId, orderId: item.property.id },
          meta,
        ),
      );

      const taxes = item.details
        .filter((detail) => detail.itemType === 'TAX')
        .map((tax) => ({
          name: tax.name,
          nominal: tax.orderPackageDetail?.nominal || 0,
          value: tax.price,
        }));

      const subTotalafter =
        Number(packageDetail.price) - Number(packageDetail.discount);
      const ttlPrice = item.totalPrice;

      // Get signature and footer images
      const signedNameUrl =
        'https://bucket.velodiva.com/membership-storage/static-assets/IMG_3711.webp';
      const footerImageUrl =
        'https://bucket.velodiva.com/membership-storage/static-assets/footer_invoice.webp';

      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Invoice ${item.invoice}</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
            @page {
              size: A4;
              margin: 0;
            }
            * {
              font-family: 'Poppins', sans-serif;
            }
            body {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            .footer-image {
              position: fixed;
              bottom: 0;
              left: 0;
              height: auto;
            }
            .keterangan {
              position: fixed;
              bottom: 31%;
              left: 0;
              right: 0;
              height: auto;
              text-align: center;
            }
            .first-uppercase:first-letter {
              text-transform: uppercase;
            }
          </style>
        </head>
        <body>
          <div style="padding: 12px;">
            <div style="max-width: 1000px; background-color: white;">
              <div style="display: flex; align-items: start; gap: 32px;">
                <div>
                  <img id="logo" src="https://bucket.velodiva.com/membership-storage/static-assets/velodiva_pink.webp" alt="Velodiva" style="width: 180px;">
                </div>
                <div style="display: flex; flex-direction: column;">
                  <span style="font-weight: 800; font-size: 22px;">INFORMASI PAKET BERLANGGANAN VELODIVA</span>
                  <span style="font-size: 22px; font-style: italic;">VELODIVA SUBSCRIPTION BILLING STATEMENT</span>
                </div>
              </div>
              <div style="width: 100%; height: 2px; margin-top: 16px; margin-bottom: 16px; background-color: #9ca3af;" />

              <div style="width: 100%; display: grid; grid-template-columns: 30% 70%; align-items: start; padding: 4px;">
                <div style="font-size: 10px;">
                  <div style="display: flex; flex-direction: column;">
                    <span style="font-weight: 800;">
                      Kepada <i>To</i>
                    </span>
                    <span style="font-weight: 400; margin-bottom: 6px;">
                    </span>
                    <span style="font-weight: 400;">
                      ${item.property.companyName}
                    </span>
                    <span style="font-weight: 400;">
                      ${item.property.address}
                    </span>
                    <span style="font-weight: 400;">
                      ${postal.city}
                    </span>
                    <span style="font-weight: 400;">
                      ${postal.code}
                    </span>
                  </div>
                </div>
                <table style="width: 100%; font-size: 10px;" cellpadding="0" cellspacing="0">
                  <tr>
                    <td style="font-weight: 800; padding: 0; width: 30%;; vertical-align: top">
                      Nomor Faktur
                    </td>
                    <td style="font-style: italic; width: 30%; vertical-align: top">
                      Invoice Number
                    </td>
                    <td style="font-weight: 800; width: 40%; vertical-align: top">
                      : ${item.invoice}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; padding: 0; vertical-align: top">
                      Tanggal Faktur
                    </td>
                    <td style="font-style: italic; vertical-align: top">
                      Invoice Date
                    </td>
                    <td style="font-weight: 800; vertical-align: top">
                      : ${toDateHuman(item.payment?.paidAt)}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; padding: 0; vertical-align: top">
                      NPWP / NIK
                    </td>
                    <td style="font-style: italic; vertical-align: top">
                      NPWP / NIK
                    </td>
                    <td style="font-weight: 800; vertical-align: top">
                      : ${this.formatNumberToFourGroups(item.property.npwp)}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; padding: 0; vertical-align: top">
                      Alamat NPWP
                    </td>
                    <td style="font-style: italic; vertical-align: top;">
                      NPWP Address
                    </td>
                    <td style="font-weight: 800; vertical-align: top;">
                      : ${item.property.billingAddress}
                    </td>
                  </tr>
                </table>
              </div>
              <div style="width: 100%; display: grid; grid-template-columns: 30% 70%; align-items: start; padding: 4px;">
                <div style="font-size: 10px;">

                </div>
                <table style="width: 100%; font-size: 10px;" cellpadding="0" cellspacing="0">
                  <tr>
                    <td style="font-weight: 800; padding: 0; width: 30%;; vertical-align: top">
                      Email Pelanggan
                    </td>
                    <td style="font-style: italic; width: 30%; vertical-align: top">
                      Customer Email
                    </td>
                    <td style="font-weight: 800; width: 40%; vertical-align: top">
                      : ${item.property.companyEmail}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; padding: 0; vertical-align: top">
                      Nomor ID Pelanggan
                    </td>
                    <td style="font-style: italic; vertical-align: top">
                      Customer ID
                    </td>
                    <td style="font-weight: 800; vertical-align: top">
                      : ${item.property.cid}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; padding: 0; vertical-align: top">
                      Nama Perusahaan
                    </td>
                    <td style="font-style: italic; vertical-align: top;">
                      Company Name
                    </td>
                    <td style="font-weight: 800; vertical-align: top;">
                      : ${item.property.companyName}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; padding: 0; vertical-align: top">
                      Merk Dagang
                    </td>
                    <td style="font-style: italic; vertical-align: top;">
                      Brand
                    </td>
                    <td style="font-weight: 800; vertical-align: top;">
                      : ${item.property.brandName}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; padding: 0; vertical-align: top">
                      Industri
                    </td>
                    <td style="font-style: italic; vertical-align: top;">
                      Industry
                    </td>
                    <td style="font-weight: 800; vertical-align: top;">
                      : ${(propertyType as any)?.name || ''}
                    </td>
                  </tr>
                </table>
              </div>
              <div style="margin-top: 8px;">
                <table style="border: 1px solid black; width: 100%; border-collapse: collapse; font-size: 10px;">
                  <tr style="background-color: #666;">
                    <td colspan="6" style="text-align: center; color: white; font-weight: 800;">
                      BIAYA BERLANGGANAN /<span style="font-weight: 800; font-style: italic;">SUBSCRIPTION FEE</span>
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;" rowspan="${8 + taxes.length}">
                      <i>Music Player</i>
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      Nomor ID Pelanggan
                    </td>
                    <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                      Customer ID
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;" colspan="3">
                      ${item.property.cid}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      Periode Pembayaran
                    </td>
                    <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                      Payment Term
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;" colspan="3">
                      ${packageDetail.duration === 'monthly' ? 'Bulanan' : 'Tahunan'}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      Jangka Waktu Paket
                    </td>
                    <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                      Subscription Term
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      ${toDateHuman((pkg as any)?.activeAt, true)}
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;">
                      s/d
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      ${toDateHuman((pkg as any)?.expiredAt, true)}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      <div style="display: flex; flex-direction: column; justify-content: center;">
                        <span style="font-weight: 800; text-align: center;">Deskripsi Layanan</span>
                        <span style="font-weight: 800; text-align: center; font-style: italic;">Item Description</span>
                      </div>
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      <div style="display: flex; flex-direction: column; justify-content: center;">
                        <span style="font-weight: 800; text-align: center;">Jumlah Item</span>
                        <span style="font-weight: 800; text-align: center; font-style: italic;">Total Item</span>
                      </div>
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      <div style="display: flex; flex-direction: column; justify-content: center;">
                        <span style="font-weight: 800; text-align: center;">Harga per Item</span>
                        <span style="font-weight: 800; text-align: center; font-style: italic;">Price per Item</span>
                      </div>
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      <div style="display: flex; flex-direction: column; justify-content: center;">
                        <span style="font-weight: 800; text-align: center;">Diskon</span>
                        <span style="font-weight: 800; text-align: center; font-style: italic;">Discount</span>
                      </div>
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                      <div style="display: flex; flex-direction: column; justify-content: center;">
                        <span style="font-weight: 800; text-align: center;">Total</span>
                        <span style="font-weight: 800; text-align: center; font-style: italic;">Total</span>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td style="border: 1px solid black; padding: 4px;">
                      ${item.name}
                    </td>
                    <td style="border: 1px solid black; padding: 4px; text-align: center;">
                      ${packageDetail.qty}
                    </td>
                    <td style="border: 1px solid black; padding: 4px; text-align: end;">
                      Rp ${this.numberToLocaleStringID(Number(packageDetail.price))}
                    </td>
                    <td style="border: 1px solid black; padding: 4px; text-align: end;">
                      Rp ${this.numberToLocaleStringID(Number(packageDetail.discount))}
                    </td>
                    <td style="border: 1px solid black; padding: 4px; text-align: end;">
                      Rp ${this.numberToLocaleStringID(Number(subTotalafter))}
                    </td>
                  </tr>
                  <tr>
                    <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                      SUB TOTAL
                    </td>
                    <td style="border: 1px solid black; padding: 4px; text-align: end;">
                      Rp ${this.numberToLocaleStringID(Number(subTotalafter))}
                    </td>
                  </tr>
                  <tr>
                    <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                      DPP Nilai Lain
                    </td>
                    <td style="border: 1px solid black; padding: 4px; text-align: end;">
                      Rp ${this.numberToLocaleStringID(Number(subTotalafter) * (11 / 12))}
                    </td>
                  </tr>
                  ${taxes
                    .map(
                      (tax) => `
                    <tr>
                      <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                        ${tax.name} (${tax.nominal}%)
                      </td>
                      <td style="border: 1px solid black; padding: 4px; text-align: end;">
                        Rp ${this.numberToLocaleStringID(Number(tax.value ?? 0))}
                      </td>
                    </tr>
                  `,
                    )
                    .join('')}
                  <tr>
                    <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                      TOTAL PEMBAYARAN <span style="font-style: italic; font-weight: 800;">TOTAL PAYMENT</span>
                    </td>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                      Rp ${this.numberToLocaleStringID(Number(ttlPrice))}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;">
                      <div style="display: flex; flex-direction: column;">
                        <span>Terbilang</span>
                        <span style="font-style: italic;">In Word</span>
                      </div>
                    </td>
                    <td colspan="5" style="border: 1px solid black; padding: 4px;">
                      <div style="display: flex; flex-direction: column;">
                        <span class="first-uppercase">${this.numberToWords(Number(ttlPrice), 'id')} rupiah</span>
                        <span class="first-uppercase" style="font-style: italic;">${this.numberToWords(Number(ttlPrice), 'en')} rupiahs</span>
                      </div>
                    </td>
                  </tr>
                </table>
              </div>
              <div style="display: grid; grid-template-columns: 60% 40%; align-items: center; margin-right: 10%;">
                <div>

                </div>
                <div style=";font-size: 10px; display: flex; flex-direction: column; align-items: end;">
                  <img src="${signedNameUrl}" alt="Direktur Keuangan" style="max-width: 180px;" />
                  <span style="text-align: center; margin-right: 36px;">Direktur Keuangan</span>
                </div>
              </div>
              <div class="keterangan" style="font-size: 10px;">
                Faktur ini merupakan bukti pembayaran lunas yang sah
                <br />
                <i>This invoice serves as an official receipt and proof of full payment.</i>
              </div>
            </div>
          </div>
          <div class="footer-image">
            <img id="footer-image" style="width: 100%;" src="${footerImageUrl}" alt="Footer Image">
          </div>
        </body>
        </html>
      `;

      browser = await puppeteer.launch({
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disable-extensions',
          '--disable-audio-output',
          '--single-process',
          '--no-zygote',
          '--disable-accelerated-2d-canvas',
          '--js-flags=--max-old-space-size=512',
          '--disable-web-security',
          '--disable-features=site-per-process',
          '--disable-notifications',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-breakpad',
          '--disable-component-extensions-with-background-pages',
          '--disable-ipc-flooding-protection',
          '--memory-pressure-off',
          // Additional performance optimizations
          '--disable-default-apps',
          '--disable-sync',
          '--disable-translate',
          '--hide-scrollbars',
          '--mute-audio',
          '--no-first-run',
          '--disable-plugins',
          '--disable-javascript',
          '--disable-plugins-discovery',
          '--disable-preconnect',
          '--disable-prefetch',
          '--no-pings',
          '--no-default-browser-check',
          '--disable-hang-monitor',
          '--disable-prompt-on-repost',
          '--disable-domain-reliability',
          '--disable-component-update',
          '--disable-background-networking',
          '--disable-background-downloads',
          '--disable-add-to-shelf',
          '--disable-client-side-phishing-detection',
          '--disable-datasaver-prompt',
          '--disable-desktop-notifications',
          '--disable-device-discovery-notifications',
          '--disable-infobars',
          '--disable-features=TranslateUI',
          '--disable-features=BlinkGenPropertyTrees',
          '--run-all-compositor-stages-before-draw',
          '--disable-threaded-animation',
          '--disable-threaded-scrolling',
          '--disable-checker-imaging',
          '--disable-new-bookmark-apps',
          '--disable-office-editing-component-app',
          '--disable-reading-from-canvas',
          '--disable-software-rasterizer',
          '--disable-features=VizDisplayCompositor',
        ],
        executablePath:
          process.env.PUPPETEER_EXECUTABLE_PATH || '/usr/bin/chromium',
        headless: true,
        defaultViewport: { width: 800, height: 1200 },
      });

      page = await browser.newPage();

      // Set smaller viewport to reduce memory usage
      await page.setViewport({
        width: 800,
        height: 600,
        deviceScaleFactor: 1
      });

      await page.setRequestInterception(true);
      page.on('request', (req) => {
        const resourceType = req.resourceType();
        const url = req.url();

        // Block unnecessary resources to reduce memory usage
        if (
          ['media', 'script', 'websocket', 'manifest', 'other'].includes(resourceType) ||
          url.includes('analytics') ||
          url.includes('tracking') ||
          url.includes('ads') ||
          url.includes('facebook') ||
          url.includes('twitter') ||
          url.includes('google-analytics')
        ) {
          req.abort();
        } else {
          req.continue();
        }
      });

      // Disable additional features to save resources
      await page.setJavaScriptEnabled(false);
      await page.setCacheEnabled(false);

      // Load content with minimal waiting to speed up generation
      await page.setContent(htmlContent, {
        waitUntil: 'domcontentloaded',
        timeout: 30000,
      });

      // Wait a bit for fonts to load
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Optimize PDF generation with minimal settings
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '0.4in',
          right: '0.4in',
          bottom: '0.4in',
          left: '0.4in',
        },
        preferCSSPageSize: true,
        omitBackground: false,
        timeout: 30000,
        // Additional optimizations
        displayHeaderFooter: false,
        tagged: false,
        outline: false,
      });

      return pdfBuffer;
    } catch (error) {
      console.error('[PDF generation error]:', error);
      throw error;
    } finally {
      try {
        if (page) {
          // Clear page resources before closing
          await page.removeAllListeners();
          await page.close();
        }
      } catch (closeError) {
        console.error('[Error closing page]:', closeError);
      }

      if (browser) {
        // Close all pages first
        const pages = await browser.pages();
        await Promise.all(pages.map(page => page.close().catch(() => {})));

        await browser.close();

        // Force garbage collection multiple times
        if (global.gc) {
          global.gc();
          setTimeout(() => global.gc && global.gc(), 100);
          setTimeout(() => global.gc && global.gc(), 500);
        }
      }
    }
  }



  private numberToLocaleStringID(value: number) {
    return value.toLocaleString('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
  }

  private formatNumberToFourGroups(numberInput: string): string {
    if (!numberInput) return '';

    try {
      const cleanedNumber = numberInput.replace(/\D/g, '');

      if (cleanedNumber.length !== 15 && cleanedNumber.length !== 16) {
        console.warn(
          `Number length is not suitable for formatting. It should be 15 or 16 digits but got ${cleanedNumber.length}.`,
        );
        return numberInput;
      }

      const group1 = cleanedNumber.substring(0, 2);
      const group2 = cleanedNumber.substring(2, 5);
      const group3 = cleanedNumber.substring(5, 8);
      const group4 = cleanedNumber.substring(8, 9);
      const group5 = cleanedNumber.substring(9, 12);
      const group6 = cleanedNumber.substring(12);

      return `${group1}.${group2}.${group3}.${group4}-${group5}.${group6}`;
    } catch (error) {
      console.error('Error formatting number:', error);
      return numberInput;
    }
  }

  private numberToWords(num: number, lang = 'en') {
    if (num === 0) return lang === 'en' ? 'zero' : 'nol';

    const words = {
      en: {
        ones: [
          '',
          'one',
          'two',
          'three',
          'four',
          'five',
          'six',
          'seven',
          'eight',
          'nine',
        ],
        teens: [
          '',
          'eleven',
          'twelve',
          'thirteen',
          'fourteen',
          'fifteen',
          'sixteen',
          'seventeen',
          'eighteen',
          'nineteen',
        ],
        tens: [
          '',
          'ten',
          'twenty',
          'thirty',
          'forty',
          'fifty',
          'sixty',
          'seventy',
          'eighty',
          'ninety',
        ],
        thousands: ['', 'thousand', 'million', 'billion'],
        hundred: 'hundred',
      },
      id: {
        ones: [
          '',
          'satu',
          'dua',
          'tiga',
          'empat',
          'lima',
          'enam',
          'tujuh',
          'delapan',
          'sembilan',
        ],
        teens: [
          '',
          'sebelas',
          'dua belas',
          'tiga belas',
          'empat belas',
          'lima belas',
          'enam belas',
          'tujuh belas',
          'delapan belas',
          'sembilan belas',
        ],
        tens: [
          '',
          'sepuluh',
          'dua puluh',
          'tiga puluh',
          'empat puluh',
          'lima puluh',
          'enam puluh',
          'tujuh puluh',
          'delapan puluh',
          'sembilan puluh',
        ],
        thousands: ['', 'ribu', 'juta', 'miliar'],
        hundred: 'ratus',
      },
    };

    function convertChunk(n, lang) {
      let str = '';
      const w = words[lang];

      if (n >= 100) {
        str += `${w.ones[Math.floor(n / 100)]} ${w.hundred} `;
        n %= 100;
      }

      if (n >= 11 && n <= 19) {
        str += `${w.teens[n - 10]} `;
        return str.trim();
      }

      if (n >= 10 || n === 10) {
        str += `${w.tens[Math.floor(n / 10)]} `;
        n %= 10;
      }

      if (n > 0) {
        str += `${w.ones[n]} `;
      }

      return str.trim();
    }

    let result = '';
    let chunkIndex = 0;

    while (num > 0) {
      const chunk = num % 1000;

      if (chunk !== 0) {
        const chunkWords = convertChunk(chunk, lang);
        result = `${chunkWords + (words[lang].thousands[chunkIndex] ? ` ${words[lang].thousands[chunkIndex]}` : '')} ${result}`;
      }

      num = Math.floor(num / 1000);
      chunkIndex++;
    }

    return result.trim();
  }
}
