import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { DeleteOrderCommand } from '../impl';

@CommandHandler(DeleteOrderCommand)
export class DeleteOrderHandler
  implements ICommandHandler<DeleteOrderCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeleteOrderCommand) {
    const { id } = command;
    return this.prisma.order.delete({ where: { id: id } });
  }
}
