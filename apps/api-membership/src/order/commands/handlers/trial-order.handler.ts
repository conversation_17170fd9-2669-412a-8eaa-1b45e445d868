import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { TrialOrderCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PlanGrpcService } from '../../../internal-client/plan.service';
import { IntegrationDlpService } from '../../../integration/services';
import { OrderType } from '../../dto/create-order.dto';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { status } from '@grpc/grpc-js';
import { invoiceGenerator } from '@app/common';
import { RpcException } from '@nestjs/microservices';

@CommandHandler(TrialOrderCommand)
export class TrialOrderHandler implements ICommandHandler<TrialOrderCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly planGrpcService: PlanGrpcService,
    private readonly integrationService: IntegrationDlpService,
  ) {}

  async execute(command: TrialOrderCommand) {
    const { args } = command;
    let totalPrice: number = 0;
    const orderItems = [];
    const withLicense = false;

    const order = await this.prisma.order.findFirst({
      where: { propertyId: args.propertyId },
    });

    if (order) {
      throw new RpcException({
        code: status.ALREADY_EXISTS,
        message: 'This property already apply demo',
      });
    }

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        AND: [
          {
            property: {
              id: args.propertyId,
            },
          },
        ],
      },
      include: {
        property: {
          include: {
            configuration: true,
          },
        },
      },
    });

    if (!userProperty && userProperty?.property) {
      throw new BadRequestException('order cannot be processed');
    }

    switch (args.itemType) {
      case OrderType.PLAN:
        let planPrice: number = 0;
        let planTaxPrice: number = 0;
        let planBasePrice: number = 0;
        const planTaxItems = [];
        try {
          const plan = await this.planGrpcService
            .detailPlan({ id: args.itemId })
            .toPromise();

          planPrice = plan.basePrice;
          planBasePrice = plan.basePrice;

          if (plan.taxes && plan.taxes.length > 0) {
            for (const tax of plan.taxes) {
              const start = new Date(tax.startDate);
              const end = new Date(tax.endDate);
              const now = new Date();

              if (now >= start && now < end) {
                if (tax.type === 'increase') {
                  planTaxPrice += (Number(tax.nominal) / 100) * planPrice;
                  planTaxItems.push({
                    name: tax.name,
                    nominal: tax.nominal,
                    type: tax.type,
                    value: planTaxPrice,
                  });
                } else if (tax.type === 'reduction') {
                  planTaxPrice -= (Number(tax.nominal) / 100) * planPrice;
                  planTaxItems.push({
                    name: tax.name,
                    nominal: tax.nominal,
                    type: tax.type,
                    value: planTaxPrice,
                  });
                }
              }
            }
          }

          planPrice += planTaxPrice;
          totalPrice += planPrice;
          Object.assign(plan, {
            itemType: OrderType.PLAN,
            basePrice: planBasePrice,
            totalPrice: planPrice,
            taxPrice: planTaxPrice,
            taxItems: planTaxItems,
          });
          orderItems.push(plan);
        } catch (err) {
          if (err.code === status.NOT_FOUND) {
            throw new NotFoundException('plan not found');
          } else {
            console.error('failed order', err.message);
            throw new InternalServerErrorException('failed order');
          }
        }
        break;
      default:
        break;
    }

    const orderTransact = await this.prisma.$transaction(async (tr) => {
      const order = await tr.order.create({
        data: {
          name: orderItems.map((item) => item.name).join(' + '),
          status: 'completed',
          totalPrice: totalPrice,
          discount: 0,
          tag: 'trial',
          propertyId: userProperty.property.id,
          userId: userProperty.userId,
          expiredAt: BigInt(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      for (const item of orderItems) {
        const pkg = await tr.orderDetail.create({
          data: {
            name: item.name,
            orderId: order.id,
            duration: item.duration,
            price: item.basePrice,
            sku: item?.sku?.code,
            itemType: item.itemType,
            itemId: item.id,
            qty: item.qty,
            totalPrice: item.totalPrice,
            tax: item.taxPrice,
            discount: 0,
            taxItems: item.taxItems,
          },
        });

        if (item?.features) {
          await tr.orderPackageDetail.createMany({
            data: item.features.map((ft) => {
              return {
                orderDetailId: pkg.id,
                featureId: ft.featureId,
                qty: Number(ft.qty),
              };
            }),
          });
        }
      }

      return order;
    });

    const orderInvoice = await invoiceGenerator(
      this.prisma,
      'order',
      {},
      'TRD',
    );
    await this.prisma.order.update({
      where: {
        id: orderTransact.id,
      },
      data: {
        invoice: orderInvoice,
      },
    });

    await this.prisma.paymentHistory.create({
      data: {
        orderId: orderTransact.id,
        propertyId: orderTransact.propertyId,
        description: `Trial - ${orderTransact.name}`,
        amount: 0,
      },
    });

    return await this.prisma.order.findFirst({
      where: {
        id: orderTransact.id,
      },
      include: {
        details: {
          include: {
            orderPackageDetail: true,
          },
        },
      },
    });
  }
}
