import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>andler } from '@nestjs/cqrs';
import {
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { CancelOrderCommand } from '../impl';
import { OrderPaymentGrpcService } from 'apps/api-membership/src/internal-client/order-payment.service';
import {
  ReferralService,
  VoucherService,
} from '../../../internal-client/services';
import { Metadata } from '@grpc/grpc-js';

@CommandHandler(CancelOrderCommand)
export class CancelOrderHandler implements ICommandHandler<CancelOrderCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly orderPayment: OrderPaymentGrpcService,
    private readonly voucherService: VoucherService,
    private readonly referralService: ReferralService,
  ) {}

  async execute(command: CancelOrderCommand) {
    const { id, user, args } = command;

    const order = await this.prisma.order.findFirst({
      where: {
        AND: [
          {
            property: {
              users: {
                some: {
                  user: {
                    id: user.id,
                  },
                },
              },
            },
          },
          { id: id },
          { status: 'pending' },
        ],
      },
      include: {
        payment: true,
      },
    });

    if (!order) {
      throw new NotFoundException('pending order not found');
    }

    try {
      if (order?.payment) {
        if (!order.payment.isPaid && order.payment.paymentId) {
          // TODO: ENABLE ONLY ON PRODUCTION
          //await this.orderPayment.cancelOrder(order.payment.paymentId, args.reason).toPromise();
        }
      }

      const meta = new Metadata();

      if (order.voucherCode) {
        await this.voucherService.client
          .updateStatusVoucher({ orderId: order.id }, meta)
          .toPromise();
      }

      if (order.referralCode) {
        await this.referralService.client
          .updateStatusReferral({ orderId: order.id }, meta)
          .toPromise();
      }

      await this.prisma.order.update({
        where: {
          id: order.id,
        },
        data: {
          status: 'cancelled',
          cancelReason: args.reason,
          cancelAt: new Date(),
        },
      });

      // Create order history
      //await this.prisma.orderHistory.create({
      //	data: {
      //		orderId: order.id,
      //		fromPropertyId: order.fromPropertyId,
      //		status: 'cancelled',
      //		totalPrice: order.totalPrice,
      //		discount: order.discount,
      //		tag: order.tag,
      //		event: 'OrderCancelled',
      //		snapshot: order
      //	}
      //});

      return 'Order cancelled successfully';
    } catch (error) {
      console.error('Error cancelling order:', error);
      throw new InternalServerErrorException(
        'Failed to cancel order',
        error.message,
      );
    }
  }
}
