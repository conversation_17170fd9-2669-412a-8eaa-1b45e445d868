import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CreateOrderCommand } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { OrderType } from '../../dto/create-order.dto';
import { status } from '@grpc/grpc-js';
import { IntegrationDlpService } from 'apps/api-membership/src/integration/services';
import { PlanGrpcService } from 'apps/api-membership/src/internal-client/plan.service';
import { LicenseGrpcService } from 'apps/api-membership/src/internal-client/license.service';
import { AddonGrpcService } from '../../../add-on/service/add-on.grpc.service';
import { invoiceGenerator } from '@app/common';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { lastValueFrom } from 'rxjs';

@CommandHandler(CreateOrderCommand)
export class CreateOrderHandler implements ICommandHandler<CreateOrderCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly planGrpcService: PlanGrpcService,
    private readonly addOnGrpcService: AddonGrpcService,
    private readonly licenseGrpcService: LicenseGrpcService,
    private readonly integrationService: IntegrationDlpService,
    private readonly propertyTypeGrpc: PropertyTypeGrpcService,
  ) {}

  async execute(command: CreateOrderCommand) {
    const { user, args } = command;
    console.log('[Order-Create] Starting order creation:', {
      userId: user.id,
      propertyId: args.propertyId,
      itemCount: args.items.length,
    });

    let totalPrice: number = 0;
    let payload = {};
    let withLicense = false;
    const orderItems = [];
    const now = new Date();
    let plan: any;

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        AND: [
          {
            user: {
              id: user.id,
            },
          },
          {
            property: {
              id: args.propertyId,
            },
          },
        ],
      },
      include: {
        property: {
          include: {
            configuration: true,
          },
        },
        user: {
          include: {
            profile: true,
          },
        },
      },
    });
    if (!userProperty && userProperty?.property) {
      throw new BadRequestException('order cannot be processed');
    }

    if (!userProperty.property.id) {
      throw new BadRequestException('property not registered');
    }

    const hasPlan = args.items.some((item) => item.type === OrderType.PLAN);
    const hasLicense = args.items.some(
      (item) => item.type === OrderType.LICENSE,
    );

    const licenseType =
      userProperty?.property.configuration.options['licenseType'];

    const existingOrder = await this.prisma.order.findFirst({
      where: {
        AND: [
          {
            property: {
              id: userProperty.property.id,
            },
          },
          {
            status: 'pending',
          },
          {
            details: {
              some: {
                AND: [
                  {
                    itemId: {
                      in: args.items.map((itm) => itm.id),
                    },
                  },
                ],
              },
            },
          },
        ],
      },
    });

    if (existingOrder) {
      throw new BadRequestException(
        'Cannot create a new order, please paid your order first',
      );
    }

    if (licenseType) {
      if (userProperty?.property.configuration.options['customfields']['id']) {
        try {
          const category = await this.integrationService.DetailCategoryById(
            userProperty?.property.configuration.options['customfields']['id'],
          );
          if (
            category[
              `${userProperty?.property.configuration.options['customfields']['type']}`
            ].length === 0
          ) {
            throw new BadRequestException('invalid custom fields');
          }
          withLicense = true;
        } catch (err) {
          throw new InternalServerErrorException('Internal Server Error');
        }
      }
    }

    for (const item of args.items) {
      switch (item.type) {
        case OrderType.PLAN:
          let planPrice: number = 0;
          let planTaxPrice: number = 0;
          let planBasePrice: number = 0;
          const planTaxItems = [];
          try {
            plan = await lastValueFrom(
              this.planGrpcService.detailPlan({ id: item.id }),
            );

            planPrice = plan.basePrice * item.quantity;
            planBasePrice = plan.basePrice;
            const featureMap = new Map();

            for (const feature of plan.features) {
              let featureQty = Number(feature.qty);
              if (featureQty !== 999) {
                featureQty *= item.quantity;

                if (featureMap.has(feature.featureId)) {
                  featureMap.set(
                    feature.featureId,
                    featureMap.get(feature.featureId) + featureQty,
                  );
                } else {
                  featureMap.set(feature.featureId, featureQty);
                }
              } else {
                featureMap.set(feature.featureId, featureQty);
              }
            }

            const updatedFeatures = [];

            for (const feature of plan.features) {
              const updatedQty = featureMap.get(feature.featureId) || 0;
              updatedFeatures.push({
                featureId: feature.featureId,
                qty: String(updatedQty),
                feature: feature,
              });
            }

            if (plan.taxes && plan.taxes.length > 0) {
              for (const tax of plan.taxes) {
                const start = new Date(tax.startDate);
                const end = new Date(tax.endDate);
                const now = new Date();

                if (now >= start && now < end) {
                  if (tax.type === 'increase') {
                    planTaxPrice += (Number(tax.nominal) / 100) * planPrice;
                    planTaxItems.push({
                      name: tax.name,
                      nominal: tax.nominal,
                      type: tax.type,
                      value: planTaxPrice,
                    });
                  } else if (tax.type === 'reduction') {
                    planTaxPrice -= (Number(tax.nominal) / 100) * planPrice;
                    planTaxItems.push({
                      name: tax.name,
                      nominal: tax.nominal,
                      type: tax.type,
                      value: planTaxPrice,
                    });
                  }
                }
              }
            }

            plan.features = updatedFeatures;
            planPrice += planTaxPrice;
            totalPrice += planPrice;
            Object.assign(plan, {
              itemType: OrderType.PLAN,
              basePrice: planBasePrice,
              totalPrice: planPrice,
              taxPrice: planTaxPrice,
              taxItems: planTaxItems,
              qty: item.quantity,
            });
            orderItems.push(plan);
          } catch (err) {
            if (err.code === status.NOT_FOUND) {
              throw new NotFoundException('plan not found');
            } else {
              console.error('failed order', err.message);
              throw new InternalServerErrorException('failed order');
            }
          }
          break;
        case OrderType.ADDON:
          let addOnPrice: number = 0;
          let addOnTaxPrice: number = 0;
          let addOnBasePrice: number = 0;

          try {
            const addOn = await this.addOnGrpcService
              .detailAddon({ id: item.id })
              .toPromise();
            addOnPrice = addOn.price;
            addOnBasePrice = addOn.price;
            if (addOn.taxes.length > 0) {
              for (const tax of addOn.taxes) {
                addOnTaxPrice +=
                  (Number(tax.nominal.toFixed(0)) / 100) * addOn.price;
              }
            }

            addOnPrice += addOnTaxPrice;
            totalPrice += addOnPrice;
            Object.assign(addOn, {
              itemType: OrderType.ADDON,
              basePrice: addOnBasePrice,
              totalPrice: addOnPrice,
              taxPrice: addOnTaxPrice,
            });
            orderItems.push(addOn);
          } catch (err) {
            if (err.code === status.NOT_FOUND) {
              throw new NotFoundException('plan not found');
            } else {
              throw new InternalServerErrorException('failed order');
            }
          }
          break;
        default:
          break;
      }
    }

    const id = userProperty?.property.configuration.options['categoryId'];

    if (licenseType === 'DLM') {
      const license = await lastValueFrom(
        this.licenseGrpcService.getLicense({ id }),
      );

      Object.assign(license, {
        itemType: OrderType.LICENSE,
        duration: 'yearly',
      });
      orderItems.push(license);

      const propertyType = await lastValueFrom(
        this.propertyTypeGrpc.getOnePropertyType({
          id: plan.propertyTypeId,
        }),
      );
      const categoryCode = await lastValueFrom(
        this.propertyTypeGrpc.getOnePropertyType({
          id: userProperty.property.configuration.options['industryPlan'],
        }),
      );

      payload = {
        user: {
          email: user.email,
          phoneNumber: userProperty.user.mobileNumber,
          firstName: userProperty.user.profile.firstName,
          lastName: userProperty.user.profile.lastName,
          dateOfBirth:
            userProperty.user.profile.dateOfBirth instanceof Date
              ? userProperty.user.profile.dateOfBirth
                  .toISOString()
                  .split('T')[0]
              : '',
          gender: userProperty.user.profile.gender,
          address: userProperty.user.profile.address || '',
          businessType: userProperty.user.businessType,

          placeOfBirth: userProperty.user.profile.placeOfBirth,
        },
        customer: {
          companyName: userProperty.property.companyName,
          companyEmail: userProperty.property.companyEmail,
          brandName: userProperty.property.brandName,
          postalCode: userProperty.property.postalId,
          categoryCode: categoryCode.categoryCode,
          unit: Number(
            userProperty.property.configuration.options['customfields'][
              'value'
            ],
          ),
          companyPhoneNumber: userProperty.property.companyPhoneNumber,
          industry: propertyType.name,
        },
      };

      const request = await this.integrationService.requestDlm(payload);
      await this.prisma.property.update({
        where: {
          id: args.propertyId,
        },
        data: {
          requestId: request.requestId,
        },
      });
    } else if (licenseType === 'CONVENTIONAL') {
    }

    console.log('[Order-Create] Creating order transaction:', {
      itemCount: orderItems.length,
      totalPrice: totalPrice,
    });

    const orderTransact = await this.prisma.$transaction(async (tr) => {
      const order = await tr.order.create({
        data: {
          name: orderItems
            .filter((item) => item.itemType !== OrderType.LICENSE)
            .map((item) => item.name)
            .join(' + '),
          status: 'pending',
          totalPrice: totalPrice,
          discount: 0,
          tag: 'new-subscription',
          propertyId: userProperty.property.id,
          userId: userProperty.userId,
          expiredAt: BigInt(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      for (const items of orderItems) {
        const pkg = await tr.orderDetail.create({
          data: {
            name: items.name,
            orderId: order.id,
            duration: items.duration,
            price: items.basePrice,
            sku: items?.sku?.code,
            itemType: items.itemType,
            itemId: items.id,
            qty: items.qty,
            totalPrice: items.totalPrice,
            tax: items.taxPrice,
            discount: 0,
            taxItems: items.taxItems,
          },
        });

        if (items?.features) {
          await tr.orderPackageDetail.createMany({
            data: items?.features.map((ft) => {
              return {
                orderDetailId: pkg.id,
                featureId: ft.featureId,
                qty: Number(ft.qty),
              };
            }),
          });
        }
      }

      return order;
    });

    const orderInvoice = await invoiceGenerator(
      this.prisma,
      'order',
      {},
      'ORD',
    );
    await this.prisma.order.update({
      where: {
        id: orderTransact.id,
      },
      data: {
        invoice: orderInvoice,
      },
    });

    const order = await this.prisma.order.findFirst({
      where: {
        id: orderTransact.id,
      },
      include: {
        details: {
          include: {
            orderPackageDetail: true,
          },
        },
      },
    });

    return {
      message: 'successfully order',
      data: order,
    };
  }
}
