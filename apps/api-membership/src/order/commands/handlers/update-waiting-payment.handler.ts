import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { status } from '@grpc/grpc-js';
import { UpdateWaitingPaymentCommand } from '../impl';
import { invoiceGenerator } from '@app/common';

@CommandHandler(UpdateWaitingPaymentCommand)
export class UpdateWaitingPaymentHandler
  implements ICommandHandler<UpdateWaitingPaymentCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UpdateWaitingPaymentCommand) {
    const { args } = command;
    try {
      await this.prisma.orderPayment.upsert({
        where: {
          orderId: args.id,
        },
        create: {
          url: args.url,
          expiredPayment: BigInt(args.expired),
          isPaid: args.isPaid,
          paymentRequestId: args.paymentRequestId,
          orderId: args.id,
        },
        update: {
          url: args.url,
          expiredPayment: Number(args.expired),
          isPaid: args.isPaid,
          paymentRequestId: args.paymentRequestId,
        },
      });

      // const orderInvoice = await invoiceGenerator(this.prisma, "order", {}, 'ORD');
      // await this.prisma.order.update({
      //   where: {
      //     id: args.id
      //   },
      //   data: {
      //     invoice: orderInvoice,
      //   }
      // });

      return {
        code: status.OK,
        message: 'Success update order payment',
      };
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException('Internal Server Error');
    }
    //const nowPlus = now.add(30, 'minutes');

    // TODO : call rpc token isseler
    // const token: ITokenIseller = await this.commandBus.execute(
    //   new TokenIsellerCommand(),
    // );

    //const clientId = '262e2008c28f449e8e29b42d4b5af50b';
    //const clientSecret =
    //  'secret-dZQakN9rRCPBur5mt80q7GqPi2qFUgQCiNF9SRWzW0Ryt+rDM7jWI+Z4WOlgw3wixTReXJ+Do6k8hWYL/lREmyI5ZL0lfGUgv5yUI7uzKO8=';

    //const data = {
    //  amount: order.basePrice,
    //  items: [
    //    {
    //      quantity: 1,
    //      name: order.duration,
    //      sku: 'SKU01',
    //      id: order.id,
    //      price: order.basePrice,
    //    },
    //  ],
    //  customer: {
    //    firstname: order?.property?.user?.profile?.firstName,
    //    id: order?.property?.user?.id,
    //    email: order?.property?.user?.email,
    //  },
    //  setting: {
    //    expired_date: nowPlus.toISOString(),
    //    payment_methods: [
    //      'bank_transfer',
    //      'ebanking',
    //      'mbanking',
    //      'creditcard',
    //      'convenient_store',
    //      'gopay',
    //      'ovo',
    //      'shopeepay',
    //      'dana',
    //      'yukk',
    //      'voucher',
    //    ],
    //    disable_payment_email: true,
    //    ui_mode: 'standard',
    //  },
    //  signature: ''
    //}
    //const itemIds = data.items.map(item => item.id);
    //const signatureString = `${clientId}.${itemIds.join('.')}.${data.amount}.${clientSecret}`;
    //const signature = generateMd5(signatureString).toUpperCase();

    // data.signature = signature;

    // const requestPayment = await lastValueFrom(
    //   this.httpService.post(
    //     'https://vntstore.iseller.io/api/v3/RequestPayment',
    //     data,
    //     {
    //       headers: {
    //         'Content-Type': 'application/json',
    //         'X-Notification-Url': 'http://103.58.160.91:8001/v1/webhook',
    //         Authorization: `Bearer ${token.access_token}`,
    //       },
    //       timeout: 15000,
    //     },
    //   ),
    // );

    // if (!paymentItem) {
    //   await this.prisma.orderPayment.create({
    //     data: {
    //       url: requestPayment.data.checkout_url,
    //       expiredPayment: 30,
    //       status: 'waiting',
    //       payment_request_id: requestPayment.data.payment_request_id,
    //       orderId: orderId,
    //     },
    //   });
    // }

    // return requestPayment.data;
  }
}
