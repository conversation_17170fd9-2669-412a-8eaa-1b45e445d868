import { ICommand } from "@nestjs/cqrs";
import { ICurrentUser } from "apps/api-membership/src/auth/strategies/types/user.type";
import { CancelOrderDto } from "../../dto/cancel-order.dto";

export class CancelOrderCommand implements ICommand {
    constructor(
        public readonly user: ICurrentUser,
        public readonly id: string,
        public readonly args: CancelOrderDto
    ) {
        
    }
}