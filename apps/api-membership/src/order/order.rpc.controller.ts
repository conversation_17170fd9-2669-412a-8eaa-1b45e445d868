import { Id, Query, Status } from '@app/proto-schema/index.common';
import {
  CheckPaymentRequest,
  GetAllOrderResponse,
  GetListAllOrderResponse,
  Order,
  ORDER_SERVICE_NAME,
  OrderServiceController,
  PaidOrderRequest,
  UpdateWaitingOrderRequest,
  TrialOrderRequest,
  Orders,
  GetActivePropertyByOrderStatusCounterResponse,
  GetActivePropertyByOrderStatusCounterRequest,
  GetOrderDetailByPropertyRequest,
} from '@app/proto-schema/index.membership';
import { Metadata, status } from '@grpc/grpc-js';
import {
  BadRequestException,
  Controller,
  NotFoundException,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import {
  CheckOrderPaymentQuery,
  GetDetailAllOrderQuery,
  GetListAllOrdersQuery,
  GetOrderDetailByPropertyQuery,
  GetOrderDetailQuery,
} from './queries';
import {
  PaidOrderCommand,
  UpdateWaitingPaymentCommand,
} from './commands';
import { TrialOrderCommand } from './commands/impl/trial-order.command';
import { GetActivePropertyByOrderStatusCounterQuery } from './queries/impl/get-active-property-by-order-status-counter.query';
@Controller()
export class OrderRpcController implements OrderServiceController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}
  @GrpcMethod(ORDER_SERVICE_NAME, 'getDetailAllOrder')
  getDetailAllOrder(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<GetAllOrderResponse>
    | Observable<GetAllOrderResponse>
    | GetAllOrderResponse {
    return this.queryBus.execute(new GetDetailAllOrderQuery(request.id));
  }
  @GrpcMethod(ORDER_SERVICE_NAME, 'getListAllOrder')
  getListAllOrder(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<GetListAllOrderResponse>
    | Observable<GetListAllOrderResponse>
    | GetListAllOrderResponse {
    try {
      const query = request.query ? JSON.parse(request.query) : {};
      return this.queryBus.execute(
        new GetListAllOrdersQuery({
          limit: query?.limit,
          page: query?.page,
          search: query?.search,
          status: query?.status,
          tag: query?.tag,
        }),
      );
    } catch (error) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Invalid query format',
      });
    }
  }
  @GrpcMethod(ORDER_SERVICE_NAME, 'updateWaitingOrder')
  updateWaitingOrder(
    request: UpdateWaitingOrderRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(new UpdateWaitingPaymentCommand(request));
  }
  @GrpcMethod(ORDER_SERVICE_NAME, 'getOrderDetail')
  getOrderDetail(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<Order> | Observable<Order> | Order {
    return this.queryBus.execute(new GetOrderDetailQuery(request.id));
  }

  @GrpcMethod(ORDER_SERVICE_NAME, 'getOrderDetailByProperty')
  getOrderDetailByProperty(
    request: GetOrderDetailByPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Orders> | Observable<Orders> | Orders {
    return this.queryBus.execute(
      new GetOrderDetailByPropertyQuery(request.propertyId),
    );
  }

  @GrpcMethod(ORDER_SERVICE_NAME, 'checkPayment')
  checkPayment(
    request: CheckPaymentRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Order> | Observable<Order> | Order {
    return this.queryBus.execute(
      new CheckOrderPaymentQuery(request.paymentRequestId),
    );
  }
  @GrpcMethod(ORDER_SERVICE_NAME, 'paidOrder')
  paidOrder(
    request: PaidOrderRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(new PaidOrderCommand(request));
  }
  @GrpcMethod(ORDER_SERVICE_NAME, 'trialOrder')
  trialOrder(
    request: TrialOrderRequest,
    metadata: Metadata,
    ...rest
  ): Promise<Order> | Observable<Order> | Order {
    return this.commandBus.execute(new TrialOrderCommand(request));
  }

  @GrpcMethod(ORDER_SERVICE_NAME, 'getActivePropertyByOrderStatusCounter')
  getActivePropertyByOrderStatusCounter(
    request: GetActivePropertyByOrderStatusCounterRequest,
    metadata: Metadata,
    ...rest
  ):
    | Promise<GetActivePropertyByOrderStatusCounterResponse>
    | Observable<GetActivePropertyByOrderStatusCounterResponse>
    | GetActivePropertyByOrderStatusCounterResponse {
    return this.queryBus.execute(
      new GetActivePropertyByOrderStatusCounterQuery(request),
    );
  }
}
