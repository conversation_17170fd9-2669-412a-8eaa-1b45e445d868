import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { PlanModule } from '../plan/plan.module';
import { OrderController } from './order.controller';
import { OrderCommandHandlers } from './commands';
import { OrderRpcController } from './order.rpc.controller';
import { OrderQueryHandlers } from './queries';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { IntegrationModule } from '../integration/integration.module';
import { AddonModule } from '../add-on/add-on.module';
import { ConfigurationGrpcService } from './services/configuration.grpc.service';
import { ClientsModule } from '@nestjs/microservices';
import { INTERNAL_PACKAGE, InternalClient } from 'libs/clients';
import { PropertyTypeModule } from '../property-type/property-type.module';
import { ConfigService } from '@nestjs/config';
import { PostalModule } from '../postal/postal.module';
import { OrderSchedulerService } from './services/order.scheduler.service';
import { LicenseModule } from '../license/license.module';

@Module({
  imports: [
    CqrsModule,
    AddonModule,
    PlanModule,
    InternalClientModule,
    IntegrationModule,
    PropertyTypeModule,
    IntegrationModule,
    LicenseModule,
    PostalModule,
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) =>
          InternalClient(configService),
        name: INTERNAL_PACKAGE,
      },
    ]),
  ],
  controllers: [OrderController, OrderRpcController],
  providers: [
    ...OrderCommandHandlers,
    ...OrderQueryHandlers,
    ConfigurationGrpcService,
    OrderSchedulerService,
  ],
  exports: [ConfigurationGrpcService],
})
export class OrderModule {}
