import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetOrderLicenseQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { NotFoundException } from '@nestjs/common';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';
import { Metadata } from '@grpc/grpc-js';

@QueryHandler(GetOrderLicenseQuery)
export class GetOrderLicenseHandler
  implements IQueryHandler<GetOrderLicenseQuery>
{
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private postalGrpc: PostalGrpcService,
  ) {}

  async execute(query: GetOrderLicenseQuery) {
    const { id, user } = query;

    const item = await this.prisma.order.findFirst({
      where: {
        AND: [
          {
            property: {
              users: {
                some: {
                  user: {
                    id: user.id,
                  },
                },
              },
            },
          },
          { id: id },
        ],
      },
      include: {
        details: {
          include: {
            orderPackageDetail: true,
          },
          orderBy: { createdAt: 'desc' },
        },
        payment: true,
        property: true,
      },
    });

    if (!item) {
      throw new NotFoundException();
    }

    const meta = new Metadata();

    const [propertyType, postal] = await Promise.all([
      this.propertyTypeGrpc
        .getOnePropertyType({ id: item.property.propertyTypeId })
        .toPromise(),
      this.postalGrpc
        .getPostal({ id: item.property.postalId }, meta)
        .toPromise(),
    ]);

    const { propertyId, ...response } = item;
    const packageDetail = item.details.find(
      (detail) => detail.itemType === 'LICENSE',
    );

    const managementFeeCalculated = packageDetail?.price
      ? Number(packageDetail.price) * 0.2
      : 0;
    const priceCalculated = packageDetail?.price
      ? Number(packageDetail.price) * 0.8
      : 0;

    return {
      id: item.id,
      name: packageDetail?.name,
      invoice: item.invoice,
      description: item.description,
      status: item.status,
      totalPrice: item.totalPrice,
      discount: item.discount,
      industry: propertyType.name,
      package: {
        id: packageDetail?.id,
        name: packageDetail?.name,
        duration: packageDetail?.duration,
        price: String(priceCalculated),
        managementFee: String(managementFeeCalculated),
        subTotal: packageDetail?.price,
        qty: packageDetail?.qty,
        subtotalAfterDiscount: packageDetail?.totalPrice,
        tax: packageDetail?.tax,
        totalPrice: packageDetail?.totalPrice,
        taxItems: packageDetail?.taxItems,
        discount: packageDetail?.discount,
      },
      paidAt: item.payment.paidAt,
      licenseId: item.property.licenseKey,
      property: {
        cid: item.property.cid,
        companyName: item.property.companyName,
        brandName: item.property.brandName,
        companyEmail: item.property.companyEmail,
        industryName: propertyType.name,
        npwp: item.property.npwp,
        address: item.property.address,
        postal: {
          province: {
            name: postal.province,
          },
          city: {
            name: postal.city,
          },
          district: {
            name: postal.district,
          },
          urban: {
            name: postal.urban,
          },
          zip: {
            code: postal.code,
          },
        },
      },
    };
  }
}
