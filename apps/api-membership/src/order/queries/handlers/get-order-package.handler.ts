import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetOrderPackageQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { NotFoundException } from '@nestjs/common';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';
import { Metadata } from '@grpc/grpc-js';
import {
  PackageService,
  VoucherService,
} from '../../../internal-client/services';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigurationGrpcService } from '../../services/configuration.grpc.service';

@QueryHandler(GetOrderPackageQuery)
export class GetOrderPackageHandler
  implements IQueryHandler<GetOrderPackageQuery>
{
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private postalGrpc: PostalGrpcService,
    private pkgGrpc: PackageService,
    private readonly mailerService: MailerService,
    private readonly configGrpc: ConfigurationGrpcService,

    private voucherService: VoucherService,
  ) {}

  async execute(Query: GetOrderPackageQuery) {
    const { id, user } = Query;

    const item = await this.prisma.order.findFirst({
      where: {
        AND: [
          {
            property: {
              users: {
                some: {
                  user: {
                    id: user.id,
                  },
                },
              },
            },
          },
          { id: id },
        ],
      },
      include: {
        details: {
          include: {
            orderPackageDetail: true,
          },
          orderBy: { createdAt: 'desc' },
        },
        payment: true,
        property: true,
      },
    });

    if (!item) {
      throw new NotFoundException();
    }
    const meta = new Metadata();

    const packageDetail = item.details.find(
      (detail) => detail.itemType === 'PLAN',
    );

    const [propertyType, postal, pkg] = await Promise.all([
      this.propertyTypeGrpc
        .getOnePropertyType({ id: item.property.propertyTypeId })
        .toPromise(),
      this.postalGrpc
        .getPostal({ id: item.property.postalId }, meta)
        .toPromise(),
      this.pkgGrpc.client
        .getOnePackage(
          { id: packageDetail.itemId, orderId: item.propertyId },
          meta,
        )
        .toPromise(),
    ]);

    const { propertyId, ...response } = item;

    const totalZone =
      packageDetail?.orderPackageDetail.reduce((acc, curr) => {
        return curr.featureId === 'f-002' ? acc + curr.qty : acc;
      }, 0) || 0;

    let taxItems;
    if (typeof packageDetail.taxItems === 'string') {
      taxItems = JSON.parse(packageDetail.taxItems);
    } else {
      taxItems = packageDetail.taxItems;
    }
    const taxes = taxItems.map((tax) => {
      if (tax.name === 'PPh23') {
        return {
          ...tax,
          value:
            Number(packageDetail.price) *
            packageDetail.qty *
            (Number(tax.nominal) / 100),
        };
      }

      return tax;
    });

    const subTotalafter =
      Number(packageDetail?.price) * packageDetail.qty -
      Number(packageDetail.discount);

    return {
      id: item.id,
      name: item.name,
      invoice: item.invoice,
      description: item.description,
      status: item.status,
      totalPrice: item.totalPrice,
      discount: item.discount,
      totalZone,
      industry: propertyType.name,
      package: {
        id: packageDetail?.id,
        name: packageDetail?.name,
        duration: packageDetail?.duration,
        price: packageDetail?.price,
        qty: packageDetail?.qty,
        subtotalAfterDiscount: String(subTotalafter),
        totalPrice: String(Number(subTotalafter) + Number(packageDetail?.tax)),
        tax: Number(packageDetail?.tax),
        taxItems: taxes,
        discount: packageDetail?.discount,
        activeAt: pkg.activeAt,
        expiredAt: pkg.expiredAt,
      },
      paidAt: item.payment.paidAt,
      licenseId: item.property.licenseKey,
      property: {
        cid: item.property.cid,
        companyName: item.property.companyName,
        brandName: item.property.brandName,
        companyEmail: item.property.companyEmail,
        industryName: propertyType.name,
        npwp: item.property.npwp,
        billingAddress: item.property.billingAddress,
        address: item.property.address,
        postal: {
          province: {
            name: postal.province,
          },
          city: {
            name: postal.city,
          },
          district: {
            name: postal.district,
          },
          urban: {
            name: postal.urban,
          },
          zip: {
            code: postal.code,
          },
        },
      },
    };
  }
}
