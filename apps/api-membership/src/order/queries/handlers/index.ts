import { GetOrderDetailByPropertyHandler } from './get-order-detail-by-property.handler';
import { GetOrderHandler } from './get-order.handler';
import { GetOrdersHandler } from './get-orders.handler';
import { GetListAllOrdersHandler } from './get-list-all-order.handler';
import { GetDetailAllOrderHandler } from './get-detail-all-order.handler';
import { CheckOrderPaymentHandler } from './check-order-payment.handler';
import { GetOrderDetailHandler } from './get-order-detail.handler';
import { GetActivePropertyByOrderStatusCounterHandler } from './get-active-property-by-order-status-counter.handler';
import { GetOrderLicenseHandler } from './get-order-license.handler';
import { GetOrderPackageHandler } from './get-order-package.handler';
import { OrderInvoiceHandler } from './order-invoice.handler';

export const OrderQueryHandlers = [
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rderD<PERSON>il<PERSON><PERSON><PERSON>,
  GetListAllOrders<PERSON><PERSON><PERSON>,
  GetDetailAllOrderHandler,
  GetOrderDetailByPropertyHandler,
  GetActivePropertyByOrderStatusCounterHandler,
  GetOrderLicenseHandler,
  GetOrderPackageHandler,
  OrderInvoiceHandler,
];
