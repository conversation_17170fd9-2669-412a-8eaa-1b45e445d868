import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { CheckOrderPaymentQuery } from '../impl';

@QueryHandler(CheckOrderPaymentQuery)
export class CheckOrderPaymentHandler
  implements IQueryHandler<CheckOrderPaymentQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: CheckOrderPaymentQuery) {
    const { paymentRequestId } = query;

    const orderPayment = await this.prisma.orderPayment.findFirst({
      where: { paymentRequestId: paymentRequestId },
      include: {
        order: {
          include: {
            details: {
              include: {
                orderPackageDetail: true,
              },
            },
            User: true
          },
        },
      },
    });
    if (!orderPayment) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'order payment not found',
      });
    }
    console.log(orderPayment)

    return {
      id: orderPayment.order.id,
      name: orderPayment.order.name,
      description: orderPayment.order.description,
      status: orderPayment.order.status,
      totalPrice: orderPayment.order.totalPrice,
      discount: orderPayment.order.discount,
      propertyId: orderPayment.order.propertyId,
      voucherCode: orderPayment.order.voucherCode,
      tag: orderPayment.order.tag,
      createdAt: orderPayment.order.createdAt,
      updatedAt: orderPayment.order.updatedAt,
      //type: orderPayment.order.type,
      orderPayment: {
        id: orderPayment.id,
        by: orderPayment.by,
        url: orderPayment.url,
        paymentRequestId: orderPayment.paymentRequestId,
        expiredPayment: orderPayment.expiredPayment,
        isPaid: orderPayment.isPaid,
        createdAt: orderPayment.createdAt,
        updatedAt: orderPayment.updatedAt,
      },
      orderDetail: orderPayment.order.details.map((detail) => {
        return {
          id: detail.id,
          name: detail.name,
          totalPrice: detail.totalPrice,
          price: detail.price,
          itemType: detail.itemType,
          itemId: detail.itemId,
          duration: detail.duration,
          tax: detail.tax,
          discount: detail.discount,
          sku: detail.sku,
          qty: detail.qty,
          orderPackageDetail:
            detail.orderPackageDetail.length !== 0
              ? detail.orderPackageDetail.map((pkg) => {
                  return {
                    id: pkg.id,
                    //price: pkg.price,
                    //totalPrice: pkg.totalPrice,
                    qty: pkg.qty,
                    //increment: pkg.increment,
                    featureId: pkg.featureId,
                    createdAt: pkg.createdAt,
                    updatedAt: pkg.updatedAt,
                  };
                })
              : null,
          createdAt: detail.createdAt,
          updatedAt: detail.updatedAt,
        };
      }),
      userId: orderPayment.order.userId
      //createdAt: orderPayment.order.createdAt,
      //updatedAt: orderPayment.order.updatedAt
    };
  }
}
