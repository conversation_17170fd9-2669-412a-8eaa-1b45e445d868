import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Prisma } from '@prisma/client';
import { GetOrdersQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetOrdersQuery)
export class GetOrdersHandler implements IQueryHandler<GetOrdersQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetOrdersQuery) {
    const { user, args } = query;

    const whereConditions: any[] = [
      {
        property: {
          users: {
            some: {
              user: {
                id: user.id,
              },
            },
          },
        },
      },
    ];

    if (args?.status) {
      whereConditions.push({ status: args.status });
    }

    if (args?.tag) {
      whereConditions.push({ tag: args.tag });
    }

    if (args?.propertyId) {
      whereConditions.push({ propertyId: args.propertyId });
    }

    if (args?.invoiceNumber && args.invoiceNumber.trim() !== '') {
      whereConditions.push({
        invoice: { contains: args.invoiceNumber, mode: 'insensitive' },
      });
    }

    if (args?.orderName && args.orderName.trim() !== '') {
      whereConditions.push({
        name: { contains: args.orderName, mode: 'insensitive' },
      });
    }

    if (args?.companyName && args.companyName.trim() !== '') {
      whereConditions.push({
        property: {
          companyName: { contains: args.companyName, mode: 'insensitive' },
        },
      });
    }

    if (args?.orderAt) {
      const orderDate = new Date(args.orderAt);
      const nextDay = new Date(orderDate);
      nextDay.setDate(nextDay.getDate() + 1);

      whereConditions.push({
        createdAt: {
          gte: orderDate,
          lt: nextDay,
        },
      });
    }

    if (args?.search && args.search.trim() !== '') {
      const searchTerm = args.search;
      whereConditions.push({
        OR: [
          { invoice: { contains: searchTerm, mode: 'insensitive' } },
          { name: { contains: searchTerm, mode: 'insensitive' } },
          {
            property: {
              companyName: { contains: searchTerm, mode: 'insensitive' },
            },
          },
        ],
      });
    }

    const items = await Pagination<any, Prisma.OrderFindManyArgs>(
      this.prisma.order,
      {
        orderBy: { createdAt: args?.sortType || 'desc' },
        where: {
          AND: whereConditions,
        },
        include: {
          details: { include: { orderPackageDetail: true } },
          property: true,
        },
      },
      { page: args.page, limit: args.limit },
    );

    const orderIds = items.data.map((item) => item.id);

    const payments = await this.prisma.orderPayment.findMany({
      where: { orderId: { in: orderIds } },
    });

    const paymentsByOrderId = payments.reduce(
      (map, payment) => {
        map[payment.orderId] = payment;
        return map;
      },
      {} as { [key: string]: { url: string | null } },
    );

    const data = items.data.map((item) => {
      const status = item.tag === 'trial' ? 'Trial' : item.status;

      return {
        id: item.id,
        price: item.basePrice,
        duration: item.duration,
        status: status,
        invoice: item.invoice,
        name: item.name,
        description: item.description,
        orderAt: item.createdAt,
        checkoutUrl: paymentsByOrderId[item.id]?.url || null,
        property: item.property,
      };
    });
    return Object.assign(items, { data: data });
  }
}
