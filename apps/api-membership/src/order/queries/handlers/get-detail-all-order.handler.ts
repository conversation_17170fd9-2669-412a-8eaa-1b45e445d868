import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { GetDetailAllOrderQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetDetailAllOrderQuery)
export class GetDetailAllOrderHandler
  implements IQueryHandler<GetDetailAllOrderQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetDetailAllOrderQuery) {
    const { id } = query;

    const item = await this.prisma.order.findFirst({
      where: {
        id,
      },
      include: {
        details: { include: { orderPackageDetail: true } },
        property: true,
        payment: true,
      },
    });

    return {
      id: item.id,
      name: item.name,
      description: item.description,
      status: item.status,
      tag: item.tag,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      totalPrice: item.totalPrice,
      discount: item.discount,
      voucherCode: item.voucherCode,
      orderPayment: item.payment
        ? {
            id: item.payment.id,
            by: item.payment.by,
            url: item.payment.url,
            expiredPayment: item.payment.expiredPayment,
            status: item.payment.status,
            paymentRequestId: item.payment.paymentRequestId,
            createdAt: item.payment.createdAt,
            updatedAt: item.payment.updatedAt,
          }
        : null,
      orderDetail: item.details.map((det) => {
        return {
          id: det.id,
          name: det.name,
          duration: det.duration,
          price: det.price,
          totalPrice: det.totalPrice,
          tax: det.tax,
          discount: det.discount,
          itemType: det.itemType,
          itemId: det.itemId,
          qty: det.qty,
          sku: det.sku,
          createdAt: det.createdAt,
          updatedAt: det.updatedAt,
          orderPackageDetail:
            det.orderPackageDetail.length > 0
              ? det.orderPackageDetail.map((opd) => {
                  return {
                    id: opd.id,
                    featureId: opd.featureId,
                    qty: opd.qty,
                  };
                })
              : [],
        };
      }),
      property: item.property,
    };
  }
}
