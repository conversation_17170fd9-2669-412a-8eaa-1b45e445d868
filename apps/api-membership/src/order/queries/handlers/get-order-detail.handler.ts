import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { GetOrderDetailQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetOrderDetailQuery)
export class GetOrderDetailHandler
  implements IQueryHandler<GetOrderDetailQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetOrderDetailQuery) {
    const { id } = query;

    const order = await this.prisma.order.findFirst({
      where: { id },
      include: {
        payment: true,
        details: {
          include: {
            orderPackageDetail: true,
          },
        },
        property: true,
      },
    });
    if (!order) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'Order not found',
      });
    }

    return {
      id: order.id,
      name: order.name || '',
      description: order.description || '',
      status: order.status,
      tag: order.tag || '',
      createdAt: order.createdAt.toISOString(),
      updatedAt: order.updatedAt.toISOString(),
      propertyId: order.propertyId || '',
      totalPrice: Number(order.totalPrice),
      discount: Number(order.discount || 0),
      voucherId: order.voucherCode || '',
      propertyType: order.property?.propertyTypeId || '',
      orderPayment: order.payment
        ? {
            id: order.payment.id,
            by: order.payment.by || '',
            url: order.payment.url || '',
            expiredPayment: Number(order.payment.expiredPayment || 0),
            status: order.payment.status || '',
            paymentRequestId: order.payment.paymentRequestId || '',
            createdAt: order.payment.createdAt.toISOString(),
            updatedAt: order.payment.updatedAt.toISOString(),
            isPaid: order.payment.isPaid,
          }
        : null,
      orderDetail: order.details.map((dt) => ({
        id: dt.id,
        name: dt.name || '',
        duration: dt.duration || '',
        price: dt.price.toString(),
        totalPrice: Number(dt.totalPrice),
        tax: Number(dt.tax || 0),
        discount: Number(dt.discount || 0),
        itemType: dt.itemType || '',
        itemId: dt.itemId || '',
        qty: Number(dt.qty),
        sku: dt.sku || '',
        createdAt: dt.createdAt.toISOString(),
        updatedAt: dt.updatedAt.toISOString(),
        orderPackageDetail: dt.orderPackageDetail.map((opd) => ({
          id: opd.id,
          featureId: opd.featureId || '',
          qty: Number(opd.qty || 0),
        })),
      })),
    };
  }
}
