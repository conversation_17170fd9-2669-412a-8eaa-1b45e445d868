import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetActivePropertyByOrderStatusCounterQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { GetActivePropertyByOrderStatusCounterResponse } from '@app/proto-schema/index.membership';
import { OrderStatus } from '@prisma/client';

@QueryHandler(GetActivePropertyByOrderStatusCounterQuery)
export class GetActivePropertyByOrderStatusCounterHandler
  implements IQueryHandler<GetActivePropertyByOrderStatusCounterQuery>
{
  constructor(private prisma: PrismaService) {}
  async execute(
    query: GetActivePropertyByOrderStatusCounterQuery,
  ): Promise<GetActivePropertyByOrderStatusCounterResponse> {
    const { status } = query;
    status.orderStatus;
    const counters = await this.prisma.property.count({
      where: {
        AND: [
          {
            status: 'active',
          },
          {
            order: {
              some: {
                status: status.orderStatus as unknown as OrderStatus,
              },
            },
          },
        ],
      },
    });
    return { counters };
  }
}
