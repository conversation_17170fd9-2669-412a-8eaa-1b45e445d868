import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { GetOrderQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { Metadata } from '@grpc/grpc-js';
import { PackageService } from '../../../internal-client/services';
import { lastValueFrom } from 'rxjs';

@QueryHandler(GetOrderQuery)
export class GetOrderHandler implements IQueryHandler<GetOrderQuery> {
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private pkgGrpc: PackageService,
  ) {}

  async execute(query: GetOrderQuery) {
    const { id, user } = query;

    const item = await this.prisma.order.findFirst({
      where: {
        AND: [
          {
            property: {
              users: {
                some: {
                  user: {
                    id: user.id,
                  },
                },
              },
            },
          },
          { id: id },
        ],
      },
      include: {
        details: {
          include: {
            orderPackageDetail: true,
          },
          orderBy: { createdAt: 'desc' },
        },
        payment: true,
        property: true,
      },
    });

    if (!item) {
      throw new NotFoundException();
    }

    const payload: any = {
      propertyId: item.property.id,
    };

    const meta = new Metadata();
    const pkg = await lastValueFrom(
      this.pkgGrpc.client.listPackage(
        {
          query: JSON.stringify(payload),
        },
        meta,
      ),
    );

    const propertyType = await lastValueFrom(
      this.propertyTypeGrpc.getOnePropertyType({
        id: item.property.propertyTypeId,
      }),
    );

    const detailsWithTax = item.details.map((detail) => {
      let taxItems;
      if (typeof detail.taxItems === 'string') {
        taxItems = JSON.parse(detail.taxItems);
      } else {
        taxItems = detail.taxItems;
      }

      const pph23Tax = Array.isArray(taxItems)
        ? taxItems.find((tax) => tax.name === 'PPh23')
        : null;
      const taxValue = pph23Tax
        ? Number(detail.price) * detail.qty * (Number(pph23Tax.nominal) / 100)
        : 0;

      if (pph23Tax) {
        pph23Tax.value = taxValue;
      }

      return {
        ...detail,
        taxItems,
      };
    });

    const { propertyId, ...response } = item;
    const totalZone = pkg.data.reduce((acc, packageItem) => {
      const feature = packageItem.packageFeature?.find(
        (feature) => feature.id === 'f-002',
      );
      return acc + (feature ? Number(feature.qty) : 0);
    }, 0);

    const status = item.tag === 'trial' ? 'Trial' : item.status;

    return {
      ...response,
      status: status,
      details: detailsWithTax,
      property: {
        ...response.property,
        totalZone: totalZone,
        industry: propertyType.name,
        packages: pkg.data,
      },
    };
  }
}
