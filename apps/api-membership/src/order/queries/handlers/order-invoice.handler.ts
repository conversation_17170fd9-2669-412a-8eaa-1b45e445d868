import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { OrderInvoiceQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { PostalGrpcService } from '../../../postal/service/postal.grpc.service';
import {
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Metadata } from '@grpc/grpc-js';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigurationGrpcService } from '../../services/configuration.grpc.service';

import puppeteer from 'puppeteer';
import { PackageService } from '../../../internal-client/services';
import { lastValueFrom } from 'rxjs';
import { toDateHuman } from '@app/common';

interface InvoiceResponse {
  buffer: Buffer;
  filename: string;
  contentType: string;
}

@QueryHandler(OrderInvoiceQuery)
export class OrderInvoiceHandler implements IQueryHandler<OrderInvoiceQuery> {
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private postalGrpc: PostalGrpcService,
    private pkgGrpc: PackageService,
    private readonly mailerService: MailerService,
    private readonly configGrpc: ConfigurationGrpcService,
  ) {}

  async execute(Query: OrderInvoiceQuery): Promise<InvoiceResponse> {
    const { id, user } = Query;
    try {
      const item = await this.prisma.order.findFirst({
        where: {
          AND: [
            {
              property: {
                users: {
                  some: {
                    user: {
                      id: user.id,
                    },
                  },
                },
              },
            },
            { id: id },
          ],
        },
        include: {
          details: {
            include: {
              orderPackageDetail: true,
            },
            orderBy: { createdAt: 'desc' },
          },
          payment: true,
          property: true,
        },
      });

      if (!item) {
        throw new NotFoundException('Order not found');
      }

      const meta = new Metadata();
      const packageDetail = item.details.find(
        (detail) => detail.itemType === 'PLAN',
      );

      if (!packageDetail) {
        throw new NotFoundException('Package details not found');
      }

      try {
        const [propertyType, postal, pkg] = await Promise.all([
          lastValueFrom(
            this.propertyTypeGrpc.getOnePropertyType({
              id: item.property.propertyTypeId,
            }),
          ),
          lastValueFrom(
            this.postalGrpc.getPostal({ id: item.property.postalId }, meta),
          ),
          lastValueFrom(
            this.pkgGrpc.client.getOnePackage(
              {
                id: packageDetail.itemId,
                orderId: item.property.id,
              },
              meta,
            ),
          ),
        ]);

        // Process tax items safely
        let taxItems = [];
        try {
          if (typeof packageDetail.taxItems === 'string') {
            taxItems = JSON.parse(packageDetail.taxItems);
          } else if (Array.isArray(packageDetail.taxItems)) {
            taxItems = packageDetail.taxItems;
          }
        } catch (error) {
          console.error('Error parsing tax items:', error);
          taxItems = [];
        }

        const taxes = taxItems.map((tax) => {
          if (tax.name === 'PPh23') {
            return {
              ...tax,
              value:
                Number(packageDetail.price) *
                packageDetail.qty *
                (Number(tax.nominal) / 100),
            };
          }
          return tax;
        });

        const subTotalafter =
          Number(packageDetail?.price) * packageDetail.qty -
          Number(packageDetail.discount || 0);

        const ttlPrice = String(
          Number(subTotalafter) + Number(packageDetail?.tax || 0),
        );

        const signedNameUrl =
          'https://bucket.velodiva.com/membership-storage/static-assets/IMG_3711.webp';
        const footerImageUrl =
          'https://bucket.velodiva.com/membership-storage/static-assets/footer_invoice_1.webp';

        const htmlContent = `
    <html lang="">
    <head>
      <title>${item.invoice}</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
        @media print {
          @page {
            margin: 0;
          }
          * {
            font-family: 'Poppins', sans-serif;
          }
          body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          .footer-image {
            position: fixed;
            bottom: 0;
            left: 0;
            height: auto;
          }
          .keterangan {
            position: fixed;
            bottom: 31%;
            left: 0;
            right: 0;
            height: auto;
            text-align: center;
          }
          .first-uppercase:first-letter {
            text-transform: uppercase;
          }
        }
      </style>
    </head>
    <body>
      <div style="padding: 12px;">
        <div style="max-width: 1000px; background-color: white;">
          <div style="display: flex; align-items: start; gap: 32px;">
            <div>
              <img id="logo" src="https://bucket.velodiva.com/membership-storage/static-assets/velodiva_pink.webp" alt="Velodiva" style="width: 180px;">
            </div>
            <div style="display: flex; flex-direction: column;">
              <span style="font-weight: 800; font-size: 22px;">INFORMASI PAKET BERLANGGANAN VELODIVA</span>
              <span style="font-size: 22px; font-style: italic;">VELODIVA SUBSCRIPTION BILLING STATEMENT</span>
            </div>
          </div>
          <div style="width: 100%; height: 2px; margin-top: 16px; margin-bottom: 16px; background-color: #9ca3af;" />

          <div style="width: 100%; display: grid; grid-template-columns: 30% 70%; align-items: start; padding: 4px;">
            <div style="font-size: 10px;">
              <div style="display: flex; flex-direction: column;">
                <span style="font-weight: 800;">
                  Kepada <i>To</i>
                </span>
                <span style="font-weight: 400; margin-bottom: 6px;">
                </span>
                <span style="font-weight: 400;">
                  ${item.property.companyName}
                </span>
                <span style="font-weight: 400;">
                  ${item.property.address}
                </span>
                <span style="font-weight: 400;">
                  ${postal.city} 
                </span>
                <span style="font-weight: 400;">
                  ${postal.code}
                </span>
              </div>
            </div>
            <table style="width: 100%; font-size: 10px;" cellpadding="0" cellspacing="0">
              <tr>
                <td style="font-weight: 800; padding: 0; width: 30%;; vertical-align: top">
                  Nomor Faktur
                </td>
                <td style="font-style: italic; width: 30%; vertical-align: top">
                  Invoice Number
                </td>
                <td style="font-weight: 800; width: 40%; vertical-align: top">
                  : ${item.invoice}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Tanggal Faktur
                </td>
                <td style="font-style: italic; vertical-align: top">
                  Invoice Date
                </td>
                <td style="font-weight: 800; vertical-align: top">
                  : ${toDateHuman(item.payment?.paidAt)}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  NPWP / NIK
                </td>
                <td style="font-style: italic; vertical-align: top">
                  NPWP / NIK
                </td>
                <td style="font-weight: 800; vertical-align: top">
                  : ${this.formatNumberToFourGroups(item.property.npwp)}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Alamat NPWP
                </td>
                <td style="font-style: italic; vertical-align: top;">
                  NPWP Address
                </td>
                <td style="font-weight: 800; vertical-align: top;">
                  : ${item.property.billingAddress}
                </td>
              </tr>
            </table>
          </div>
          <div style="width: 100%; display: grid; grid-template-columns: 30% 70%; align-items: start; padding: 4px;">
            <div style="font-size: 10px;">

            </div>
            <table style="width: 100%; font-size: 10px;" cellpadding="0" cellspacing="0">
              <tr>
                <td style="font-weight: 800; padding: 0; width: 30%;; vertical-align: top">
                  Email Pelanggan
                </td>
                <td style="font-style: italic; width: 30%; vertical-align: top">
                  Customer Email
                </td>
                <td style="font-weight: 800; width: 40%; vertical-align: top">
                  : ${item.property.companyEmail}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Nomor ID Pelanggan
                </td>
                <td style="font-style: italic; vertical-align: top">
                  Customer ID
                </td>
                <td style="font-weight: 800; vertical-align: top">
                  : ${item.property.cid}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Nama Perusahaan
                </td>
                <td style="font-style: italic; vertical-align: top;">
                  Company Name
                </td>
                <td style="font-weight: 800; vertical-align: top;">
                  : ${item.property.companyName}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Merk Dagang
                </td>
                <td style="font-style: italic; vertical-align: top;">
                  Brand
                </td>
                <td style="font-weight: 800; vertical-align: top;">
                  : ${item.property.brandName}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; padding: 0; vertical-align: top">
                  Industri
                </td>
                <td style="font-style: italic; vertical-align: top;">
                  Industry
                </td>
                <td style="font-weight: 800; vertical-align: top;">
                  : ${propertyType.name}
                </td>
              </tr>
            </table>
          </div>
          <div style="margin-top: 8px;">
            <table style="border: 1px solid black; width: 100%; border-collapse: collapse; font-size: 10px;">
              <tr style="background-color: #666;">
                <td colspan="6" style="text-align: center; color: white; font-weight: 800;">
                  BIAYA BERLANGGANAN /<span style="font-weight: 800; font-style: italic;">SUBSCRIPTION FEE</span>
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;" rowspan="${8 + taxes.length}">
                  <i>Music Player</i>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  Nomor ID Pelanggan
                </td>
                <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                  Customer ID
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;" colspan="3">
                  ${item.property.cid}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  Periode Pembayaran
                </td>
                <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                  Payment Term
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;" colspan="3">
                  ${packageDetail.duration === 'monthly' ? 'Bulanan' : 'Tahunan'}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  Jangka Waktu Paket
                </td>
                <td style="border: 1px solid black; padding: 4px; font-style: italic;">
                  Subscription Term
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  ${toDateHuman(pkg.activeAt, true)}
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;">
                  s/d
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  ${toDateHuman(pkg.expiredAt, true)}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Deskripsi Layanan</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Item Description</span>
                  </div>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Jumlah Item</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Total Item</span>
                  </div>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Harga per Item</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Price per Item</span>
                  </div>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Diskon</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Discount</span>
                  </div>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-weight: 800; text-align: center;">Total</span>
                    <span style="font-weight: 800; text-align: center; font-style: italic;">Total</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td style="border: 1px solid black; padding: 4px;">
                  ${item.name}
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: center;">
                  ${packageDetail.qty}
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(packageDetail.price))}
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(packageDetail.discount))}
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(subTotalafter))}
                </td>
              </tr>
              <tr>
                <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                  SUB TOTAL
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(subTotalafter))}
                </td>
              </tr>
              <tr>
                <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                  DPP Nilai Lain
                </td>
                <td style="border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(subTotalafter) * (11 / 12))}
                </td>
              </tr>
              ${taxes.map(
                (tax) => `
                <tr>
                  <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                    ${tax.name} (${tax.nominal}%)
                  </td>
                  <td style="border: 1px solid black; padding: 4px; text-align: end;">
                    Rp ${this.numberToLocaleStringID(Number(tax.value ?? 0))}
                  </td>
                </tr>
              `,
              )}
              <tr>
                <td colspan="4" style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                  TOTAL PEMBAYARAN <span style="font-style: italic; font-weight: 800;">TOTAL PAYMENT</span>
                </td>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: end;">
                  Rp ${this.numberToLocaleStringID(Number(ttlPrice))}
                </td>
              </tr>
              <tr>
                <td style="font-weight: 800; border: 1px solid black; padding: 4px; text-align: center;">
                  <div style="display: flex; flex-direction: column;">
                    <span>Terbilang</span>
                    <span style="font-style: italic;">In Word</span>
                  </div>
                </td>
                <td colspan="5" style="border: 1px solid black; padding: 4px;">
                  <div style="display: flex; flex-direction: column;">
                    <span class="first-uppercase">${this.numberToWords(Number(ttlPrice), 'id')} rupiah</span>
                    <span class="first-uppercase" style="font-style: italic;">${this.numberToWords(Number(ttlPrice), 'en')} rupiahs</span>
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div style="display: grid; grid-template-columns: 60% 40%; align-items: center; margin-right: 10%;">
            <div>

            </div>
            <div style=";font-size: 10px; display: flex; flex-direction: column; align-items: end;">
              <img src="${signedNameUrl}" alt="Direktur Keuangan" style="max-width: 180px;" />
              <span style="text-align: center; margin-right: 36px;">Direktur Keuangan</span>
            </div>
          </div>
          <div class="keterangan" style="font-size: 10px;">
            Faktur ini merupakan bukti pembayaran lunas yang sah
            <br />
            <i>This invoice serves as an official receipt and proof of full payment.</i>
          </div>
        </div>
      </div>
      <div class="footer-image">
        <img id="footer-image" style="width: 100%;" src="${footerImageUrl}" alt="Footer Image">
      </div>
    </body>
    </html>
  `;

        const year = new Date().getFullYear();

        let bccEmails = [];
        try {
          const bccEmailsResponse = await this.configGrpc.getBccEmails();
          bccEmails = Array.isArray(bccEmailsResponse?.emails)
            ? bccEmailsResponse.emails
            : [];
        } catch (error) {
          console.error('Error fetching BCC emails:', error);
        }

        console.log('[Generating Pdf]...');
        let browser = null;
        let page = null;
        try {
          // Launch browser with optimized settings to reduce resource usage
          browser = await puppeteer.launch({
            args: [
              '--no-sandbox',
              '--disable-setuid-sandbox',
              '--disable-dev-shm-usage',
              '--disable-gpu',
              '--disable-extensions',
              '--disable-audio-output',
              '--single-process',
              '--no-zygote',
              '--disable-accelerated-2d-canvas',
              '--js-flags=--max-old-space-size=512',
              '--disable-web-security',
              '--disable-features=site-per-process',
              '--disable-notifications',
              '--disable-background-timer-throttling',
              '--disable-backgrounding-occluded-windows',
              '--disable-breakpad',
              '--disable-component-extensions-with-background-pages',
              '--disable-ipc-flooding-protection',
              '--memory-pressure-off',
            ],
            executablePath:
              process.env.PUPPETEER_EXECUTABLE_PATH || '/usr/bin/chromium',
            headless: true,
            defaultViewport: { width: 800, height: 1200 },
          });

          page = await browser.newPage();

          // Disable unnecessary resources to save memory
          await page.setRequestInterception(true);
          page.on('request', (req) => {
            const resourceType = req.resourceType();
            // Block unnecessary resources to reduce memory usage, but allow stylesheets, fonts, and images for proper rendering
            if (['media', 'script'].includes(resourceType)) {
              req.abort();
            } else {
              req.continue();
            }
          });

          // Optimize memory usage with aggressive cleanup
          await page.evaluate(() => {
            const style = document.createElement('style');
            style.textContent =
              '* { animation: none !important; transition: none !important; }';
            document.head.appendChild(style);

            // Remove unnecessary DOM elements that won't be visible in PDF
            // Keep all style elements and stylesheets for proper rendering
            const elementsToRemove =
              document.querySelectorAll('script, noscript');
            elementsToRemove.forEach((el) => el.remove());

            // Clear any event listeners, intervals, etc.
            window.addEventListener('beforeunload', () => {
              for (let i = 1; i < 99999; i++) {
                window.clearTimeout(i);
                window.clearInterval(i);
              }

              // Remove event listeners from all elements
              const allElements = document.querySelectorAll('*');
              allElements.forEach((el) => {
                el.replaceWith(el.cloneNode(true));
              });
            });
          });

          // Force garbage collection if possible
          if (global.gc) {
            global.gc();
          }

          // Load content and wait for network to be idle to ensure all resources are loaded
          await page.setContent(htmlContent, {
            waitUntil: ['domcontentloaded', 'networkidle0'],
          });

          // Optimize PDF generation
          const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
              top: '0.4in',
              right: '0.4in',
              bottom: '0.4in',
              left: '0.4in',
            },
            preferCSSPageSize: true,
            omitBackground: false,
          });

          // Check if PDF buffer is empty
          if (!pdfBuffer || pdfBuffer.length === 0) {
            console.error('[PDF creation failed]: Buffer is empty');
            throw new Error('PDF generation failed: Empty buffer');
          }

          console.log(
            '[PDF created successfully] Buffer size:',
            pdfBuffer.length,
          );

          console.log('[PDF generated successfully]');

          return {
            buffer: pdfBuffer,
            filename: `Order-${item.invoice}.pdf`,
            contentType: 'application/pdf',
          };
        } catch (error) {
          console.error('[Puppeteer error]:', error);
          throw error;
        } finally {
          try {
            if (page) {
              await page.close();
              console.log('[Page closed]');
            }
          } catch (closeError) {
            console.error('[Error closing page]:', closeError);
          }

          if (browser) {
            await browser.close();
            console.log('[Browser closed]');

            if (global.gc) {
              global.gc();
            }
          }
        }
      } catch (error) {
        console.error('Error in API calls:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error generating or sending email:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to generate invoice');
    }
  }

  private numberToLocaleStringID(value: number) {
    return value.toLocaleString('id-ID', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
  }

  private formatNumberToFourGroups(numberInput: string): string {
    if (!numberInput) return '';

    try {
      const cleanedNumber = numberInput.replace(/\D/g, '');

      if (cleanedNumber.length !== 15 && cleanedNumber.length !== 16) {
        console.warn(
          `Number length is not suitable for formatting. It should be 15 or 16 digits but got ${cleanedNumber.length}.`,
        );
        return numberInput; // Return original if format doesn't match
      }

      const group1 = cleanedNumber.substring(0, 2);
      const group2 = cleanedNumber.substring(2, 5);
      const group3 = cleanedNumber.substring(5, 8);
      const group4 = cleanedNumber.substring(8, 9);
      const group5 = cleanedNumber.substring(9, 12);
      const group6 = cleanedNumber.substring(12);

      return `${group1}.${group2}.${group3}.${group4}-${group5}.${group6}`;
    } catch (error) {
      console.error('Error formatting number:', error);
      return numberInput; // Return original on error
    }
  }

  private numberToWords(num: number, lang = 'en') {
    if (num === 0) return lang === 'en' ? 'zero' : 'nol';

    const words = {
      en: {
        ones: [
          '',
          'one',
          'two',
          'three',
          'four',
          'five',
          'six',
          'seven',
          'eight',
          'nine',
        ],
        teens: [
          '',
          'eleven',
          'twelve',
          'thirteen',
          'fourteen',
          'fifteen',
          'sixteen',
          'seventeen',
          'eighteen',
          'nineteen',
        ],
        tens: [
          '',
          'ten',
          'twenty',
          'thirty',
          'forty',
          'fifty',
          'sixty',
          'seventy',
          'eighty',
          'ninety',
        ],
        thousands: ['', 'thousand', 'million', 'billion'],
        hundred: 'hundred',
      },
      id: {
        ones: [
          '',
          'satu',
          'dua',
          'tiga',
          'empat',
          'lima',
          'enam',
          'tujuh',
          'delapan',
          'sembilan',
        ],
        teens: [
          '',
          'sebelas',
          'dua belas',
          'tiga belas',
          'empat belas',
          'lima belas',
          'enam belas',
          'tujuh belas',
          'delapan belas',
          'sembilan belas',
        ],
        tens: [
          '',
          'sepuluh',
          'dua puluh',
          'tiga puluh',
          'empat puluh',
          'lima puluh',
          'enam puluh',
          'tujuh puluh',
          'delapan puluh',
          'sembilan puluh',
        ],
        thousands: ['', 'ribu', 'juta', 'miliar'],
        hundred: 'ratus',
      },
    };

    function convertChunk(n, lang) {
      let str = '';
      const w = words[lang];

      if (n >= 100) {
        str += `${w.ones[Math.floor(n / 100)]} ${w.hundred} `;
        n %= 100;
      }

      if (n >= 11 && n <= 19) {
        str += `${w.teens[n - 10]} `;
        return str.trim();
      }

      if (n >= 10 || n === 10) {
        str += `${w.tens[Math.floor(n / 10)]} `;
        n %= 10;
      }

      if (n > 0) {
        str += `${w.ones[n]} `;
      }

      return str.trim();
    }

    let result = '';
    let chunkIndex = 0;

    while (num > 0) {
      const chunk = num % 1000;

      if (chunk !== 0) {
        const chunkWords = convertChunk(chunk, lang);
        result = `${chunkWords + (words[lang].thousands[chunkIndex] ? ` ${words[lang].thousands[chunkIndex]}` : '')} ${result}`;
      }

      num = Math.floor(num / 1000);
      chunkIndex++;
    }

    return result.trim();
  }
}
