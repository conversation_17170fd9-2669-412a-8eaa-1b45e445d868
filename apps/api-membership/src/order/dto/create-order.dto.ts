import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

export enum OrderType {
  PLAN = 'PLAN',
  LICENSE = 'LICENSE',
  ADDON = 'ADDON',
}

export class OderItemDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @ValidateIf((prop) => prop.type != '')
  @IsEnum(OrderType)
  type: OrderType;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateIf((prop) => prop.typeId != '')
  @IsString()
  id: string;

  @ApiProperty()
  @IsOptional()
  @ValidateIf((item) => item.type === OrderType.PLAN)
  @IsNotEmpty()
  quantity: number = 1;
}

export class CreateOrderDto {
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => OderItemDto)
  @ApiProperty({ type: [OderItemDto] })
  @ArrayNotEmpty()
  items: OderItemDto[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  propertyId: string;
}
