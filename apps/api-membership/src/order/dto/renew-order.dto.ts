import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsString } from "class-validator";
import { RenewalType } from "@app/proto-schema/index.membership";

export class RenewOrderDto {
    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    propertyId: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    itemType: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    itemId: string;

    @ApiProperty({ enum: RenewalType })
    @IsEnum(RenewalType)
    @IsNotEmpty()
    renewalType: RenewalType;
}
