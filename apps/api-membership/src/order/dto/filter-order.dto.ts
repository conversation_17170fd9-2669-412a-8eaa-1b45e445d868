import { BaseFilterDto } from '@app/common';
import { ApiProperty, ApiPropertyOptional, OmitType, PartialType } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { IsEnum, IsOptional, IsString, IsDateString } from 'class-validator';

export class FilterOrderDto extends PartialType(BaseFilterDto) {
  @IsEnum($Enums.OrderStatus)
  @ApiProperty({ enum: $Enums.OrderStatus })
  @IsOptional()
  @ApiPropertyOptional()
  status?: $Enums.OrderStatus;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  tag?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  propertyId?: string;

  @ApiPropertyOptional({ description: 'Search by order date (YYYY-MM-DD format)' })
  @IsOptional()
  @IsDateString()
  orderAt?: string;

  @ApiPropertyOptional({ description: 'Search by invoice number' })
  @IsOptional()
  @IsString()
  invoiceNumber?: string;

  @ApiPropertyOptional({ description: 'Search by order name' })
  @IsOptional()
  @IsString()
  orderName?: string;

  @ApiPropertyOptional({ description: 'Search by company name' })
  @IsOptional()
  @IsString()
  companyName?: string;
}
