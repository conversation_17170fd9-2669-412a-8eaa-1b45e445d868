import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { CancelOrderCommand, CreateOrderCommand, SendInvoiceEmailCommand } from './commands';
import { CreateOrderDto } from './dto/create-order.dto';
import { Throttle } from '@nestjs/throttler';
import { FilterOrderDto } from './dto/filter-order.dto';
import {
  GetOrderLicenseQuery,
  GetOrderPackageQuery,
  GetOrderQuery,
  GetOrdersQuery,
  OrderInvoiceQuery,
} from './queries';
import { CancelOrderDto } from './dto/cancel-order.dto';

@Controller('order')
@ApiTags('Order')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class OrderController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Post()
  @Throttle({ default: { limit: 15, ttl: 60000 } })
  create(@User() user: ICurrentUser, @Body() createOrderDto: CreateOrderDto) {
    return this.commandBus.execute(
      new CreateOrderCommand(user, createOrderDto),
    );
  }

  @Get()
  findAll(@User() user: ICurrentUser, @Query() filter: FilterOrderDto) {
    return this.queryBus.execute(new GetOrdersQuery(user, filter));
  }

  @Get(':id')
  cekOrder(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.queryBus.execute(new GetOrderQuery(user, id));
  }

  @Get(':id/license')
  orderLicense(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.queryBus.execute(new GetOrderLicenseQuery(user, id));
  }

  @Get(':id/package')
  orderPackage(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.queryBus.execute(new GetOrderPackageQuery(user, id));
  }

  @Throttle({ default: { limit: 15, ttl: 60000 } })
  @Patch(':id/cancel')
  cancelOrder(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Body() payload: CancelOrderDto,
  ) {
    return this.commandBus.execute(new CancelOrderCommand(user, id, payload));
  }

  @Throttle({ default: { limit: 2, ttl: 60000 } })
  @Get(':id/invoice')
  async orderInvoice(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Res() res,
  ) {
    const result = await this.queryBus.execute(new OrderInvoiceQuery(user, id));

    if (result && result.buffer) {
      try {
        const buffer = Buffer.from(result.buffer, 'base64');

        res.set({
          'Content-Type': result.contentType || 'application/pdf',
          'Content-Disposition': `attachment; filename="${result.filename}"`,
          'Content-Length': buffer.length,
        });

        return res.end(result.buffer);
      } catch (err) {
        console.error('[ERROR] Failed to decode base64 buffer:', err);
        return res
          .status(500)
          .json({ message: 'Failed to process PDF buffer' });
      }
    }

    return res.status(404).json({ message: 'PDF not found' });
  }
}
