import {
  CONFIGURATION_SERVICE_NAME,
  ConfigurationServiceClient,
  GetAppVersionResponse,
  GetBccEmailsResponse,
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { firstValueFrom, Observable } from 'rxjs';

@Injectable()
export class ConfigurationGrpcService {
  private configurationService: ConfigurationServiceClient;

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.configurationService =
      this.client.getService<ConfigurationServiceClient>(
        CONFIGURATION_SERVICE_NAME,
      );
  }

  getBccEmails(): Promise<GetBccEmailsResponse> | GetBccEmailsResponse {
    const meta = new Metadata();
    return firstValueFrom(this.configurationService.getBccEmails({}, meta));
  }

  getAppService(): Promise<GetAppVersionResponse> | GetAppVersionResponse {
    const meta = new Metadata();
    return firstValueFrom(this.configurationService.getAppVersion({}, meta));
  }
}
