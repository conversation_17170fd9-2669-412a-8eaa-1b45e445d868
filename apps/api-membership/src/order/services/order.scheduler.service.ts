import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'nestjs-prisma';
import { Prisma } from '@prisma/client';

@Injectable()
export class OrderSchedulerService {
  private readonly logger = new Logger(OrderSchedulerService.name);

  constructor(private readonly prisma: PrismaService) { }

  @Cron(CronExpression.EVERY_MINUTE)
  async handlerExpiredOrder() {
    try {
      const currentTimestamp = BigInt(Date.now());

      const expiredOrder = await this.prisma.order.findMany({
        where: {
          AND: [
            {
              expiredAt: {
                not: null,
                lt: currentTimestamp,
              },
            },
            {
              status: 'pending'
            }
          ]
        },
      });

      for (const order of expiredOrder) {
        await this.processExpiredOrder(order);
      }

      this.logger.log(`Processed ${expiredOrder.length} expired order`);
    } catch (error) {
      this.logger.error('Error processing expired order', error);
    }
  }

  private async processExpiredOrder(
    order: Prisma.OrderGetPayload<{}>,
  ) {
    try {
      await this.prisma.order.update({
        where: { id: order.id },
        data: {
          status: 'cancelled',
          cancelAt: new Date(),
          cancelReason: 'Order expired',
        },
      });

      this.logger.log(
        `Cancelled order ${order.id} due to expired order`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing expired for order ${order.id}`,
        error,
      );
    }
  }
}
