import {
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { createHash } from 'crypto';
import { PrismaService } from 'nestjs-prisma';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  transports: ['websocket', 'polling'],
  namespace: 'web',
  maxHttpBufferSize: 100 * 1024 * 1024
})
@Injectable()
export class WebGateway implements OnGatewayConnection, OnGatewayDisconnect {
  constructor(
    private jwtService: JwtService,
    private config: ConfigService,
    private prismaService: PrismaService
  ) {}

  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebGateway.name);

  async handleConnection(socket: Socket) {
    try {
      const header = socket.handshake.headers;
      const token =
        (header['Authorization'] as unknown as string) ||
        (header['authorization'] as unknown as string);
      if (!token || token.length == 0 || token == '') {
        return this.disconnect(socket);
      } else {
        if (token.startsWith('Bearer ')) {
          const tokenExtracts = token.substring(7, token.length);
          const userToken = await this.jwtService.verifyAsync(tokenExtracts, {
            secret: this.config.get<string>('AT_SECRET'),
          });
          if (userToken) {
            const user = await this.prismaService.user.findFirst({
              where: {
                id: userToken.sub
              }
            });

            if (!user) {
              return this.disconnect(socket)
            }
            const connectedSocket = await this.server
            .in(`session-${user.id}`)
            .fetchSockets();
      
            if (connectedSocket.length == 0) {
              const ipAddress = socket.handshake.address;
              const userAgent = socket.handshake.headers['user-agent'];
              const hashAt = createHash('sha256').update(tokenExtracts).digest('base64');
              const session = await this.prismaService.userSession.findFirst({
                where: {
                  OR: [
                    {
                      AND: [
                        {
                          isActive: true
                        },
                        {
                          user: {
                            id: user.id
                          }
                        },
                        {
                          media: userAgent
                        },
                        {
                          ip: ipAddress
                        }
                      ] 
                    },
                    {
                      AND: [
                        {
                          isActive: true
                        },
                        {
                          user: {
                            id: user.id
                          }
                        },
                        {
                          hashAt
                        },
                      ] 
                    }
                  ]
                }
              });
              if (!session) {
                return this.disconnect(socket);
              }
              socket.join(user.id);
              socket.join(`session-${user.id}`);
              this.server.to(user.id).emit('session', {
                ip: null,
                userAgent: null,
                status: 'connected'
              });
              this.logger.log(
                `[200] WEB - client connected -> USER ID: ${user?.id}`,
              );
            } else {
              socket.join(`guest-session-${user.id}`)
              this.server.to(`guest-session-${user.id}`).emit('session', {
                ip: null,
                userAgent: null,
                status: 'waiting'
              });
            }
          } else {
            return this.disconnect(socket);
          }
        } else {
          return this.disconnect(socket);
        }
      }
    } catch (error) {
      return this.disconnect(socket);
    }
  }

  async handleDisconnect(socket: Socket) {
    this.logger.log(
      `[200] WEB - client disconnected`,
    );
    socket.disconnect();
  }

  private disconnect(socket: Socket) {
    socket.emit('Error', new UnauthorizedException());
    socket.disconnect();
  }
}
