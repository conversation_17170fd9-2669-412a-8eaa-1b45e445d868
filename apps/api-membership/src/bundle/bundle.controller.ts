import { BadRequestException, Controller, Get, Param, Query } from '@nestjs/common';
import { BundleGrpcService } from './service/bundle.grpc.service';
import { ApiParam, ApiTags, ApiQuery } from '@nestjs/swagger';
import { ListBundleResponse, Bundle, BundleRequest } from '@app/proto-schema/index.internal';
import { Id } from '@app/proto-schema/index.common';
import { FilterBundleDto } from './dto/filter-bundle.dto';
import { QueryBus } from '@nestjs/cqrs';
import { GetBundleQuery, GetBundlesQuery } from './queries/impl';

@ApiTags('Bundle')
@Controller('bundle')
export class BundleController {
  constructor(
    private queryBus: QueryBus,
  ) {}

  @Get()
  @ApiQuery({ name: 'industryId', required: false })
  @ApiQuery({ name: 'subfolderId', required: false })
  async listBundle(
    @Query() params: FilterBundleDto,
  ) {
    return this.queryBus.execute(new GetBundlesQuery(params))
  }

  @Get(':id')
  @ApiParam({ name: 'id', description: 'Bundle ID' })
  async getBundle(
    @Param('id') id: string,
    @Query('propertyId') propertyId?: string,
  ){
    return this.queryBus.execute(new GetBundleQuery(id, propertyId))
  }
}
