import { BadGatewayException, BadRequestException, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetBundleQuery } from '../impl';
import { IntegrationDlpService } from 'apps/api-membership/src/integration/services';
import { BundleGrpcService } from '../../service/bundle.grpc.service';

@QueryHandler(GetBundleQuery)
export class GetBundleHandler implements IQueryHandler<GetBundleQuery> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly integrationService: IntegrationDlpService,
    private readonly bundleGrpcService: BundleGrpcService
  ) {}

  async execute(query: GetBundleQuery) {
    const { id, propertyId } = query;
    let payload = {};
    const property = await this.prisma.property.findFirst({
      where: {
        id: propertyId
      },
      include: {
        configuration: true
      }
    });

    if (!property.configuration.options['customfields']['id']) {
      throw new BadRequestException('property fields mustbe includeded');
    }

    try {
      if (
        property.configuration.options['customfields']['type'] === 'ranges' ||
        property.configuration.options['customfields']['type'] === 'lupsum' ||
        property.configuration.options['customfields']['type'] === 'split'
      ) {
        payload = {
          id: property.configuration.options['customfields']['id'],
          amount: Number(property.configuration.options['customfields']['value']),
        }
      } else {
        throw new BadRequestException('unrecognize custom fields')
      }

      const resp = await this.integrationService.calculateLicense(payload);
    
      const bundle = await this.bundleGrpcService.getBundle({ id }).toPromise();
      bundle.license.price = bundle.license.price + resp.nominal;

      return bundle;
    } catch (err) {
      if (err instanceof NotFoundException) {
        throw err
      } else {
        throw new InternalServerErrorException('Internal Server Error')
      }
    }
  }
}
