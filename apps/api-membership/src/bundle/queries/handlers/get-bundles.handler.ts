import { BadGatewayException, BadRequestException, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { GetBundlesQuery } from '../impl';
import { IntegrationDlpService } from 'apps/api-membership/src/integration/services';
import { BundleGrpcService } from '../../service/bundle.grpc.service';
import { BundleRequest } from '@app/proto-schema/index.internal';

@QueryHandler(GetBundlesQuery)
export class GetBundlesHandler implements IQueryHandler<GetBundlesQuery> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly integrationService: IntegrationDlpService,
    private readonly bundleGrpcService: BundleGrpcService
  ) {}

  async execute(query: GetBundlesQuery) {
    const { filter } = query;
    let payload = {};

    const property = await this.prisma.property.findFirst({
      where: {
        id: filter.propertyId
      },
      include: {
        configuration: true
      }
    });

    if (!property.configuration.options['customfields']['id']) {
      throw new BadRequestException('property fields mustbe includeded');
    }

    try {
      if (
        property.configuration.options['customfields']['type'] === 'ranges' ||
        property.configuration.options['customfields']['type'] === 'lupsum' ||
        property.configuration.options['customfields']['type'] === 'split'
      ) {
        payload = {
          id: property.configuration.options['customfields']['id'],
          amount: Number(property.configuration.options['customfields']['value']),
        }
      } else {
        throw new BadRequestException('unrecognize custom fields')
      }

      const resp = await this.integrationService.calculateLicense(payload);

      const request: BundleRequest = { industryId: filter.industryId, subfolderId: filter.subfolderId }
      const bundles = await this.bundleGrpcService.listBundle(request).toPromise()

      for (let i in bundles.data) {
        bundles.data[i].license.price = bundles.data[i].license.price + resp.nominal;
      }

      return bundles;
    } catch (err) {
      if (err instanceof BadRequestException) {
        throw err
      } else {
        throw new InternalServerErrorException('Internal Server Error')
      }
    }
  }
}
