import { Modu<PERSON> } from "@nestjs/common";
import { ClientsModule } from "@nestjs/microservices";
import { BundleGrpcService } from "./service/bundle.grpc.service";
import { BundleController } from "./bundle.controller";
import { InternalClient } from "libs/clients/internal.client";
import { IntegrationModule } from "../integration/integration.module";
import { CqrsModule } from "@nestjs/cqrs";
import { BundleQueryHandlers } from "./queries";
import { ConfigService } from "@nestjs/config";
import { INTERNAL_PACKAGE } from "libs/clients";

@Module({
  imports: [
    CqrsModule,
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => InternalClient(configService),
        name: INTERNAL_PACKAGE
      }
    ]),
    IntegrationModule
  ],
  controllers: [BundleController],
  providers: [
    ...BundleQueryHandlers,
    BundleGrpcService
  ],
  exports: [BundleGrpcService]
})

export class BundleModule { }
