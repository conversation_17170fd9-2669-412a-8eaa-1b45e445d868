import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString, ValidateIf } from "class-validator";

export class FilterBundleDto {
  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  industryId: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  subfolderId: string;

  @IsString()
  @IsNotEmpty()
  @ValidateIf((prop) => prop.dateOfBirth != '')
  @ApiProperty({ required: true })
  propertyId: string;
}
