import { ListBundleResponse, Bundle, BUNDLE_SERVICE_NAME, BundleRequest, BundleServiceClient } from "@app/proto-schema/index.internal";
import { Id } from "@app/proto-schema/index.common";
import { Metadata } from "@grpc/grpc-js";
import { Inject, Injectable } from "@nestjs/common";
import { ClientGrpc } from "@nestjs/microservices";
import { Observable } from "rxjs";

@Injectable()
export class BundleGrpcService {
  private bundleService: BundleServiceClient

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.bundleService = this.client.getService<BundleServiceClient>(BUNDLE_SERVICE_NAME)
  }

  listBundle(request: BundleRequest): Observable<ListBundleResponse> {
    const meta = new Metadata
    return this.bundleService.listBundle(request, meta)
  }

  getBundle(request: Id): Observable<Bundle> {
    const meta = new Metadata
    return this.bundleService.getBundle(request, meta)
  }
}
