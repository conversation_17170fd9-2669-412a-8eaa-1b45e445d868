import { Empty, Id, QueryMap } from '@app/proto-schema/index.common';
import {
  MONITOR_SERVICE_NAME,
  MonitorServiceController,
  CustomerOverview,
  CustomerSummary,
  CustomerSummaryResponse,
} from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { 
  GrpcGetCustomerOverviewQuery,
  GrpcGetListCustomerQuery,
  GrpcGetDetailCustomerQuery,
} from './queries';

@Controller()
export class MonitorRpcController implements MonitorServiceController {
  constructor(private readonly queryBus: QueryBus) { }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getCustomerOverview')
  getCustomerOverview(
    request: Empty,
    metadata: Metadata,
  ): Promise<CustomerOverview> | Observable<CustomerOverview> | CustomerOverview {
    return this.queryBus.execute(new GrpcGetCustomerOverviewQuery(request));
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getListCustomer')
  getListCustomer(
    request: QueryMap,
    metadata: Metadata,
  ): Promise<CustomerSummaryResponse> | Observable<CustomerSummaryResponse> | CustomerSummaryResponse {
    return this.queryBus.execute(new GrpcGetListCustomerQuery(request));
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getDetailCustomer')
  getDetailCustomer(
    request: Id,
    metadata: Metadata,
  ): Promise<CustomerSummary> | Observable<CustomerSummary> | CustomerSummary {
    return this.queryBus.execute(new GrpcGetDetailCustomerQuery(request));
  }
}
