import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetCustomerOverviewQuery } from '../impl';

@QueryHandler(GrpcGetCustomerOverviewQuery)
export class GrpcGetCustomerOverviewHandler implements IQueryHandler<GrpcGetCustomerOverviewQuery> {
    constructor(
        private prisma: PrismaService,
    ) { }

    async execute(query: GrpcGetCustomerOverviewQuery) {
        try {
            const result = await this.prisma.$queryRaw<any[]>`
                SELECT 
                    COUNT(DISTINCT p.id)::text as "companyCount",
                    COUNT(DISTINCT p."brandName")::text as "brandCount",
                    COUNT(DISTINCT p."propertyTypeId")::text as "industryCount"
                FROM "Property" p
                WHERE p.id IS NOT NULL
            `;

            return {
                companyCount: result[0].companyCount,
                brandCount: result[0].brandCount,
                industryCount: result[0].industryCount
            };

        } catch (error) {
            throw new RpcException(error);
        }
    }
}