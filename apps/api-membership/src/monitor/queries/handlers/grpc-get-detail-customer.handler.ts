import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetDetailCustomerQuery } from '../impl';
import { PropertyGrpcService } from 'apps/api-membership/src/internal-client/property.service';
import { firstValueFrom } from 'rxjs';

@QueryHandler(GrpcGetDetailCustomerQuery)
export class GrpcGetDetailCustomerHandler implements IQueryHandler<GrpcGetDetailCustomerQuery> {
    constructor(
        private prisma: PrismaService,
        private propertyService: PropertyGrpcService
    ) { }

    async execute(query: GrpcGetDetailCustomerQuery) {
        try {
            const { query: id } = query;

            const property = await this.prisma.property.findUnique({
                where: { id: id.id }
            });

            if (!property) {
                throw new RpcException('Property not found');
            }

            const propertyDetails = await firstValueFrom(
                this.propertyService.getPropertyIn({
                    id: [property.id]
                })
            );

            const detail = propertyDetails.data[0];

            return {
                id: property.id,
                cid: property.cid || '',
                companyName: property.companyName || '',
                brand: property.brandName || '',
                industry: detail?.industry || '',
                city: detail?.city || '',
                province: detail?.province || '',
                category: detail?.industry || '',
                packageName: detail?.packageName || '',
                startDate: detail?.startDate || '',
                endDate: detail?.endDate || '',
                status: property.flag || '',
                totalDevice: detail?.totalDevice || ''
            };

        } catch (error) {
            throw new RpcException(error);
        }
    }
}