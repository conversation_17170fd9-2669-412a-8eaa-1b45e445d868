import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'nestjs-prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetListCustomerQuery } from '../impl';
import { convertKeyValuePairs } from '@app/common';
import { Prisma } from '@prisma/client';
import { PropertyGrpcService } from 'apps/api-membership/src/internal-client/property.service';
import { firstValueFrom } from 'rxjs';

@QueryHandler(GrpcGetListCustomerQuery)
export class GrpcGetListCustomerHandler implements IQueryHandler<GrpcGetListCustomerQuery> {
    constructor(
        private prisma: PrismaService,
        private propertyService: PropertyGrpcService
    ) { }

    async execute(query: GrpcGetListCustomerQuery) {
        try {
            const { query: args } = query;
            const params = convertKeyValuePairs(args?.filters || []);
            const page = args?.page || 1;
            const limit = args?.limit || 10;
            const offset = (page - 1) * limit;

            console.log('Query Parameters:', { page, limit, offset, params });

            const searchTerm = params.search?.trim();
            console.log('Search Term:', searchTerm);

            // Base query without search term
            let sql = `
                WITH property_data AS (
                    SELECT 
                        p.id,
                        p.cid,
                        p."companyName",
                        p."brandName",
                        p.flag,
                        p."createdAt",
                        p."updatedAt"
                    FROM "Property" p
                    ${searchTerm 
                        ? `WHERE 
                            p."companyName" ILIKE '%${searchTerm}%' OR
                            p."brandName" ILIKE '%${searchTerm}%' OR
                            p.cid = '${searchTerm}'`
                        : this.buildWhereClause(params)
                    }
                    ORDER BY p."createdAt" DESC
                    LIMIT ${limit} OFFSET ${offset}
                ),
                total_count AS (
                    SELECT COUNT(DISTINCT p.id) as total
                    FROM "Property" p
                    ${searchTerm 
                        ? `WHERE 
                            p."companyName" ILIKE '%${searchTerm}%' OR
                            p."brandName" ILIKE '%${searchTerm}%' OR
                            p.cid = '${searchTerm}'`
                        : this.buildWhereClause(params)
                    }
                )`;

            const commonSelect = `
                SELECT 
                    COALESCE(json_agg(
                        json_build_object(
                            'id', p.id,
                            'cid', p.cid,
                            'companyName', p."companyName",
                            'brand', p."brandName",
                            'status', p.flag,
                            'createdAt', p."createdAt",
                            'updatedAt', p."updatedAt"
                        )
                    ), '[]') as data,
                    (SELECT total FROM total_count) as total_count
                FROM property_data p;
            `;

            const finalSql = sql + commonSelect;
            console.log('Final SQL Query:', finalSql);

            interface QueryResult {
                data: any[];
                total_count: number;
            }

            const result = await this.prisma.$queryRawUnsafe<QueryResult[]>(finalSql);
            console.log('Query Result:', result);

            const { data, total_count } = result[0] || { data: [], total_count: 0n };
            console.log('Extracted Data:', { dataLength: data?.length, total_count });

            // Get additional property details from the property service
            if (data?.length) {
                const propertyDetails = await firstValueFrom(
                    this.propertyService.getPropertyIn({
                        id: data.map(p => p.id)
                    })
                );
                console.log('Property Details:', propertyDetails);

                // Merge the property details with the main data
                data.forEach(property => {
                    const detail = propertyDetails.data.find(d => d.id === property.id);
                    if (detail) {
                        Object.assign(property, {
                            industry: detail.industry || '',
                            city: detail.city || '',
                            province: detail.province || '',
                            category: detail.industry || '',
                            packageName: detail.packageName || '',
                            startDate: detail.startDate || '',
                            endDate: detail.endDate || '',
                            totalDevice: detail.totalDevice || ''
                        });
                    }
                });
            }

            const totalCountNumber = Number(total_count);
            const lastPage = Math.ceil(totalCountNumber / limit);

            return {
                data: data || [],
                meta: {
                    page,
                    limit,
                    lastPage,
                    total: totalCountNumber
                }
            };

        } catch (error) {
            console.error('Error in execute:', error);
            throw new RpcException(error);
        }
    }

    private buildWhereClause(params: any) {
        const conditions = [];
        
        if (params.cid) {
            conditions.push(`p.cid = '${params.cid}'`);
        }

        if (params.companyName) {
            conditions.push(`p."companyName" ILIKE '%${params.companyName}%'`);
        }

        if (params.brandName) {
            conditions.push(`p."brandName" ILIKE '%${params.brandName}%'`);
        }

        if (params.status) {
            conditions.push(`p.flag = '${params.status}'`);
        }

        return conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    }
}