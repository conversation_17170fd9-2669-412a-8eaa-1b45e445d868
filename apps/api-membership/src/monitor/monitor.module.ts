import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { MonitorQueryHandlers } from './queries';
import { MonitorRpcController } from './monitor.rpc.controller';
import { InternalClientModule } from '../internal-client/internal-client.module';

@Module({
  imports: [CqrsModule, InternalClientModule],
  controllers: [MonitorRpcController],
  providers: [...MonitorQueryHandlers],
})
export class MonitorModule {}
