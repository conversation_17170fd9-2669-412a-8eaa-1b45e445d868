import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListPropertyQuery } from '../impl/list-property.query';
import { PrismaService } from 'nestjs-prisma';
import { PropertyGrpcService } from 'apps/api-membership/src/internal-client/property.service';
import { firstValueFrom } from 'rxjs';
import { PackageService } from 'apps/api-membership/src/internal-client/services';
import { Metadata } from '@grpc/grpc-js';

@QueryHandler(ListPropertyQuery)
export class ListPropertyHandler implements IQueryHandler<ListPropertyQuery> {
  constructor(
    private prisma: PrismaService,
    private propertyService: PropertyGrpcService,
    private packageService: PackageService,
  ) {}

  private async checkPropertyPackages(propertyId: string): Promise<{
    hasActivePackage: boolean;
  }> {
    const meta = new Metadata();
    try {
      const response = await this.packageService.client
        .listPackage({ query: JSON.stringify({ propertyId }) }, meta)
        .toPromise();

      const packageList = response.data || [];
      const activePackages = packageList.filter(
        (pkg) => pkg.isActive && pkg.status === 'active',
      );
      return {
        hasActivePackage: activePackages.length > 0,
      };
    } catch (error) {
      return { hasActivePackage: false };
    }
  }

  async execute(query: ListPropertyQuery): Promise<any> {
    const { user, args } = query;
    const search = args.search || '';

    const data = await this.prisma.userProperty.findMany({
      where: {
        AND: [
          { userId: user.id },
          {
            property: {
              NOT: {
                status: 'draft',
              },
            },
          },
          search
            ? {
                property: {
                  OR: [
                    { cid: { contains: search, mode: 'insensitive' } },
                    { brandName: { contains: search, mode: 'insensitive' } },
                    { companyName: { contains: search, mode: 'insensitive' } },
                  ],
                },
              }
            : {},
        ],
      },
      include: {
        property: true,
        user: true,
      },
    });

    const filteredData = [];
    for (const item of data) {
      const { hasActivePackage } = await this.checkPropertyPackages(
        item.property.id,
      );

      if (!hasActivePackage) {
        continue;
      }

      filteredData.push(item);
    }

    const isZoneAllUsed = await firstValueFrom(
      this.propertyService.getIsActivateAllUsed({
        id: filteredData.map(({ property }) => property.id),
      }),
    );

    const isZoneAllUsedMap = new Map(
      isZoneAllUsed.data.map((item) => [item.propertyId, item]),
    );

    const userWithDefaults = await this.prisma.user.findMany({
      where: { parentId: user.id },
      include: {
        property: true,
      },
    });

    const userChild = await this.prisma.userProperty.findMany({
      where: {
        userId: {
          in: userWithDefaults.map(({ id }) => id),
        },
      },
      include: {
        property: true,
      },
    });

    const response = filteredData.map(({ property, isDefault }) => {
      const allUsedData = isZoneAllUsedMap.get(property.id);
      const isFromUserChild = userChild.some(
        (child) => child.propertyId === property.id,
      );

      isDefault =
        user?.businessType === 'Single' || isFromUserChild ? true : false;

      return {
        id: property.id,
        cid: property.cid,
        brandName: property.brandName,
        companyName: property.companyName,
        hasPrimary: isDefault,
        allActivationUsed: allUsedData?.allActivationUsed ?? false,
      };
    });

    return {
      data: response,
    };
  }
}
