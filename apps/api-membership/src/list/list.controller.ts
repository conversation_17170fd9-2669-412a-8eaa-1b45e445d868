import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { QueryBus } from '@nestjs/cqrs';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/types/user.type';
import { ListPropertyDto } from './dto/list-property.dto';
import { ListPropertyQuery } from './queries';

@ApiTags('List')
@ApiBearerAuth()
@UseGuards(JwtGuard)
@Controller('list')
export class ListController {
  constructor(private queryBus: QueryBus) {}

  @Get('property')
  getListProperty(@User() user: ICurrentUser, @Query() args: ListPropertyDto) {
    return this.queryBus.execute(new ListPropertyQuery(user, args));
  }
}
