import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';
import { ClientsModule } from '@nestjs/microservices';
import { INTERNAL_PACKAGE, InternalClient } from 'libs/clients';
import { InternalClientModule } from '../internal-client/internal-client.module';
import { IntegrationModule } from '../integration/integration.module';
import { ListController } from './list.controller';
import { ListQueryHandler } from './queries';
import { PropertyModule } from '../property/property.module';

@Module({
  imports: [
    CqrsModule,
    PropertyModule,
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) =>
          InternalClient(configService),
        name: INTERNAL_PACKAGE,
      },
    ]),
    InternalClientModule,
    IntegrationModule,
  ],
  controllers: [ListController],
  providers: [...ListQueryHandler],
})
export class ListModule {}
