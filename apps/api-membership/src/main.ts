import { ContextInterceptor, errorFormat } from '@app/common';
import {
  BadRequestException,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationError, useContainer } from 'class-validator';
import { join } from 'path';
import { cwd } from 'process';
import { ApiMembershipModule } from './api-membership.module';
import { ReflectionService } from '@grpc/reflection';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unreachable code error
BigInt.prototype.toJSON = function () {
  const int = Number.parseInt(this.toString());
  return int ?? this.toString();
};

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(
    ApiMembershipModule,
    {
      logger: ['error', 'warn'],
    },
  ); 

  const configService = app.get(ConfigService);
  app.useGlobalInterceptors(new ContextInterceptor());
  app.enableCors();

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: false,
      },
      whitelist: true,
      validateCustomDecorators: true,
      forbidUnknownValues: false,
      stopAtFirstError: true,
      exceptionFactory: (errors: ValidationError[]) => {
        const messages = errorFormat(errors);
        return new BadRequestException(messages);
      },
    }),
  );

  app.enableVersioning({
    defaultVersion: '1',
    type: VersioningType.URI,
  });

  const configSwagger = new DocumentBuilder()
    .setTitle('Membership rest api')
    .addBearerAuth()
    .setDescription('The membership api')
    .setVersion('1.0')
    .build();

  const document = SwaggerModule.createDocument(app, configSwagger);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      tagsSorter: 'alpha',
      apisSorter: 'alpha',
      operationsSorter: 'alpha',
      docExpansion: 'none',
      persistAuthorization: true,
    },
  }); 

  useContainer(app.select(ApiMembershipModule), { fallbackOnErrors: true });

  await app.listen(configService.get<string>('APP_MEMBERSHIP_PORT'));

  const grpcUrl = configService.get<string>('GRPC_URL');
  const grpcOptions: MicroserviceOptions = {
    transport: Transport.GRPC,
    options: {
      package: 'membership',
      protoPath: [
        join(cwd(), '_proto/membership-proto/auth.proto'),
        join(cwd(), '_proto/membership-proto/order.proto'),
        join(cwd(), '_proto/membership-proto/property.proto'),
        join(cwd(), '_proto/membership-proto/user.proto'),
        join(cwd(), '_proto/membership-proto/license.proto'),
        join(cwd(), '_proto/membership-proto/monitor.proto'),
      ],
      url: grpcUrl,
      onLoadPackageDefinition: (pkg, server) => {
        new ReflectionService(pkg).addToServer(server);
      },
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
        includeDirs: [join(cwd(), '_proto')],
      },
    },
  };
  app.connectMicroservice(grpcOptions);
  await app.startAllMicroservices();
}
bootstrap();
