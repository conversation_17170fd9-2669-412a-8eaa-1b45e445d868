<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reset Your Password</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f9f9f9;
      margin: 0;
      padding: 0;
      text-align: center;
    }

    .container {
      max-width: 600px;
      margin: 30px auto;
      background: #fff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .logo {
      font-size: 24px;
      font-weight: bold;
      color: #e6007e;
      margin-bottom: 10px;
    }

    .image-container {
      margin-bottom: 20px;
    }

    .content {
      font-size: 18px;
      color: #333;
      margin-bottom: 20px;
    }

    .button {
      display: inline-block;
      background-color: #ff4081;
      color: #fff;
      padding: 12px 24px;
      text-decoration: none;
      font-size: 16px;
      font-weight: bold;
      border-radius: 25px;
      margin-top: 20px;
    }

    .footer {
      font-size: 14px;
      color: #999;
      margin-top: 20px;
    }

    .otp-container {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
      max-width: 256px;
      margin-left: auto;
      margin-right: auto;
      cursor: pointer;
    }

    .otp-box {
      width: 50px;
      height: 50px;
      font-size: 24px;
      text-align: center;
      border: 2px solid #e6007e;
      border-radius: 5px;
      background-color: #fff;
      line-height: 50px;
      font-weight: bold;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="content">
    <h2>Reset Password</h2>
    <div class="image-container">
      <img src="https://dashboard-stg.velodiva.com/mail/reset-password.webp" alt="Reset Password" width="100%">
    </div>
    <p>We’re here to get you back on track.</p>

    <div class="otp-container" onclick="copyOTP()">
      <div class="otp-box">{{codeOtpDigits.[0]}}</div>
      <div class="otp-box">{{codeOtpDigits.[1]}}</div>
      <div class="otp-box">{{codeOtpDigits.[2]}}</div>
      <div class="otp-box">{{codeOtpDigits.[3]}}</div>
    </div>

    <script>
      function copyOTP() {
        const otp = Array.from(document.querySelectorAll('.otp-box'))
          .map(box => box.innerText)
          .join('');
        navigator.clipboard.writeText(otp).then(() => {
          alert('OTP Copied: ' + otp);
        });
      }
    </script>


    <p>If you didn’t request a password reset, please ignore this email.</p>
  </div>
  <div class="footer">
    <p>© {{year}} VELODIVA. All rights reserved.</p>
  </div>
</div>
</body>
</html>
