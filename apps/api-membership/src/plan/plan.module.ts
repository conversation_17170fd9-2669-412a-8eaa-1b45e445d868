import { Modu<PERSON> } from "@nestjs/common";
import { ClientsModule } from "@nestjs/microservices";
import { PlanGrpcService } from "./service/plan.grpc.service";
import { PlanController } from "./plan.controller";
import { InternalClient } from "libs/clients/internal.client";
import { CqrsModule } from "@nestjs/cqrs";
import { PlanQueryHandlers } from "./queries";
import { ConfigService } from "@nestjs/config";
import { INTERNAL_PACKAGE } from "libs/clients";

@Module({
  imports: [
    CqrsModule,
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => InternalClient(configService),
        name: INTERNAL_PACKAGE
      }
    ])
  ],
  controllers: [PlanController],
  providers: [
    ...PlanQueryHandlers,
    PlanGrpcService
  ],
  exports: [PlanGrpcService]
})

export class PlanModule { }
