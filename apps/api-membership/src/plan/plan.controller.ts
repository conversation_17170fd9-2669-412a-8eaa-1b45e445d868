import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { PlanGrpcService } from './service/plan.grpc.service';
import { ApiParam, ApiTags, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { ListPlanResponse, Plan, PlanRequest } from '@app/proto-schema/index.internal';
import { Id } from '@app/proto-schema/index.common';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { QueryBus } from '@nestjs/cqrs';
import { FilterPlanDto } from './dto/filter-plan.dto';
import { GetPlansQuery } from './queries';

@ApiTags('Plan')
@ApiBearerAuth()
@UseGuards(JwtGuard)
@Controller('plan')
export class PlanController {
  constructor(
    private planGrpcService: PlanGrpcService,
    private queryBus: QueryBus,
  ) {}

  @Get()
  async listPlan(
    @User() user: ICurrentUser,
    @Query() filter: FilterPlanDto
  ): Promise<ListPlanResponse> {
    return this.queryBus.execute(new GetPlansQuery(user, filter));
  }

  @Get(':id')
  @ApiParam({ name: 'id', description: 'Plan ID' })
  async detailPlan(
    @Param('id') id: string
  ): Promise<Plan> {
    const requestId: Id = { id }
    return await this.planGrpcService.detailPlan(requestId).toPromise()
  }
}
