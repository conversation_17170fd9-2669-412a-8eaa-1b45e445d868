import { ListPlanResponse, Plan, PLAN_SERVICE_NAME, PlanRequest, PlanServiceClient } from "@app/proto-schema/index.internal";
import { Id } from "@app/proto-schema/index.common";
import { Metadata } from "@grpc/grpc-js";
import { Inject, Injectable } from "@nestjs/common";
import { ClientGrpc } from "@nestjs/microservices";
import { Observable } from "rxjs";

@Injectable()
export class PlanGrpcService {
  private planService: PlanServiceClient

  constructor(@Inject('INTERNAL_PACKAGE') private client: ClientGrpc) {
    this.planService = this.client.getService<PlanServiceClient>(PLAN_SERVICE_NAME)
  }

  listPlan(request: PlanRequest): Observable<ListPlanResponse> {
    const meta = new Metadata
    return this.planService.listPlan(request, meta)
  }

  detailPlan(request: Id): Observable<Plan> {
    const meta = new Metadata
    return this.planService.detailPlan(request, meta)
  }
}