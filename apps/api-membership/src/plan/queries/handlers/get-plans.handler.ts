import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Prisma } from '@prisma/client';
import { GetPlansQuery } from '../impl';
import { PlanGrpcService } from '../../service/plan.grpc.service';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { PlanRequest } from '@app/proto-schema/index.internal';

@QueryHandler(GetPlansQuery)
export class GetPlansHandler implements IQueryHandler<GetPlansQuery> {
  constructor(
    private planGrpcService: PlanGrpcService,
    private prisma: PrismaService
  ) {}

  async execute(query: GetPlansQuery) {
    const { user, args } = query;

    const currentUser = await this.prisma.user.findUnique({
      where: { id: user.id },
      select: { businessType: true }
    });

    const userProperty = await this.prisma.userProperty.findFirst({
      where: {
        AND: [
          {
            user: { id: user.id }
          },
          {
            property: { id: args.propertyId }
          }
        ]
      },
      include: {
        property: {
          include: {
            configuration: true
          }
        }
      }
    });
    if (!userProperty && userProperty?.property) {
      throw new BadRequestException('order cannot be processed')
    }

    if (userProperty?.property.configuration.options['industryPlan']) {
      try {
        const request: PlanRequest = {
          industryId: null,
          subfolderId: userProperty?.property.configuration.options['industryPlan'],
          businessType: currentUser?.businessType // Use businessType from database query
        };
        console.log('Sending plan request:', request);
        return await this.planGrpcService.listPlan(request).toPromise();
      } catch (err) {
        console.error('Error fetching plans:', err);
        throw new InternalServerErrorException('Internal Server Error');
      }
    } else {
      return {
        data: []
      };
    }
  }
}
