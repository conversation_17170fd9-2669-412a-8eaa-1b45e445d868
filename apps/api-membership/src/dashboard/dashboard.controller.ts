import {
  Controller,
  Get,
  InternalServerErrorException,
  NotFoundException,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { DeviceGrpcService } from '../internal-client/device.service';
import { PrismaService } from 'nestjs-prisma';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/types/user.type';
import { GetTotalDeviceRequest } from '@app/proto-schema/index.internal';
import { status } from '@grpc/grpc-js';
import { firstValueFrom } from 'rxjs';

@ApiTags('Dashboard')
@Controller('dashboard')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class DashboardController {
  constructor(
    private deviceService: DeviceGrpcService,
    private readonly prismaService: PrismaService,
  ) {}

  @Get('/total-device-active')
  async totalDeviceActive(@User() user: ICurrentUser) {
    try {
      const properties = await this.prismaService.userProperty.findMany({
        where: { userId: user.id },
        select: { propertyId: true },
      });
      if (!properties) {
        return { total: 0 };
      }

      const propertyIds = properties.map((p) => p.propertyId);
      const request: GetTotalDeviceRequest = {
        Ids: propertyIds,
        isActive: true,
        isOnline: true,
      };

      return await firstValueFrom(this.deviceService.getTotalDevice(request));
    } catch (err) {
      console.log(err);
      if (err.code == status.NOT_FOUND) {
        throw new NotFoundException('device not found');
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }

  @Get('/total-device')
  async totalDevice(@User() user: ICurrentUser) {
    try {
      const properties = await this.prismaService.userProperty.findMany({
        where: { userId: user.id },
        select: { propertyId: true },
      });
      if (!properties) {
        return { total: 0 };
      }

      const propertyIds = properties.map((p) => p.propertyId);
      const requestActive: GetTotalDeviceRequest = {
        Ids: propertyIds,
        isActive: true,
        isOnline: true,
      };

      const requestActiveNotOnline: GetTotalDeviceRequest = {
        Ids: propertyIds,
        isActive: true,
        isOnline: false,
      };

      const isActive = await firstValueFrom(
        this.deviceService.getTotalDevice(requestActive),
      );

      const isActiveNotOnline = await firstValueFrom(
        this.deviceService.getTotalDevice(requestActiveNotOnline),
      );

      const total = isActive.total + isActiveNotOnline.total;
      return { total };
    } catch (err) {
      console.log(err);
      if (err.code == status.NOT_FOUND) {
        throw new NotFoundException('device not found');
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }

  @Get('/total-device-inactive')
  async totalDeviceInactive(@User() user: ICurrentUser) {
    try {
      const properties = await this.prismaService.userProperty.findMany({
        where: { userId: user.id },
        select: { propertyId: true },
      });
      if (!properties) {
        return { total: 0 };
      }

      const propertyIds = properties.map((p) => p.propertyId);
      const request: GetTotalDeviceRequest = {
        Ids: propertyIds,
        isActive: true,
        isOnline: false,
      };

      return await firstValueFrom(this.deviceService.getTotalDevice(request));
    } catch (err) {
      console.log(err);
      if (err.code == status.NOT_FOUND) {
        throw new NotFoundException('device not found');
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }
}
