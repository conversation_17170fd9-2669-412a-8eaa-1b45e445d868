apiVersion: apps/v1
kind: Deployment
metadata:
  name: velodiva-backend-membership
  namespace: production  
spec:
  replicas: 1
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
  selector:
    matchLabels:
      app: velodiva-backend-membership
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:
      labels:
        app: velodiva-backend-membership
    spec:
      containers:
        - name: velodiva-backend-membership
          image: gitlab.vnt.co.id:5050/velodiva-1/backend-crm-membership-velodiva/membership-prod:latest
          imagePullPolicy: IfNotPresent
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: be-api-membership-sc
                  key: DATABASE_URL
          envFrom:
            - configMapRef:
                name: velodiva-backend-membership-configmap
          ports:
            - containerPort: 8080
              protocol: TCP
            - containerPort: 50052
              protocol: TCP
          resources:
            requests:
              cpu: 250m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      imagePullSecrets:
        - name: pull-image-secret
      nodeSelector:
        node-role.kubernetes.io/worker: worker
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
