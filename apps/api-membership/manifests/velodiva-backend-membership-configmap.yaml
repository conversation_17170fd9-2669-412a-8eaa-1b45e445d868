apiVersion: v1
kind: ConfigMap
metadata:
  name: velodiva-backend-membership-configmap
  namespace: production
data:
  APP_MEMBERSHIP_PORT: "8080"
  APP_MEMBERSHIP_SERVER: https://api-membership.velodiva.com
  AT_SECRET: zsb7aPO3dGyNEWemOAXw13M1kNgIuxsW
  CALLBACK_URL: https://api-membership.velodiva.com/v1/auth/callback
  CLIENT_REDIRECT_URL: https://dashboard.velodiva.com
  DEFAULT_IMAGE_AVATAR_URL: https://bucket.velodiva.com/public/default-thumbnail/64x64.svg
  FOLDER_NAME: upload
  GOOGLE_CLIENT_ID: 366185989904-3rnhq742cv8tk8viinbondjjorpbjpkj.apps.googleusercontent.com
  GOOGLE_CLIENT_SECRET: 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
  GRPC_PORT: "50052"
  GRPC_URL: 0.0.0.0:50052
  INTERNAL_GRPC_HOST: velodiva-backend-commercial-service:50051
  MAIL_HOST: smtp.gmail.com
  MAIL_USER: <EMAIL>
  MAIL_PASSWORD: hhinxwjsrcjmcqlv
  MAIL_FROM: <EMAIL>
  MAIL_PORT: "465"
  MAIL_SECURE: "true"
  MINIO_ACCESS_KEY: sw0aTB827hiw36xFkY7E
  MINIO_BUCKET_NAME: membership-storage
  MINIO_ENDPOINT: bucket.velodiva.com
  MINIO_PORT: "443"
  MINIO_SECRET_KEY: xssOVt9jupA35Ygluja82KkKmO3bZdC2yPEjysh3
  MINIO_USE_SSL: "true"
  REDIS: redis://:RgaSEIghTstiAmbE@*************:6379
  REDIS_HOST: *************
  REDIS_PASS: RgaSEIghTstiAmbE
  REDIS_PORT: "6379"
  RMS_CLIENT_ID: gqw3YQ7LbMEqkzp4PhrVikFpEWtTz3bK
  RMS_CLIENT_SECRET: WYood1lJMF3nyhyOZ1FfG7dA83FQV4dq
  RMS_HOST: https://api-integration.royalti.co.id/v1
  RT_SECRET: pa9ERNevGrlshSPbYeQYhcL6U9LJnv8r
  STORAGE_PROVIDER: minio
  DLP_HOST: https://api-dlp-integration.royalti.co.id/v1
  DLP_CLIENT_ID: CLIENT-3d08b7055a40d94d5620a64445bf5769-1742108070038
  DLP_CLIENT_SECRET: 7060404178bb58b7f5c9ff81211c05fc002ed2a9908973204749dc4f175f7222
