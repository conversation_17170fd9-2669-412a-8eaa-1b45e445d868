apiVersion: v1
kind: Service
metadata:
  name: velodiva-backend-membership-service
  namespace: production
spec:
  type: ClusterIP  
  internalTrafficPolicy: Cluster
  ipFamilyPolicy: SingleStack
  ipFamilies:
    - IPv4
  selector:
    app: velodiva-backend-membership
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8080
    - name: grpc
      port: 50052
      protocol: TCP
      targetPort: 50052
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800
