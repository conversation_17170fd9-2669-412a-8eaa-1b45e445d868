syntax = "proto3";

package membership;

import "common-proto/common.proto";

message CustomerOverview {
  string companyCount = 1;
  string brandCount = 2;
  string industryCount = 3;   
}

message CustomerSummary {
  string id = 1;
  string cid = 2;
  string companyName = 3;
  string brand = 4;
  string industry = 5;
  string city = 6;
  string province = 7;
  string category = 8;
  string packageName = 9;
  string startDate = 10;      
  string endDate = 11;       
  string status = 12;        
  string totalDevice = 13;
}

message CustomerSummaryResponse {
    repeated CustomerSummary data = 1;
    common.Meta meta = 2;
}

service MonitorService {
    rpc GetCustomerOverview (common.Empty) returns (CustomerOverview) {}
    rpc GetListCustomer (common.QueryMap) returns (CustomerSummaryResponse) {}
    rpc GetDetailCustomer (common.Id) returns (CustomerSummary) {}
}