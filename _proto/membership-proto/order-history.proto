syntax = "proto3";

package membership;

import "common-proto/common.proto";
import "membership-proto/property.proto";
import "membership-proto/order.proto";

service OrderHistoryService {
  rpc CreateOrderHistory(CreateOrderHistoryRequest) returns (common.Status) {}
}

message OrderHistory {
  string id = 1;
  string orderId = 2;
  string fromPropertyId = 3;
  string toPropertyId = 4;
  string status = 5;
  double totalPrice = 6;
  double discount = 7;
  string tag = 8;
  string event = 9;
  string createdAt = 10;
  string updatedAt = 11;
  Property fromProperty = 12;
  Property toProperty = 13;
  Order order = 14;
  string snapshot = 15;
}

message CreateOrderHistoryRequest {
  string orderId = 1;
  string snapshot = 2;
  string event = 3;
}
