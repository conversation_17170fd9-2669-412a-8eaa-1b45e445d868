syntax = "proto3";

package membership;

service AuthService {
  rpc GetUserById(GetUserByIdRequest) returns (User) {}
  rpc RegisterPartnership(PartnershipRequest) returns (User) {}
}

message Profile {
  string id = 1;
  string firstName = 2;
  string lastName = 3;
  string placeOfBirth = 4;
  string dateOfBirth = 5;
  string address = 6;
  string createdAt = 7;
  string updatedAt = 8;
}

message PartnershipRequest {
  string firstName = 1;
  string lastName = 2;
  string email = 3;
  string mobileNumber = 4;
  string gender = 5;
  string address = 6;
  string roleId = 7;
  string postalId = 8;
  string mediaId = 9;
  string password = 10;
  string passwordConfirmation = 11;
  Partnership partnership = 12;
}

message Partnership {
  string id = 1;
  string partnershipName = 2;
  string popularName = 3;
}


message User {
  string id = 1;
  string email = 2;
  string mobileNumber = 3;
  string createdAt = 4;
  string updatedAt = 5;
  Profile profile = 6;
}

message GetUserByIdRequest {
  string userId = 1;
}