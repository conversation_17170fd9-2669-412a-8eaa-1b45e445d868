syntax = "proto3";

package internal;

import "common-proto/common.proto";

service DeviceService {
  rpc GetDevice(common.Query) returns (Device) {}
  rpc GetDevicesByPropertyIn(common.Query) returns (ListDeviceResponse) {}
  rpc GetTotalDevice(GetTotalDeviceRequest) returns (GetTotalDeviceResponse){}
  rpc GetActivationsByProperty(GetActivationsByPropertyRequest) returns (GetActivationsByPropertyResponse) {}
}

message Connection {
  string privateIP = 1;
  string publicIP = 2;
  string lastConnected = 3;
  string lastDisconnected = 4;
  int64 connectedCount = 5;
  bool isOnline = 6;
}

message Activation {
  string id = 1;
  string activatedAt = 2;
  bool isActive = 3;
  int64 quota = 4;
  string licenseKey = 5;
  string code = 6;
  string deviceId = 7;
}

message Device {
  string id = 1;
  string name = 2;
  string zone = 3;
  string serialNumber = 4;
  string macAddr = 5;
  Config config = 6;
  string type = 7;
  bool isActive = 8;
  Connection connection = 9;
  repeated Activation activation = 10;
  CurrentPlay currentPlay = 11;
  PropDevice property = 12;
  User user = 13;
  string createdAt = 14;
  string updatedAt = 15;
}

message CurrentPlay {
  string id = 1;
  string title = 2;
  string type = 3;
}

message Player {
  string loop = 1;
  string muted = 2;
  string paused = 3;
  string position = 4;
  string shuffle = 5;
  string volume = 6;
}

message User {
  string email = 1;
  string phoneNumber = 2;
  string type = 3;
  string status = 4;
  string provider = 5;
  string businessType = 6;
  string isAdmin = 7;
  string activationId = 8;
}

message Config {
  string id = 1;
  string activationId = 2;
  string allowSettingAccess = 3;
  Player player = 4;
  string license = 5;
}

message PropDevice {
  string id = 1;
  string cid = 2;
  string companyName = 3;
  string brandName = 4;
  string companyEmail = 5;
  string companyPhoneNumber = 6;
  string npwp = 7;
  string status = 8;
  string address = 9;
  string flag = 10;
  string licenseKey = 11;
  string licenseType = 12;
}

message ListDeviceResponse {
  common.Status status = 1;
  common.Meta meta = 2;
  repeated Device data = 3;
}

message GetTotalDeviceRequest{
  repeated string Ids = 1;
  bool isOnline = 2;
  bool isActive = 3;
}

message GetTotalDeviceResponse{
  int32 total = 1;
}

message GetActivationsByPropertyRequest {
  string propertyId = 1;
}

message GetActivationsByPropertyResponse {
  repeated ActivationDetail data = 1;
}

message ActivationDetail {
  string id = 1;
  string code = 2;
  bool isUsed = 3;
}