syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/plan.proto";

service AddonService {
  rpc ListAddon (AddonRequest) returns (ListAddonResponse) {}
  rpc DetailAddon (common.Id) returns (AddonDetail) {}
}

enum TimeType {
  HOURLY = 0;
  DAILY = 1;
  MONTHLY = 2;
  WEEKLY = 3;
  YEARLY = 4;
}

enum AddonServiceType {
  ADDON_ENTERPRISE = 0;
  ADDON_CUSTOM = 1;
}

message AddonDetail {
  string id = 1;
  double price = 2;
  int32 qty = 3;
  string createdAt = 4;
  string updatedAt = 5;
  AddonServiceType type = 6;
  string propertyTypeId = 7;
  string featureId = 8;
  TimeType duration = 9;
  Feature feature = 10;
  repeated Tax taxes = 11;
}

message AddonRequest {
  string subfolderId = 1;
}

message ListAddonResponse {
  common.Status status = 1;
  repeated AddonDetail data = 2;
}
