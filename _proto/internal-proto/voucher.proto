syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/property-type.proto";

service VoucherService {
    rpc DetailVoucherByCode(VoucherCodeRequest) returns (Voucher) {}
    rpc DetailVoucherById(common.Id) returns (Voucher) {}
    rpc CheckVoucherByCode(VoucherCodeRequest) returns (common.Id) {}
    rpc CheckVoucherExist(common.Id) returns (CheckVoucherExistResponse) {}
    rpc UseVoucher(VoucherCodeRequest) returns (UseVoucherResponse) {}
    rpc updateStatusVoucher(UpdateVoucherRequest) returns (common.Status) {}
}

message Voucher {
    string id                 = 1;
    string name               = 2;
    PropertyType propertyType = 3;
    string code               = 4;
    string discountType       = 5;
    double discountValue      = 6;
    string startDate          = 7;
    string endDate            = 8;
    string createdAt          = 9;
    string updatedAt          = 10;
}

message VoucherCodeRequest {
  string code = 1;
  string orderId = 2;
}

message CheckVoucherExistResponse {
  string id     = 1;
  string status = 2;
}

message UpdateVoucherRequest {
  string orderId = 1;
}

message UseVoucherResponse {
  string voucherId = 1;
  string itemId = 2;
  string itemType = 3;
  double totalPriceBefore = 4;
  double discountPrice = 5;
  double totalPriceAfter = 6;
}