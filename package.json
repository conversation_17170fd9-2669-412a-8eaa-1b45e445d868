{"name": "velodiva-crm-be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "clean": "rm -rf dist", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/api-membership/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/api-membership/test/jest-e2e.json"}, "dependencies": {"@grpc/grpc-js": "^1.12.2", "@grpc/proto-loader": "^0.7.13", "@grpc/reflection": "^1.0.4", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.1.1", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^10.4.6", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.6", "@nestjs/cqrs": "^10.2.8", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.6", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.6", "@nestjs/platform-socket.io": "^10.4.15", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^8.0.1", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.2.1", "@nestjs/websockets": "^10.4.15", "@prisma/client": "^5.21.1", "@spotify/web-api-ts-sdk": "^1.2.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bullmq": "^5.53.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "dayjs-plugin-utc": "^0.1.2", "express": "^4.21.2", "html-pdf-node-ts": "^1.0.7", "ioredis": "^5.4.2", "minio": "^8.0.3", "moment": "^2.30.1", "mongoose": "^8.7.3", "nestjs-prisma": "^0.23.0", "nodemailer": "^6.9.16", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "platform-express": "^0.0.13", "puppeteer": "^24.6.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "ts-proto": "^2.2.5", "uuid": "^11.0.2"}, "devDependencies": {"@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.6", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.8.5", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.12.2", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "prettier": "^3.3.3", "prisma": "^5.21.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/common(|/.*)$": "<rootDir>/libs/common/src/$1", "^@app/core(|/.*)$": "<rootDir>/libs/core/src/$1", "^@app/proto-schema(|/.*)$": "<rootDir>/libs/proto-schema/src/$1"}}}