export const Permission = {
  USER_MANAGE: 'USER:<PERSON><PERSON><PERSON>',
  USER_CREATE: 'USER:CREATE',
  USER_READ: 'USER:READ',
  USER_UPDATE: 'USER:UPDATE',
  USER_DELETE: 'USER:DELETE',

  P<PERSON><PERSON>_MANAGE: 'PLAN:MANAGE',
  PLAN_CREATE: 'PLAN:CREATE',
  PLAN_READ: 'PLAN:READ',
  PLAN_UPDATE: 'PLAN:UPDATE',
  PLAN_DELETE: 'PLAN:DELETE',

  PROPERTY_MANAGE: 'PROPERTY:MANAGE',
  PROPERTY_CREATE: 'PROPERTY:CREATE',
  PROPERTY_READ: 'PROPERTY:READ',
  PROPERTY_UPDATE: 'PROPERTY:UPDATE',
  PROPERTY_DELETE: 'PROPERTY:DELETE',

  ROLE_MANAGE: 'ROLE:MANAGE',
  ROLE_CREATE: 'ROLE:CREATE',
  ROLE_READ: 'ROLE:READ',
  ROLE_UPDATE: 'ROLE:UPDATE',
  R<PERSON>E_DELETE: 'ROLE:DELETE',

  SETTING_MANAGE: 'SETTING:MANAGE',
  SETTING_CREATE: 'SETTING:CREATE',
  SETTING_READ: 'SETTING:READ',
  SETTING_UPDATE: 'SETTING:UPDATE',
  SETTING_DELETE: 'SETTING:DELETE',

  DEVICE_MANAGE: 'DEVICE:MANAGE',
  DEVICE_CREATE: 'DEVICE:CREATE',
  DEVICE_READ: 'DEVICE:READ',
  DEVICE_UPDATE: 'DEVICE:UPDATE',
  DEVICE_DELETE: 'DEVICE:DELETE',

  INTEGRATION_MANAGE: 'INTEGRATION:MANAGE',
  INTEGRATION_CREATE: 'INTEGRATION:CREATE',
  INTEGRATION_READ: 'INTEGRATION:READ',
  INTEGRATION_UPDATE: 'INTEGRATION:UPDATE',
  INTEGRATION_DELETE: 'INTEGRATION:DELETE',

  SCHEDULE_MANAGE: 'SCHEDULE:MANAGE',
  SCHEDULE_CREATE: 'SCHEDULE:CREATE',
  SCHEDULE_READ: 'SCHEDULE:READ',
  SCHEDULE_UPDATE: 'SCHEDULE:UPDATE',
  SCHEDULE_DELETE: 'SCHEDULE:DELETE',

  TRACK_MANAGE: 'TRACK:MANAGE',
  TRACK_CREATE: 'TRACK:CREATE',
  TRACK_READ: 'TRACK:READ',
  TRACK_UPDATE: 'TRACK:UPDATE',
  TRACK_DELETE: 'TRACK:DELETE',

  MEMBERSHIP_MANAGE: 'MEMBERSHIP:MANAGE',
  MEMBERSHIP_CREATE: 'MEMBERSHIP:CREATE',
  MEMBERSHIP_READ: 'MEMBERSHIP:READ',
  MEMBERSHIP_UPDATE: 'MEMBERSHIP:UPDATE',
  MEMBERSHIP_DELETE: 'MEMBERSHIP:DELETE',

  PROPERTY_TYPE_MANAGE: 'PROPERTY_TYPE:MANAGE',
  PROPERTY_TYPE_CREATE: 'PROPERTY_TYPE:CREATE',
  PROPERTY_TYPE_READ: 'PROPERTY_TYPE:READ',
  PROPERTY_TYPE_UPDATE: 'PROPERTY_TYPE:UPDATE',
  PROPERTY_TYPE_DELETE: 'PROPERTY_TYPE:DELETE',

  BILLING_MANAGE: 'BILLING:MANAGE',
  BILLING_CREATE: 'BILLING:CREATE',
  BILLING_READ: 'BILLING:READ',
  BILLING_UPDATE: 'BILLING:UPDATE',
  BILLING_DELETE: 'BILLING:DELETE',

  PLAYLIST_MANAGE: 'PLAYLIST:MANAGE',
  PLAYLIST_CREATE: 'PLAYLIST:CREATE',
  PLAYLIST_READ: 'PLAYLIST:READ',
  PLAYLIST_UPDATE: 'PLAYLIST:UPDATE',
  PLAYLIST_DELETE: 'PLAYLIST:DELETE',

  REPORT_MANAGE: 'REPORT:MANAGE',
  REPORT_CREATE: 'REPORT:CREATE',
  REPORT_READ: 'REPORT:READ',
  REPORT_UPDATE: 'REPORT:UPDATE',
  REPORT_DELETE: 'REPORT:DELETE',

  HISTORY_MANAGE: 'HISTORY:MANAGE',
  HISTORY_CREATE: 'HISTORY:CREATE',
  HISTORY_READ: 'HISTORY:READ',
  HISTORY_UPDATE: 'HISTORY:UPDATE',
  HISTORY_DELETE: 'HISTORY:DELETE',

  VOUCHER_MANAGE: 'VOUCHER:MANAGE',
  VOUCHER_CREATE: 'VOUCHER:CREATE',
  VOUCHER_READ: 'VOUCHER:READ',
  VOUCHER_UPDATE: 'VOUCHER:UPDATE',
  VOUCHER_DELETE: 'VOUCHER:DELETE',

  REPOSITORY_MANAGE: 'REPOSITORY:MANAGE',
  REPOSITORY_CREATE: 'REPOSITORY:CREATE',
  REPOSITORY_READ: 'REPOSITORY:READ',
  REPOSITORY_UPDATE: 'REPOSITORY:UPDATE',
  REPOSITORY_DELETE: 'REPOSITORY:DELETE',
};

export const PermissionModule = {
  USER: 'USER',
  PROPERTY: 'PROPERTY',
  PROPERTY_TYPE: 'PROPERTY_TYPE',
  ROLE: 'ROLE',
  PLAN: 'PLAN',
  SETTING: 'SETTING',
  DEVICE: 'DEVICE',
  PLAYLIST: 'PLAYLIST',
  INTEGRATION: 'INTEGRATION',
  SCHEDULE: 'SCHEDULE',
  TRACK: 'TRACK',
  MEMBERSHIP: 'MEMBERSHIP',
  CREATOR: 'CREATOR',
  ORDER: 'ORDER',
  BILLING: 'BILLING',
  REPORT: 'REPORT',
  HISTORY: 'HISTORY',
};
