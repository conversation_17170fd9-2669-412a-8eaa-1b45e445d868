import * as moment from 'moment';

/**
 * Safely formats various date types to human-readable Indonesian format
 * @param input - Date input (string, Date object, number timestamp, etc.)
 * @param fromNumber - Whether to treat string input as numeric timestamp
 * @returns Formatted date string in Indonesian locale or empty string if invalid
 */
export function toDateHuman(input?: string | Date | number | null, fromNumber = false): string {
  if (!input) {
    return '';
  }
  
  try {
    let momentDate;
    
    if (fromNumber) {
      // Handle numeric timestamps
      const numValue = typeof input === 'number' ? input : Number(input);
      if (isNaN(numValue)) {
        return '';
      }
      momentDate = moment(numValue);
    } else if (input instanceof Date) {
      // Handle Date objects
      momentDate = moment(input);
    } else if (typeof input === 'string') {
      // Handle string dates
      const trimmedInput = input.trim();
      if (trimmedInput === '') {
        return '';
      }
      
      // Try to parse as ISO string first
      if (trimmedInput.includes('T') || trimmedInput.includes('Z')) {
        momentDate = moment(trimmedInput);
      } else if (/^\d+$/.test(trimmedInput)) {
        // If it's all digits, treat as timestamp
        const timestamp = Number(trimmedInput);
        // Check if it's a reasonable timestamp (not too small or too large)
        if (timestamp > 1000000000 && timestamp < 9999999999999) {
          momentDate = moment(timestamp);
        } else {
          momentDate = moment(trimmedInput);
        }
      } else {
        // Try to parse as regular date string
        momentDate = moment(trimmedInput);
      }
    } else if (typeof input === 'number') {
      // Handle numeric input directly
      momentDate = moment(input);
    } else {
      // Handle other types by converting to string first
      momentDate = moment(String(input));
    }
    
    // Check if the moment is valid
    if (!momentDate.isValid()) {
      console.warn('Invalid date provided to toDateHuman:', input);
      return '';
    }
    
    return momentDate.locale('id').format('LL');
  } catch (error) {
    console.error('Date formatting error:', error, 'Input:', input);
    return '';
  }
}

/**
 * Safely formats date to ISO string
 * @param input - Date input
 * @returns ISO string or empty string if invalid
 */
export function toISOString(input?: string | Date | number | null): string {
  if (!input) {
    return '';
  }
  
  try {
    const momentDate = moment(input);
    if (!momentDate.isValid()) {
      return '';
    }
    return momentDate.toISOString();
  } catch (error) {
    console.error('ISO date formatting error:', error, 'Input:', input);
    return '';
  }
}

/**
 * Safely formats date to custom format
 * @param input - Date input
 * @param format - Moment.js format string
 * @param locale - Locale (default: 'id')
 * @returns Formatted date string or empty string if invalid
 */
export function formatDate(input?: string | Date | number | null, format = 'LL', locale = 'id'): string {
  if (!input) {
    return '';
  }
  
  try {
    const momentDate = moment(input);
    if (!momentDate.isValid()) {
      return '';
    }
    return momentDate.locale(locale).format(format);
  } catch (error) {
    console.error('Date formatting error:', error, 'Input:', input, 'Format:', format);
    return '';
  }
}
