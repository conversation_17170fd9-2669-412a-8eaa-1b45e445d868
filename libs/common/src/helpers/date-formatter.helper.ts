import * as moment from 'moment';

/**
 * Safely converts various date inputs to a valid Date object
 * @param input - Date input of various types
 * @param fromNumber - Whether to treat string input as numeric timestamp
 * @returns Valid Date object or null if invalid
 */
function safeToDate(input: string | Date | number | null | undefined, fromNumber = false): Date | null {
  if (!input) {
    return null;
  }

  try {
    if (input instanceof Date) {
      // Already a Date object, check if valid
      return isNaN(input.getTime()) ? null : input;
    }

    if (typeof input === 'number') {
      // Handle numeric timestamps
      const date = new Date(input);
      return isNaN(date.getTime()) ? null : date;
    }

    if (typeof input === 'string') {
      const trimmedInput = input.trim();
      if (trimmedInput === '') {
        return null;
      }

      if (fromNumber) {
        // Treat string as numeric timestamp
        const numValue = Number(trimmedInput);
        if (isNaN(numValue)) {
          return null;
        }
        const date = new Date(numValue);
        return isNaN(date.getTime()) ? null : date;
      }

      // Try parsing as date string
      const date = new Date(trimmedInput);
      return isNaN(date.getTime()) ? null : date;
    }

    // Try converting other types to Date
    const date = new Date(input as any);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.error('Error converting to Date:', error, 'Input:', input);
    return null;
  }
}

/**
 * Safely formats various date types to human-readable Indonesian format
 * @param input - Date input (string, Date object, number timestamp, etc.)
 * @param fromNumber - Whether to treat string input as numeric timestamp
 * @returns Formatted date string in Indonesian locale or empty string if invalid
 */
export function toDateHuman(input?: string | Date | number | null, fromNumber = false): string {
  if (!input) {
    return '';
  }

  try {
    // First, safely convert to Date object
    const dateObj = safeToDate(input, fromNumber);
    if (!dateObj) {
      console.warn('Invalid date provided to toDateHuman:', input);
      return '';
    }

    // Convert to ISO string to avoid moment.js deprecation warning
    const isoString = dateObj.toISOString();
    const momentDate = moment(isoString);

    // Check if the moment is valid
    if (!momentDate.isValid()) {
      console.warn('Invalid moment date created from:', input);
      return '';
    }

    return momentDate.locale('id').format('LL');
  } catch (error) {
    console.error('Date formatting error:', error, 'Input:', input);
    return '';
  }
}

/**
 * Safely formats date to ISO string
 * @param input - Date input
 * @returns ISO string or empty string if invalid
 */
export function toISOString(input?: string | Date | number | null): string {
  if (!input) {
    return '';
  }

  try {
    // Use native Date conversion to avoid moment.js issues
    const dateObj = safeToDate(input);
    if (!dateObj) {
      return '';
    }

    return dateObj.toISOString();
  } catch (error) {
    console.error('ISO date formatting error:', error, 'Input:', input);
    return '';
  }
}

/**
 * Safely formats date to custom format
 * @param input - Date input
 * @param format - Moment.js format string
 * @param locale - Locale (default: 'id')
 * @returns Formatted date string or empty string if invalid
 */
export function formatDate(input?: string | Date | number | null, format = 'LL', locale = 'id'): string {
  if (!input) {
    return '';
  }

  try {
    // First, safely convert to Date object
    const dateObj = safeToDate(input);
    if (!dateObj) {
      return '';
    }

    // Convert to ISO string to avoid moment.js deprecation warning
    const isoString = dateObj.toISOString();
    const momentDate = moment(isoString);

    if (!momentDate.isValid()) {
      return '';
    }
    return momentDate.locale(locale).format(format);
  } catch (error) {
    console.error('Date formatting error:', error, 'Input:', input, 'Format:', format);
    return '';
  }
}
