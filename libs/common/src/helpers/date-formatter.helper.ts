import * as moment from 'moment';

/**
 * Safely converts various date inputs to a valid Date object
 * @param input - Date input of various types
 * @param fromNumber - Whether to treat string input as numeric timestamp
 * @returns Valid Date object or null if invalid
 */
function safeToDate(input: string | Date | number | null | undefined, fromNumber = false): Date | null {
  if (!input) {
    return null;
  }

  try {
    if (input instanceof Date) {
      // Already a Date object, check if valid
      return isNaN(input.getTime()) ? null : input;
    }

    if (typeof input === 'number') {
      // Handle numeric timestamps
      const date = new Date(input);
      return isNaN(date.getTime()) ? null : date;
    }

    if (typeof input === 'string') {
      const trimmedInput = input.trim();
      if (trimmedInput === '') {
        return null;
      }

      if (fromNumber) {
        // Treat string as numeric timestamp
        const numValue = Number(trimmedInput);
        if (isNaN(numValue)) {
          return null;
        }
        const date = new Date(numValue);
        return isNaN(date.getTime()) ? null : date;
      }

      // Try parsing as date string
      const date = new Date(trimmedInput);
      return isNaN(date.getTime()) ? null : date;
    }

    // Try converting other types to Date
    const date = new Date(input as any);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.error('Error converting to Date:', error, 'Input:', input);
    return null;
  }
}

/**
 * Safely formats various date types to human-readable Indonesian format
 * @param input - Date input (string, Date object, number timestamp, etc.)
 * @param fromNumber - Whether to treat string input as numeric timestamp
 * @returns Formatted date string in Indonesian locale or empty string if invalid
 */
export function toDateHuman(input?: string | Date | number | null, fromNumber = false): string {
  if (!input) {
    return '';
  }

  try {
    let momentDate;

    if (fromNumber) {
      // Handle numeric timestamps
      const numValue = typeof input === 'number' ? input : Number(input);
      if (isNaN(numValue)) {
        return '';
      }
      momentDate = moment(numValue);
    } else if (input instanceof Date) {
      // Handle Date objects - convert to ISO string first to avoid deprecation warning
      const isoString = input.toISOString();
      momentDate = moment(isoString);
    } else if (typeof input === 'string') {
      // Handle string dates
      const trimmedInput = input.trim();
      if (trimmedInput === '') {
        return '';
      }

      // Try to parse as ISO string first
      if (trimmedInput.includes('T') || trimmedInput.includes('Z')) {
        momentDate = moment(trimmedInput);
      } else if (/^\d+$/.test(trimmedInput)) {
        // If it's all digits, treat as timestamp
        const timestamp = Number(trimmedInput);
        // Check if it's a reasonable timestamp (not too small or too large)
        if (timestamp > 1000000000 && timestamp < 9999999999999) {
          momentDate = moment(timestamp);
        } else {
          // Try parsing as date string with explicit format
          momentDate = moment(trimmedInput, moment.ISO_8601, true);
          if (!momentDate.isValid()) {
            momentDate = moment(trimmedInput);
          }
        }
      } else {
        // Try to parse as ISO format first, then fallback
        momentDate = moment(trimmedInput, moment.ISO_8601, true);
        if (!momentDate.isValid()) {
          // Try common date formats
          const formats = [
            'YYYY-MM-DD',
            'YYYY-MM-DD HH:mm:ss',
            'DD/MM/YYYY',
            'MM/DD/YYYY',
            'DD-MM-YYYY'
          ];

          for (const format of formats) {
            momentDate = moment(trimmedInput, format, true);
            if (momentDate.isValid()) {
              break;
            }
          }

          // Last resort - let moment parse it
          if (!momentDate.isValid()) {
            momentDate = moment(trimmedInput);
          }
        }
      }
    } else if (typeof input === 'number') {
      // Handle numeric input directly
      momentDate = moment(input);
    } else {
      // Handle other types - convert to Date first, then to ISO string
      try {
        const dateObj = new Date(input as any);
        if (!isNaN(dateObj.getTime())) {
          const isoString = dateObj.toISOString();
          momentDate = moment(isoString);
        } else {
          momentDate = moment(String(input));
        }
      } catch {
        momentDate = moment(String(input));
      }
    }

    // Check if the moment is valid
    if (!momentDate.isValid()) {
      console.warn('Invalid date provided to toDateHuman:', input);
      return '';
    }

    return momentDate.locale('id').format('LL');
  } catch (error) {
    console.error('Date formatting error:', error, 'Input:', input);
    return '';
  }
}

/**
 * Safely formats date to ISO string
 * @param input - Date input
 * @returns ISO string or empty string if invalid
 */
export function toISOString(input?: string | Date | number | null): string {
  if (!input) {
    return '';
  }

  try {
    let momentDate;

    if (input instanceof Date) {
      // Handle Date objects - convert to ISO string first
      const isoString = input.toISOString();
      momentDate = moment(isoString);
    } else {
      momentDate = moment(input);
    }

    if (!momentDate.isValid()) {
      return '';
    }
    return momentDate.toISOString();
  } catch (error) {
    console.error('ISO date formatting error:', error, 'Input:', input);
    return '';
  }
}

/**
 * Safely formats date to custom format
 * @param input - Date input
 * @param format - Moment.js format string
 * @param locale - Locale (default: 'id')
 * @returns Formatted date string or empty string if invalid
 */
export function formatDate(input?: string | Date | number | null, format = 'LL', locale = 'id'): string {
  if (!input) {
    return '';
  }

  try {
    let momentDate;

    if (input instanceof Date) {
      // Handle Date objects - convert to ISO string first
      const isoString = input.toISOString();
      momentDate = moment(isoString);
    } else {
      momentDate = moment(input);
    }

    if (!momentDate.isValid()) {
      return '';
    }
    return momentDate.locale(locale).format(format);
  } catch (error) {
    console.error('Date formatting error:', error, 'Input:', input, 'Format:', format);
    return '';
  }
}
