export const dayName = (date: string): string => {
  return new Date(date).toLocaleString('en-US', { weekday: 'long' });
};

export const rangeDate = (startDate: string, endDate: string) => {
  const list: string[] = [];
  const strTgl = new Date(startDate);
  const endTgl = new Date(endDate);
  const strDay = strTgl.getDay();
  const endDay = endTgl.getDay();
  if (strDay == endDay) {
    return [
      strTgl
        .toLocaleDateString('en-US', { weekday: 'long' })
        .toLocaleLowerCase(),
    ];
  } else {
    while (strTgl <= endTgl) {
      const current = strTgl.setDate(strTgl.getDate() + 1);

      const convertDate = new Date(current);
      // const format = `${convertDate.getFullYear()}-${String(convertDate.getMonth()).padStart(2, '0')}-${String(convertDate.getDay()).padStart(2, '0')}`;
      list.push(
        convertDate
          .toLocaleDateString('en-US', { weekday: 'long' })
          .toLocaleLowerCase(),
      );
    }

    return [...new Set(list)];
  }
};
