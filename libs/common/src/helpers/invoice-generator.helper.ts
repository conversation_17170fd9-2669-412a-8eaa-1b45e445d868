import { PrismaService } from 'nestjs-prisma';

// invoice generator
export const invoiceGenerator = async (
  prisma: PrismaService,
  table: string,
  opt: {},
  prifixCode: string,
) => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const lastOrder = (await prisma[table].count(opt)) + 1;
  const formattedOrderNumber = String(lastOrder).padStart(10, '0');

  return `VMT.${year}.${month}.${formattedOrderNumber}`;
};
