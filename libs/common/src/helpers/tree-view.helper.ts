export function TreeView(items: TreeItem[]) {
  const idMapping = items.reduce((acc, el, i) => {
    acc[el.id] = i;
    return acc;
  }, {});
  const root = [];
  items.forEach((el) => {
    if (el.parentId === null) {
      root.push(el);
      return;
    }
    const parentEl = items[idMapping[el.parentId]];
    parentEl.children = [...(parentEl.children || []), el];
  });
  return root;
}

type TreeItem = {
  id: string;
  name: string;
  slug: string;
  icon?: string;
  parentId?: string;
  children?: TreeItem[];
};
