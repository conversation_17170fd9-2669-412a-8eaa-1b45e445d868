// import { $Enums } from '@prisma/client';
// import { BusinessType } from 'apps/api-membership/src/auth/dto/register.dto';
//
// export default function cidGenerator(type: BusinessType, current: string) {
//   const year = new Date().getFullYear();
//   const startNumber = current.padStart(6, '0');
//   if (type == 'Single') {
//     return `S${year}${startNumber}`;
//   }
//   if (type == 'Multiple') {
//     return `M${year}${startNumber}`;
//   }
//
//   return 'CID:';
// }

export default function cidGenerator(codeProperty: string, current: string) {
  const year = new Date().getFullYear();
  const yearSuffix = year.toString().slice(-2);
  const startNumber = current.padStart(6, '0');

  return `CIDV${codeProperty}${yearSuffix}${startNumber}`;
}
