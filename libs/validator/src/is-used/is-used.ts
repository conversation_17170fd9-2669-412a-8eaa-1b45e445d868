import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from 'nestjs-prisma';

@ValidatorConstraint({ async: true })
export class IsUsedConstraint implements ValidatorConstraintInterface {
  constructor(private prisma: PrismaService) {}

  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const { model, field = 'id' } = args.constraints[0];
    const count = await this.prisma[model as string].count({
      where: { [field]: value },
    });
    if (count < 1) {
      return true;
    }
    return false;
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} is already used`;
  }
}

export function IsUsed(
  property: IsUsedOptions,
  validationOptions?: ValidationOptions,
) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsUsed',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: IsUsedConstraint,
    });
  };
}

export type IsUsedOptions = {
  model: string;
  field?: string;
};
