import { ArrayExistConstraint } from './src/array-exist/array-exist';
import { ArrayOnceConstraint } from './src/array-once/array-once';
import { DateFormatConstraint } from './src/date-format/date-format';
import { IsExistConstraint } from './src/is-exist/is-exist';
import { IsPhoneConstraint } from './src/is-phone/is-phone';
import { IsUniqueAdminPropertyConstraint } from './src/is-unique-admin-property';
import { IsUniqueCommercialPropertyConstraint } from './src/is-unique-commercial-property/is-unique-commercial-property';
import { IsUniqueMeConstraint } from './src/is-unique-me/is-unique-me';
// import { IsUniquePlaylistTrackConstraint } from './src/is-unique-track-playlist';
import { IsUniqueConstraint } from './src/is-unique/is-unique';
import { IsUsedConstraint } from './src/is-used/is-used';
import { MatchConstraint } from './src/match/match';
import { RequestIdNotFoundException } from './src/customError/not-found';
import { IsValidEmailConstraint } from './src/is-valid-email/is-valid-email';

export const ValidatorProviders = [
  ArrayExistConstraint,
  ArrayOnceConstraint,
  DateFormatConstraint,
  IsExistConstraint,
  IsUniqueConstraint,
  IsUniqueMeConstraint,
  IsUsedConstraint,
  MatchConstraint,
  IsUniqueCommercialPropertyConstraint,
  IsPhoneConstraint,
  // IsUniquePlaylistTrackConstraint,
  IsUniqueAdminPropertyConstraint,
  RequestIdNotFoundException,
  IsValidEmailConstraint,
];
