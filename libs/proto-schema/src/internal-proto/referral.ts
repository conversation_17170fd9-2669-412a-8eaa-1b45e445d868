// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/referral.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Partnership } from "./partnership";

export interface Referral {
  id?: string | undefined;
  name?: string | undefined;
  partnership?: Partnership | undefined;
  code?: string | undefined;
  discountType?: string | undefined;
  discountValue?: string | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
}

export interface ReferralCodeRequest {
  code?: string | undefined;
  orderId?: string | undefined;
}

export interface UseReferralResponse {
  referralId?: string | undefined;
  itemId?: string | undefined;
  itemType?: string | undefined;
  totalPriceBefore?: number | undefined;
  discountPrice?: number | undefined;
  totalPriceAfter?: number | undefined;
}

export interface UpdateReferralRequest {
  orderId?: string | undefined;
}

export interface ReferralServiceClient {
  useReferral(request: ReferralCodeRequest, metadata: Metadata, ...rest: any): Observable<UseReferralResponse>;

  updateStatusReferral(
    request: UpdateReferralRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<UseReferralResponse>;
}

export interface ReferralServiceController {
  useReferral(
    request: ReferralCodeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<UseReferralResponse> | Observable<UseReferralResponse> | UseReferralResponse;

  updateStatusReferral(
    request: UpdateReferralRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<UseReferralResponse> | Observable<UseReferralResponse> | UseReferralResponse;
}

export function ReferralServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["useReferral", "updateStatusReferral"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("ReferralService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("ReferralService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const REFERRAL_SERVICE_NAME = "ReferralService";
