// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/job-position.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty, Id, Status } from "../common-proto/common";

export interface JobPosition {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  allowToCopyId?: string | undefined;
}

export interface ListJobPositionResponse {
  status?: Status | undefined;
  data?: JobPosition[] | undefined;
}

export interface JobPositionServiceClient {
  listJobPosition(request: Empty, metadata: Metadata, ...rest: any): Observable<ListJobPositionResponse>;

  detailJobPosition(request: Id, metadata: Metadata, ...rest: any): Observable<JobPosition>;
}

export interface JobPositionServiceController {
  listJobPosition(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJobPositionResponse> | Observable<ListJobPositionResponse> | ListJobPositionResponse;

  detailJobPosition(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<JobPosition> | Observable<JobPosition> | JobPosition;
}

export function JobPositionServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["listJobPosition", "detailJobPosition"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("JobPositionService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("JobPositionService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const JOB_POSITION_SERVICE_NAME = "JobPositionService";
