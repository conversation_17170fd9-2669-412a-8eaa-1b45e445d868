// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/auth.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export interface GenerateUserTokenResp {
  accessToken?: string | undefined;
  refreshToken?: string | undefined;
}

export interface GenerateUserTokenReq {
  userId?: string | undefined;
  propertyId?: string | undefined;
  pass?: string | undefined;
  ip?: string | undefined;
  agent?: string | undefined;
}

export interface GenerateUserTokenByDeviceIDReq {
  deviceId?: string | undefined;
  pass?: string | undefined;
  ip?: string | undefined;
  agent?: string | undefined;
}

export interface AuthServiceClient {
  generateUserToken(request: GenerateUserTokenReq, metadata: Metadata, ...rest: any): Observable<GenerateUserTokenResp>;

  generateUserTokenByDeviceId(
    request: GenerateUserTokenByDeviceIDReq,
    metadata: Metadata,
    ...rest: any
  ): Observable<GenerateUserTokenResp>;
}

export interface AuthServiceController {
  generateUserToken(
    request: GenerateUserTokenReq,
    metadata: Metadata,
    ...rest: any
  ): Promise<GenerateUserTokenResp> | Observable<GenerateUserTokenResp> | GenerateUserTokenResp;

  generateUserTokenByDeviceId(
    request: GenerateUserTokenByDeviceIDReq,
    metadata: Metadata,
    ...rest: any
  ): Promise<GenerateUserTokenResp> | Observable<GenerateUserTokenResp> | GenerateUserTokenResp;
}

export function AuthServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["generateUserToken", "generateUserTokenByDeviceId"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("AuthService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("AuthService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const AUTH_SERVICE_NAME = "AuthService";
