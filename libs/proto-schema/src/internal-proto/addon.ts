// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/addon.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";
import { Feature, Tax } from "./plan";

export enum TimeType {
  HOURLY = 0,
  DAILY = 1,
  MONTHLY = 2,
  WEEKLY = 3,
  YEARLY = 4,
  UNRECOGNIZED = -1,
}

export enum AddonServiceType {
  ADDON_ENTERPRISE = 0,
  ADDON_CUSTOM = 1,
  UNRECOGNIZED = -1,
}

export interface AddonDetail {
  id?: string | undefined;
  price?: number | undefined;
  qty?: number | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  type?: AddonServiceType | undefined;
  propertyTypeId?: string | undefined;
  featureId?: string | undefined;
  duration?: TimeType | undefined;
  feature?: Feature | undefined;
  taxes?: Tax[] | undefined;
}

export interface AddonRequest {
  subfolderId?: string | undefined;
}

export interface ListAddonResponse {
  status?: Status | undefined;
  data?: AddonDetail[] | undefined;
}

export interface AddonServiceClient {
  listAddon(request: AddonRequest, metadata: Metadata, ...rest: any): Observable<ListAddonResponse>;

  detailAddon(request: Id, metadata: Metadata, ...rest: any): Observable<AddonDetail>;
}

export interface AddonServiceController {
  listAddon(
    request: AddonRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListAddonResponse> | Observable<ListAddonResponse> | ListAddonResponse;

  detailAddon(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<AddonDetail> | Observable<AddonDetail> | AddonDetail;
}

export function AddonServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["listAddon", "detailAddon"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("AddonService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("AddonService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const ADDON_SERVICE_NAME = "AddonService";
