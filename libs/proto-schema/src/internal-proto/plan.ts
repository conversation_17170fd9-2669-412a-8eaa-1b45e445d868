// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/plan.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";
import { Sku } from "./sku";

export enum PlanType {
  FIX = 0,
  ENTERPRISE = 1,
  CUSTOM = 2,
  UNRECOGNIZED = -1,
}

export interface Plan {
  id?: string | undefined;
  name?: string | undefined;
  basePrice?: number | undefined;
  description?: string | undefined;
  publish?: boolean | undefined;
  duration?: string | undefined;
  type?: PlanType | undefined;
  isActive?: boolean | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  propertyTypeId?: string | undefined;
  subfolderId?: string | undefined;
  features?: Feature[] | undefined;
  taxes?: Tax[] | undefined;
  sku?: Sku | undefined;
}

export interface Feature {
  featureId?: string | undefined;
  qty?: number | undefined;
  feature?: FeatureDetail | undefined;
}

export interface FeatureDetail {
  id?: string | undefined;
  name?: string | undefined;
}

export interface Tax {
  id?: string | undefined;
  name?: string | undefined;
  nominal?: number | undefined;
  description?: string | undefined;
  type?: string | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
}

export interface Addon {
  feature?: string | undefined;
  value?: number | undefined;
  valueType?: string | undefined;
  order?: number | undefined;
  price?: number | undefined;
}

export interface PlanRequest {
  industryId?: string | undefined;
  subfolderId?: string | undefined;
  businessType?: string | undefined;
}

export interface PlanList {
  plans?: Plan[] | undefined;
}

export interface ListPlanResponse {
  status?: Status | undefined;
  data?: Plan[] | undefined;
}

export interface PlanServiceClient {
  listPlan(request: PlanRequest, metadata: Metadata, ...rest: any): Observable<ListPlanResponse>;

  detailPlan(request: Id, metadata: Metadata, ...rest: any): Observable<Plan>;
}

export interface PlanServiceController {
  listPlan(
    request: PlanRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListPlanResponse> | Observable<ListPlanResponse> | ListPlanResponse;

  detailPlan(request: Id, metadata: Metadata, ...rest: any): Promise<Plan> | Observable<Plan> | Plan;
}

export function PlanServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["listPlan", "detailPlan"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("PlanService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("PlanService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const PLAN_SERVICE_NAME = "PlanService";
