// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/user.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";

export interface CreateUserRequest {
  username?: string | undefined;
  email?: string | undefined;
  mobileNumber?: string | undefined;
  password?: string | undefined;
  businessType?: string | undefined;
  isAdmin?: boolean | undefined;
  firstName?: string | undefined;
  lastName?: string | undefined;
  placeOfBirth?: string | undefined;
  dateOfBirth?: string | undefined;
  gender?: string | undefined;
  address?: string | undefined;
  activationId?: string | undefined;
  propertyId?: string | undefined;
  zoneName?: string | undefined;
}

export interface UpdateUserRequest {
  userId?: string | undefined;
  firstName?: string | undefined;
  lastName?: string | undefined;
  email?: string | undefined;
  mobileNumber?: string | undefined;
  gender?: string | undefined;
  dateOfBirth?: string | undefined;
  address?: string | undefined;
  activationId?: string | undefined;
  zoneName?: string | undefined;
}

export interface UpdateUserPasswordRequest {
  userId?: string | undefined;
  password?: string | undefined;
}

export interface GetUsersByPropertyRequest {
  propertyId?: string[] | undefined;
}

export interface GetUsersByPropertyResponse {
  users?: UserInfo[] | undefined;
}

export interface UserInfo {
  id?: string | undefined;
  username?: string | undefined;
  email?: string | undefined;
  mobileNumber?: string | undefined;
  businessType?: string | undefined;
  isAdmin?: boolean | undefined;
  profile?: Profile | undefined;
  isActive?: boolean | undefined;
  status?: string | undefined;
  activationId?: string | undefined;
  zoneName?: string | undefined;
  password?: string | undefined;
}

export interface Profile {
  firstName?: string | undefined;
  lastName?: string | undefined;
  placeOfBirth?: string | undefined;
  dateOfBirth?: string | undefined;
  gender?: string | undefined;
  address?: string | undefined;
}

export interface GetUserByIdResponse {
  user?: UserInfo | undefined;
  status?: Status | undefined;
}

export interface UserServiceClient {
  createUser(request: CreateUserRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  updateUser(request: UpdateUserRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  updateUserPassword(request: UpdateUserPasswordRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  deleteUser(request: Id, metadata: Metadata, ...rest: any): Observable<Status>;

  getUsersByPropertyId(
    request: GetUsersByPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetUsersByPropertyResponse>;

  getUserById(request: Id, metadata: Metadata, ...rest: any): Observable<GetUserByIdResponse>;
}

export interface UserServiceController {
  createUser(
    request: CreateUserRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  updateUser(
    request: UpdateUserRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  updateUserPassword(
    request: UpdateUserPasswordRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  deleteUser(request: Id, metadata: Metadata, ...rest: any): Promise<Status> | Observable<Status> | Status;

  getUsersByPropertyId(
    request: GetUsersByPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetUsersByPropertyResponse> | Observable<GetUsersByPropertyResponse> | GetUsersByPropertyResponse;

  getUserById(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetUserByIdResponse> | Observable<GetUserByIdResponse> | GetUserByIdResponse;
}

export function UserServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createUser",
      "updateUser",
      "updateUserPassword",
      "deleteUser",
      "getUsersByPropertyId",
      "getUserById",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("UserService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("UserService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const USER_SERVICE_NAME = "UserService";
