// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/package.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Meta, Query, Status } from "../common-proto/common";
import { Property } from "../membership-proto/property";
import { Feature } from "./plan";

export interface FeatureSmall {
  id?: string | undefined;
  name?: string | undefined;
  valueType?: string | undefined;
  qty?: number | undefined;
  quota?: number | undefined;
}

export interface RequestOnePackage {
  id?: string | undefined;
  orderId?: string | undefined;
}

export interface PackageFeature {
  id?: string | undefined;
  feature?: Feature | undefined;
  price?: number | undefined;
  qty?: number | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface Package {
  id?: string | undefined;
  name?: string | undefined;
  status?: string | undefined;
  itemType?: string | undefined;
  itemId?: string | undefined;
  isActive?: boolean | undefined;
  activeAt?: number | undefined;
  expiredAt?: number | undefined;
  contractEndAt?: number | undefined;
  trialEndAt?: number | undefined;
  isTrial?: boolean | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  propertyId?: string | undefined;
  billingCycle?: string | undefined;
  packageFeature?: FeatureSmall[] | undefined;
}

export interface ListPackageResponse {
  status?: Status | undefined;
  meta?: Meta | undefined;
  data?: Package[] | undefined;
}

export interface GetTotalActivePackageRequest {
  Ids?: string[] | undefined;
}

export interface GetTotalActivePackageResponse {
  total?: number | undefined;
}

export interface CreateTemporaryLicenseRequest {
  id?: string | undefined;
  code?: string | undefined;
  unit?: number | undefined;
  requestId?: string | undefined;
}

export interface PropertyWithPackages {
  property?: Property | undefined;
  packages?: Package[] | undefined;
}

export interface SetPackageLicenseTemporaryExpirationRequest {
  propertyId?: string | undefined;
  expiredAt?: number | undefined;
  licenseType?: string | undefined;
}

export interface ListExpiredPackageResponse {
  data?: PropertyWithPackages[] | undefined;
}

export interface PackageServiceClient {
  detailPackage(request: Query, metadata: Metadata, ...rest: any): Observable<Package>;

  listPackage(request: Query, metadata: Metadata, ...rest: any): Observable<ListPackageResponse>;

  getTotalActivePackage(
    request: GetTotalActivePackageRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetTotalActivePackageResponse>;

  listExpiredPackage(
    request: GetTotalActivePackageRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<ListExpiredPackageResponse>;

  getOnePackage(request: RequestOnePackage, metadata: Metadata, ...rest: any): Observable<Package>;

  createTemporaryLicensePackage(
    request: CreateTemporaryLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<Status>;

  createConventionalLicensePackage(
    request: CreateTemporaryLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<Status>;

  setPackageLicenseTemporaryExpiration(
    request: SetPackageLicenseTemporaryExpirationRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<Status>;
}

export interface PackageServiceController {
  detailPackage(request: Query, metadata: Metadata, ...rest: any): Promise<Package> | Observable<Package> | Package;

  listPackage(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListPackageResponse> | Observable<ListPackageResponse> | ListPackageResponse;

  getTotalActivePackage(
    request: GetTotalActivePackageRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetTotalActivePackageResponse> | Observable<GetTotalActivePackageResponse> | GetTotalActivePackageResponse;

  listExpiredPackage(
    request: GetTotalActivePackageRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListExpiredPackageResponse> | Observable<ListExpiredPackageResponse> | ListExpiredPackageResponse;

  getOnePackage(
    request: RequestOnePackage,
    metadata: Metadata,
    ...rest: any
  ): Promise<Package> | Observable<Package> | Package;

  createTemporaryLicensePackage(
    request: CreateTemporaryLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  createConventionalLicensePackage(
    request: CreateTemporaryLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  setPackageLicenseTemporaryExpiration(
    request: SetPackageLicenseTemporaryExpirationRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;
}

export function PackageServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "detailPackage",
      "listPackage",
      "getTotalActivePackage",
      "listExpiredPackage",
      "getOnePackage",
      "createTemporaryLicensePackage",
      "createConventionalLicensePackage",
      "setPackageLicenseTemporaryExpiration",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("PackageService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("PackageService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const PACKAGE_SERVICE_NAME = "PackageService";
