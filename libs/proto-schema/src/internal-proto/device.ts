// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/device.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Meta, Query, Status } from "../common-proto/common";

export interface Connection {
  privateIP?: string | undefined;
  publicIP?: string | undefined;
  lastConnected?: string | undefined;
  lastDisconnected?: string | undefined;
  connectedCount?: number | undefined;
  isOnline?: boolean | undefined;
}

export interface Activation {
  id?: string | undefined;
  activatedAt?: string | undefined;
  isActive?: boolean | undefined;
  quota?: number | undefined;
  licenseKey?: string | undefined;
  code?: string | undefined;
  deviceId?: string | undefined;
}

export interface Device {
  id?: string | undefined;
  name?: string | undefined;
  zone?: string | undefined;
  serialNumber?: string | undefined;
  macAddr?: string | undefined;
  config?: Config | undefined;
  type?: string | undefined;
  isActive?: boolean | undefined;
  connection?: Connection | undefined;
  activation?: Activation[] | undefined;
  currentPlay?: CurrentPlay | undefined;
  property?: PropDevice | undefined;
  user?: User | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface CurrentPlay {
  id?: string | undefined;
  title?: string | undefined;
  type?: string | undefined;
}

export interface Player {
  loop?: string | undefined;
  muted?: string | undefined;
  paused?: string | undefined;
  position?: string | undefined;
  shuffle?: string | undefined;
  volume?: string | undefined;
}

export interface User {
  email?: string | undefined;
  phoneNumber?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  provider?: string | undefined;
  businessType?: string | undefined;
  isAdmin?: string | undefined;
  activationId?: string | undefined;
}

export interface Config {
  id?: string | undefined;
  activationId?: string | undefined;
  allowSettingAccess?: string | undefined;
  player?: Player | undefined;
  license?: string | undefined;
}

export interface PropDevice {
  id?: string | undefined;
  cid?: string | undefined;
  companyName?: string | undefined;
  brandName?: string | undefined;
  companyEmail?: string | undefined;
  companyPhoneNumber?: string | undefined;
  npwp?: string | undefined;
  status?: string | undefined;
  address?: string | undefined;
  flag?: string | undefined;
  licenseKey?: string | undefined;
  licenseType?: string | undefined;
}

export interface ListDeviceResponse {
  status?: Status | undefined;
  meta?: Meta | undefined;
  data?: Device[] | undefined;
}

export interface GetTotalDeviceRequest {
  Ids?: string[] | undefined;
  isOnline?: boolean | undefined;
  isActive?: boolean | undefined;
}

export interface GetTotalDeviceResponse {
  total?: number | undefined;
}

export interface GetActivationsByPropertyRequest {
  propertyId?: string | undefined;
}

export interface GetActivationsByPropertyResponse {
  data?: ActivationDetail[] | undefined;
}

export interface ActivationDetail {
  id?: string | undefined;
  code?: string | undefined;
  isUsed?: boolean | undefined;
}

export interface DeviceServiceClient {
  getDevice(request: Query, metadata: Metadata, ...rest: any): Observable<Device>;

  getDevicesByPropertyIn(request: Query, metadata: Metadata, ...rest: any): Observable<ListDeviceResponse>;

  getTotalDevice(request: GetTotalDeviceRequest, metadata: Metadata, ...rest: any): Observable<GetTotalDeviceResponse>;

  getActivationsByProperty(
    request: GetActivationsByPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetActivationsByPropertyResponse>;
}

export interface DeviceServiceController {
  getDevice(request: Query, metadata: Metadata, ...rest: any): Promise<Device> | Observable<Device> | Device;

  getDevicesByPropertyIn(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListDeviceResponse> | Observable<ListDeviceResponse> | ListDeviceResponse;

  getTotalDevice(
    request: GetTotalDeviceRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetTotalDeviceResponse> | Observable<GetTotalDeviceResponse> | GetTotalDeviceResponse;

  getActivationsByProperty(
    request: GetActivationsByPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<GetActivationsByPropertyResponse>
    | Observable<GetActivationsByPropertyResponse>
    | GetActivationsByPropertyResponse;
}

export function DeviceServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getDevice", "getDevicesByPropertyIn", "getTotalDevice", "getActivationsByProperty"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("DeviceService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("DeviceService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const DEVICE_SERVICE_NAME = "DeviceService";
