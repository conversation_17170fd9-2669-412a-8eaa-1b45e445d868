// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/bundle.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";
import { License } from "./license";
import { Plan } from "./plan";
import { PropertyType } from "./property-type";

export interface Bundle {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  plan?: Plan | undefined;
  license?: License | undefined;
  propertyType?: PropertyType | undefined;
  subfolder?: PropertyType | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface BundleRequest {
  industryId?: string | undefined;
  subfolderId?: string | undefined;
}

export interface ListBundleResponse {
  status?: Status | undefined;
  data?: Bundle[] | undefined;
}

export interface BundleServiceClient {
  listBundle(request: BundleRequest, metadata: Metadata, ...rest: any): Observable<ListBundleResponse>;

  getBundle(request: Id, metadata: Metadata, ...rest: any): Observable<Bundle>;
}

export interface BundleServiceController {
  listBundle(
    request: BundleRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListBundleResponse> | Observable<ListBundleResponse> | ListBundleResponse;

  getBundle(request: Id, metadata: Metadata, ...rest: any): Promise<Bundle> | Observable<Bundle> | Bundle;
}

export function BundleServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["listBundle", "getBundle"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("BundleService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("BundleService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const BUNDLE_SERVICE_NAME = "BundleService";
