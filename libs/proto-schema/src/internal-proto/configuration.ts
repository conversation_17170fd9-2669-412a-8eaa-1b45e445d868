// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/configuration.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty } from "../common-proto/common";

export interface GetBccEmailsResponse {
  emails?: string[] | undefined;
}

export interface GetAppVersionResponse {
  isForce?: boolean | undefined;
  version?: string | undefined;
}

export interface ConfigurationServiceClient {
  getBccEmails(request: Empty, metadata: Metadata, ...rest: any): Observable<GetBccEmailsResponse>;

  getAppVersion(request: Empty, metadata: Metadata, ...rest: any): Observable<GetAppVersionResponse>;
}

export interface ConfigurationServiceController {
  getBccEmails(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetBccEmailsResponse> | Observable<GetBccEmailsResponse> | GetBccEmailsResponse;

  getAppVersion(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetAppVersionResponse> | Observable<GetAppVersionResponse> | GetAppVersionResponse;
}

export function ConfigurationServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getBccEmails", "getAppVersion"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("ConfigurationService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("ConfigurationService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const CONFIGURATION_SERVICE_NAME = "ConfigurationService";
