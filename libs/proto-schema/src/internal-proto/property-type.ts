// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/property-type.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Meta, Query } from "../common-proto/common";

export interface PropertyType {
  id?: string | undefined;
  name?: string | undefined;
  slug?: string | undefined;
  icon?: string | undefined;
  categoryCode?: string | undefined;
  codeProperty?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  parentId?: string | undefined;
  type?: string | undefined;
  description?: string | undefined;
  isHidden?: boolean | undefined;
  visibility?: boolean | undefined;
  mediaId?: string | undefined;
  hasChild?: boolean | undefined;
  rangeLabel?: string | undefined;
  unitLabel?: string | undefined;
  sequence?: number | undefined;
  children?: PropertyType[] | undefined;
}

export interface ListPropertyTypesResponse {
  data?: PropertyType[] | undefined;
  meta?: Meta | undefined;
}

export interface GetPropertyTypeInRequest {
  id?: string[] | undefined;
}

export interface GetPropertySubRequest {
  id?: string | undefined;
  businessType?: string | undefined;
}

export interface GetPropertyTypeInResponse {
  data?: PropertyType[] | undefined;
}

export interface GetPropertySubResponse {
  data?: PropertyType[] | undefined;
}

export interface GetPropertySubLicenseRequest {
  id?: string | undefined;
  code?: string | undefined;
}

export interface PropertyTypeServiceClient {
  listPropertyTypes(request: Query, metadata: Metadata, ...rest: any): Observable<ListPropertyTypesResponse>;

  getPropertyType(request: Id, metadata: Metadata, ...rest: any): Observable<PropertyType>;

  getPropertyTypeIn(
    request: GetPropertyTypeInRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetPropertyTypeInResponse>;

  getPropertySub(request: GetPropertySubRequest, metadata: Metadata, ...rest: any): Observable<GetPropertySubResponse>;

  getPropertySubLicense(
    request: GetPropertySubLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetPropertySubResponse>;
}

export interface PropertyTypeServiceController {
  listPropertyTypes(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListPropertyTypesResponse> | Observable<ListPropertyTypesResponse> | ListPropertyTypesResponse;

  getPropertyType(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<PropertyType> | Observable<PropertyType> | PropertyType;

  getPropertyTypeIn(
    request: GetPropertyTypeInRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPropertyTypeInResponse> | Observable<GetPropertyTypeInResponse> | GetPropertyTypeInResponse;

  getPropertySub(
    request: GetPropertySubRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPropertySubResponse> | Observable<GetPropertySubResponse> | GetPropertySubResponse;

  getPropertySubLicense(
    request: GetPropertySubLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPropertySubResponse> | Observable<GetPropertySubResponse> | GetPropertySubResponse;
}

export function PropertyTypeServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "listPropertyTypes",
      "getPropertyType",
      "getPropertyTypeIn",
      "getPropertySub",
      "getPropertySubLicense",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("PropertyTypeService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("PropertyTypeService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const PROPERTY_TYPE_SERVICE_NAME = "PropertyTypeService";
