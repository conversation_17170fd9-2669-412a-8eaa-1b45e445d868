// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: internal-proto/voucher.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";
import { PropertyType } from "./property-type";

export interface Voucher {
  id?: string | undefined;
  name?: string | undefined;
  propertyType?: PropertyType | undefined;
  code?: string | undefined;
  discountType?: string | undefined;
  discountValue?: number | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface VoucherCodeRequest {
  code?: string | undefined;
  orderId?: string | undefined;
}

export interface CheckVoucherExistResponse {
  id?: string | undefined;
  status?: string | undefined;
}

export interface UpdateVoucherRequest {
  orderId?: string | undefined;
}

export interface UseVoucherResponse {
  voucherId?: string | undefined;
  itemId?: string | undefined;
  itemType?: string | undefined;
  totalPriceBefore?: number | undefined;
  discountPrice?: number | undefined;
  totalPriceAfter?: number | undefined;
}

export interface VoucherServiceClient {
  detailVoucherByCode(request: VoucherCodeRequest, metadata: Metadata, ...rest: any): Observable<Voucher>;

  detailVoucherById(request: Id, metadata: Metadata, ...rest: any): Observable<Voucher>;

  checkVoucherByCode(request: VoucherCodeRequest, metadata: Metadata, ...rest: any): Observable<Id>;

  checkVoucherExist(request: Id, metadata: Metadata, ...rest: any): Observable<CheckVoucherExistResponse>;

  useVoucher(request: VoucherCodeRequest, metadata: Metadata, ...rest: any): Observable<UseVoucherResponse>;

  updateStatusVoucher(request: UpdateVoucherRequest, metadata: Metadata, ...rest: any): Observable<Status>;
}

export interface VoucherServiceController {
  detailVoucherByCode(
    request: VoucherCodeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Voucher> | Observable<Voucher> | Voucher;

  detailVoucherById(request: Id, metadata: Metadata, ...rest: any): Promise<Voucher> | Observable<Voucher> | Voucher;

  checkVoucherByCode(request: VoucherCodeRequest, metadata: Metadata, ...rest: any): Promise<Id> | Observable<Id> | Id;

  checkVoucherExist(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<CheckVoucherExistResponse> | Observable<CheckVoucherExistResponse> | CheckVoucherExistResponse;

  useVoucher(
    request: VoucherCodeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<UseVoucherResponse> | Observable<UseVoucherResponse> | UseVoucherResponse;

  updateStatusVoucher(
    request: UpdateVoucherRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;
}

export function VoucherServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "detailVoucherByCode",
      "detailVoucherById",
      "checkVoucherByCode",
      "checkVoucherExist",
      "useVoucher",
      "updateStatusVoucher",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("VoucherService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("VoucherService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const VOUCHER_SERVICE_NAME = "VoucherService";
