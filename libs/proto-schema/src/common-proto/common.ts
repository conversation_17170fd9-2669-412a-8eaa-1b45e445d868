// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: common-proto/common.proto

/* eslint-disable */

export interface Id {
  id?: string | undefined;
}

export interface KeyValuePair {
  key?: string | undefined;
  value?: string | undefined;
}

export interface QueryMap {
  page?: number | undefined;
  limit?: number | undefined;
  filters?: KeyValuePair[] | undefined;
}

export interface Query {
  query?: string | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
  params?: { [key: string]: string } | undefined;
}

export interface Query_ParamsEntry {
  key: string;
  value: string;
}

export interface Status {
  code?: number | undefined;
  message?: string | undefined;
}

export interface Meta {
  total?: number | undefined;
  lastPage?: number | undefined;
  currentPage?: number | undefined;
  limit?: number | undefined;
  prev?: number | undefined;
  next?: number | undefined;
}

export interface MetaCursor {
  lastCursor?: string | undefined;
  hasNextPage?: boolean | undefined;
}

export interface Empty {
}
