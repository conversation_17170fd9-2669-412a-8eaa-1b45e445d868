// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: membership-proto/auth.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export interface Profile {
  id?: string | undefined;
  firstName?: string | undefined;
  lastName?: string | undefined;
  placeOfBirth?: string | undefined;
  dateOfBirth?: string | undefined;
  address?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface PartnershipRequest {
  firstName?: string | undefined;
  lastName?: string | undefined;
  email?: string | undefined;
  mobileNumber?: string | undefined;
  gender?: string | undefined;
  address?: string | undefined;
  roleId?: string | undefined;
  postalId?: string | undefined;
  mediaId?: string | undefined;
  password?: string | undefined;
  passwordConfirmation?: string | undefined;
  partnership?: Partnership | undefined;
}

export interface Partnership {
  id?: string | undefined;
  partnershipName?: string | undefined;
  popularName?: string | undefined;
}

export interface User {
  id?: string | undefined;
  email?: string | undefined;
  mobileNumber?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  profile?: Profile | undefined;
}

export interface GetUserByIdRequest {
  userId?: string | undefined;
}

export interface AuthServiceClient {
  getUserById(request: GetUserByIdRequest, metadata: Metadata, ...rest: any): Observable<User>;

  registerPartnership(request: PartnershipRequest, metadata: Metadata, ...rest: any): Observable<User>;
}

export interface AuthServiceController {
  getUserById(request: GetUserByIdRequest, metadata: Metadata, ...rest: any): Promise<User> | Observable<User> | User;

  registerPartnership(
    request: PartnershipRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<User> | Observable<User> | User;
}

export function AuthServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getUserById", "registerPartnership"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("AuthService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("AuthService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const AUTH_SERVICE_NAME = "AuthService";
