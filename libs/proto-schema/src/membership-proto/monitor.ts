// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: membership-proto/monitor.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty, Id, Meta, QueryMap } from "../common-proto/common";

export interface CustomerOverview {
  companyCount?: string | undefined;
  brandCount?: string | undefined;
  industryCount?: string | undefined;
}

export interface CustomerSummary {
  id?: string | undefined;
  cid?: string | undefined;
  companyName?: string | undefined;
  brand?: string | undefined;
  industry?: string | undefined;
  city?: string | undefined;
  province?: string | undefined;
  category?: string | undefined;
  packageName?: string | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
  status?: string | undefined;
  totalDevice?: string | undefined;
}

export interface CustomerSummaryResponse {
  data?: CustomerSummary[] | undefined;
  meta?: Meta | undefined;
}

export interface MonitorServiceClient {
  getCustomerOverview(request: Empty, metadata: Metadata, ...rest: any): Observable<CustomerOverview>;

  getListCustomer(request: QueryMap, metadata: Metadata, ...rest: any): Observable<CustomerSummaryResponse>;

  getDetailCustomer(request: Id, metadata: Metadata, ...rest: any): Observable<CustomerSummary>;
}

export interface MonitorServiceController {
  getCustomerOverview(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<CustomerOverview> | Observable<CustomerOverview> | CustomerOverview;

  getListCustomer(
    request: QueryMap,
    metadata: Metadata,
    ...rest: any
  ): Promise<CustomerSummaryResponse> | Observable<CustomerSummaryResponse> | CustomerSummaryResponse;

  getDetailCustomer(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<CustomerSummary> | Observable<CustomerSummary> | CustomerSummary;
}

export function MonitorServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getCustomerOverview", "getListCustomer", "getDetailCustomer"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("MonitorService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("MonitorService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const MONITOR_SERVICE_NAME = "MonitorService";
