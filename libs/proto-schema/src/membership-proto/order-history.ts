// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: membership-proto/order-history.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Status } from "../common-proto/common";
import { Order } from "./order";
import { Property } from "./property";

export interface OrderHistory {
  id?: string | undefined;
  orderId?: string | undefined;
  fromPropertyId?: string | undefined;
  toPropertyId?: string | undefined;
  status?: string | undefined;
  totalPrice?: number | undefined;
  discount?: number | undefined;
  tag?: string | undefined;
  event?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  fromProperty?: Property | undefined;
  toProperty?: Property | undefined;
  order?: Order | undefined;
  snapshot?: string | undefined;
}

export interface CreateOrderHistoryRequest {
  orderId?: string | undefined;
  snapshot?: string | undefined;
  event?: string | undefined;
}

export interface OrderHistoryServiceClient {
  createOrderHistory(request: CreateOrderHistoryRequest, metadata: Metadata, ...rest: any): Observable<Status>;
}

export interface OrderHistoryServiceController {
  createOrderHistory(
    request: CreateOrderHistoryRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;
}

export function OrderHistoryServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["createOrderHistory"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("OrderHistoryService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("OrderHistoryService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const ORDER_HISTORY_SERVICE_NAME = "OrderHistoryService";
