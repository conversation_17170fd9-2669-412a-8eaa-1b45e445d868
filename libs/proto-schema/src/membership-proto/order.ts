// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.5
//   protoc               v5.27.3
// source: membership-proto/order.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Meta, Query, Status } from "../common-proto/common";
import { PropertyWithType } from "./property";

export enum RenewalType {
  SUBSCRIPTION = 0,
  CONTRACT = 1,
  UNRECOGNIZED = -1,
}

export enum OrderStatus {
  pending = 0,
  completed = 1,
  cancelled = 2,
  UNRECOGNIZED = -1,
}

export interface OrderPayment {
  id?: string | undefined;
  by?: string | undefined;
  url?: string | undefined;
  expiredPayment?: number | undefined;
  status?: string | undefined;
  paymentRequestId?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  isPaid?: boolean | undefined;
}

export interface OrderPackageDetail {
  id?: string | undefined;
  featureId?: string | undefined;
  qty?: number | undefined;
}

export interface OrderDetail {
  id?: string | undefined;
  name?: string | undefined;
  duration?: string | undefined;
  price?: string | undefined;
  totalPrice?: number | undefined;
  tax?: number | undefined;
  discount?: number | undefined;
  itemType?: string | undefined;
  itemId?: string | undefined;
  qty?: number | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  sku?: string | undefined;
  orderPackageDetail?: OrderPackageDetail[] | undefined;
}

export interface Order {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  status?: string | undefined;
  tag?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  propertyId?: string | undefined;
  totalPrice?: number | undefined;
  discount?: number | undefined;
  voucherId?: string | undefined;
  orderPayment?: OrderPayment | undefined;
  orderDetail?: OrderDetail[] | undefined;
  propertyType?: string | undefined;
  userId?: string | undefined;
}

export interface Orders {
  order?: Order[] | undefined;
}

export interface OrderTrial {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  status?: string | undefined;
  tag?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  propertyId?: string | undefined;
  totalPrice?: number | undefined;
  discount?: number | undefined;
  voucherId?: string | undefined;
  orderPayment?: OrderPayment | undefined;
  details?: OrderDetail[] | undefined;
  propertyType?: string | undefined;
}

export interface CheckPaymentRequest {
  paymentRequestId?: string | undefined;
}

export interface PaidOrderRequest {
  orderId?: string | undefined;
  paymentRequestId?: string | undefined;
  paymentMethod?: string | undefined;
  paidAt?: string | undefined;
  paymentId?: string | undefined;
}

export interface UpdateWaitingOrderRequest {
  id?: string | undefined;
  isPaid?: boolean | undefined;
  paymentRequestId?: string | undefined;
  url?: string | undefined;
  expired?: string | undefined;
}

export interface TrialOrderRequest {
  propertyId?: string | undefined;
  itemType?: string | undefined;
  itemId?: string | undefined;
}

export interface GetAllOrderResponse {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  status?: string | undefined;
  tag?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  totalPrice?: number | undefined;
  discount?: number | undefined;
  voucherId?: string | undefined;
  orderPayment?: OrderPayment | undefined;
  orderDetail?: OrderDetail[] | undefined;
  property?: PropertyWithType | undefined;
  industry?: string | undefined;
  package?: Package | undefined;
}

export interface GetListAllOrderResponse {
  status?: Status | undefined;
  meta?: Meta | undefined;
  data?: GetAllOrderResponse[] | undefined;
}

export interface GetOrderDetailByPropertyRequest {
  propertyId?: string | undefined;
}

export interface GetActivePropertyByOrderStatusCounterRequest {
  orderStatus?: OrderStatus | undefined;
}

export interface GetActivePropertyByOrderStatusCounterResponse {
  counters?: number | undefined;
}

export interface Package {
  id?: string | undefined;
  name?: string | undefined;
  status?: string | undefined;
  itemType?: string | undefined;
  itemId?: string | undefined;
  isActive?: boolean | undefined;
  activeAt?: string | undefined;
  expiredAt?: string | undefined;
  contractEndAt?: string | undefined;
  trialEndAt?: string | undefined;
  isTrial?: boolean | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  propertyId?: string | undefined;
}

export interface OrderServiceClient {
  checkPayment(request: CheckPaymentRequest, metadata: Metadata, ...rest: any): Observable<Order>;

  getOrderDetailByProperty(
    request: GetOrderDetailByPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<Orders>;

  getOrderDetail(request: Id, metadata: Metadata, ...rest: any): Observable<Order>;

  paidOrder(request: PaidOrderRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  updateWaitingOrder(request: UpdateWaitingOrderRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  getListAllOrder(request: Query, metadata: Metadata, ...rest: any): Observable<GetListAllOrderResponse>;

  getDetailAllOrder(request: Id, metadata: Metadata, ...rest: any): Observable<GetAllOrderResponse>;

  trialOrder(request: TrialOrderRequest, metadata: Metadata, ...rest: any): Observable<OrderTrial>;

  getActivePropertyByOrderStatusCounter(
    request: GetActivePropertyByOrderStatusCounterRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetActivePropertyByOrderStatusCounterResponse>;
}

export interface OrderServiceController {
  checkPayment(
    request: CheckPaymentRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Order> | Observable<Order> | Order;

  getOrderDetailByProperty(
    request: GetOrderDetailByPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Orders> | Observable<Orders> | Orders;

  getOrderDetail(request: Id, metadata: Metadata, ...rest: any): Promise<Order> | Observable<Order> | Order;

  paidOrder(request: PaidOrderRequest, metadata: Metadata, ...rest: any): Promise<Status> | Observable<Status> | Status;

  updateWaitingOrder(
    request: UpdateWaitingOrderRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  getListAllOrder(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetListAllOrderResponse> | Observable<GetListAllOrderResponse> | GetListAllOrderResponse;

  getDetailAllOrder(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetAllOrderResponse> | Observable<GetAllOrderResponse> | GetAllOrderResponse;

  trialOrder(
    request: TrialOrderRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<OrderTrial> | Observable<OrderTrial> | OrderTrial;

  getActivePropertyByOrderStatusCounter(
    request: GetActivePropertyByOrderStatusCounterRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<GetActivePropertyByOrderStatusCounterResponse>
    | Observable<GetActivePropertyByOrderStatusCounterResponse>
    | GetActivePropertyByOrderStatusCounterResponse;
}

export function OrderServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "checkPayment",
      "getOrderDetailByProperty",
      "getOrderDetail",
      "paidOrder",
      "updateWaitingOrder",
      "getListAllOrder",
      "getDetailAllOrder",
      "trialOrder",
      "getActivePropertyByOrderStatusCounter",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("OrderService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("OrderService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const ORDER_SERVICE_NAME = "OrderService";
